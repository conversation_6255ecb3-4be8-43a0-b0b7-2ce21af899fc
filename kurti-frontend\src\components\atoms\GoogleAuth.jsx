import GoogleIcon from "@mui/icons-material/Google";
import { <PERSON><PERSON> } from "@mui/material";
import { auth, googleProvider } from "config/firebaseConfig";
import { signInWithPopup } from "firebase/auth";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import useCartStore from "stores/cart";
import { setUserInfo } from "stores/user";
import useWishlistStore from "stores/wishlist";
import { useUpdateCartMutation } from "api/cart.api";
import { useUpdateWishlistMutation } from "api/wishlist.api";
import { useCheckUserDisabledMutation, useSignupMutation } from "api";
import { useState } from "react";
import axios from "axios";
import { useGetProfileMutation } from "api/user.api";

const GoogleAuth = ({ type = "signin" }) => {
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const redirectUrl = searchParams.get("redirect") || "/";
  const navigate = useNavigate();
  const cartItems = useCartStore((state) => state.cartItems);
  const wishlistItems = useWishlistStore((state) => state.wishlistItems);
  const { mutateAsync: updateCart } = useUpdateCartMutation();
  const { mutateAsync: updateWishlist } = useUpdateWishlistMutation();
  const { mutateAsync: signup } = useSignupMutation();
  const { mutateAsync: checkUserDisabled } = useCheckUserDisabledMutation();

  const syncCartAndWishlist = async (user, backendToken = null) => {
    try {
      // Use backend token if available, otherwise get Firebase token
      let token = backendToken;
      if (!token) {
        token = await user.getIdToken();

        // Try to exchange for backend token
        const exchangedToken = await exchangeToken(token);
        if (exchangedToken) {
          token = exchangedToken;
        }
      }

      console.log("Syncing cart/wishlist with token:", token ? token.substring(0, 15) + "..." : "none");

      // Sync cart items
      const updatePromises = cartItems.map((item) =>
        updateCart({
          data: {
            productId: item?._id,
            quantity: item?.quantity,
            selectedSize: item?.selectedSize,
            selectedColor: item?.selectedColor,
          },
          token: token,
        })
      );

      // Sync wishlist items
      const wishlistPromises = wishlistItems.map((item) =>
        updateWishlist({
          data: {
            productId: item?._id,
          },
          token: token,
        })
      );

      await Promise.all(updatePromises.concat(wishlistPromises));
    } catch (error) {
      console.error("Error syncing cart/wishlist:", error);
      // Continue with login even if sync fails
    }
  };

  const registerWithBackend = async (userData) => {
    try {
      const payload = {
        email: userData?.email,
        fcmId: userData?.uid,
        isSocial: true,
      };

      const result = await signup(payload);

      if (!result?.success && result?.message !== "User already exists") {
        toast.error(result?.message || "Failed to register with backend");
        return false;
      }

      return true;
    } catch (error) {
      console.error("Backend registration error:", error);
      return false;
    }
  };

  const exchangeToken = async (firebaseToken) => {
    try {
      console.log("Exchanging Firebase token for backend token");
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/auth/exchange-token`,
        { firebaseToken },
        { headers: { 'Content-Type': 'application/json' } }
      );

      if (response?.data?.success && response?.data?.data?.token) {
        console.log("Token exchange successful");
        return response.data.data.token;
      } else {
        console.error("Token exchange failed:", response.data);
        return null;
      }
    } catch (error) {
      console.error("Token exchange error:", error);
      return null;
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);

    try {
      // Step 1: Sign in with Google
      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;

      const checkUserDisabledResult = await checkUserDisabled({
        email: user.email,
      });

      if (
        checkUserDisabledResult?.success &&
        checkUserDisabledResult?.data?.isSuspended
      ) {
        toast.error("Account is disabled. Please contact the administrator.");
        return;
      }

      if (!user) {
        toast.error("Failed to authenticate with Google");
        setIsLoading(false);
        return;
      }

      // Step 2: Get the Firebase ID token
      const firebaseToken = await user.getIdToken();

      // Step 3: Register with backend
      const registrationSuccess = await registerWithBackend(user);

      if (!registrationSuccess) {
        setIsLoading(false);
        return;
      }

      // Step 4: Exchange Firebase token for backend token
      const backendToken = await exchangeToken(firebaseToken);

      // Step 5: Set user info in store with the appropriate token
      const userInfo = {
        token: backendToken || firebaseToken, // Use backend token if available, otherwise fallback to Firebase token
        user: {
          _id: user.uid,
          email: user.email,
          displayName: user.displayName || user.email.split('@')[0],
          photoURL: user.photoURL,
          emailVerified: user.emailVerified
        }
      };

      setUserInfo(userInfo);

      // Log the token to verify it's being set correctly
      console.log("Auth token set:", backendToken ? "Backend token" : "Firebase token (fallback)");

      // Step 6: Sync cart and wishlist
      await syncCartAndWishlist(user, backendToken);

      // Step 7: Show success message
      toast.success(`Successfully ${type === "signup" ? "signed up" : "signed in"} with Google`);

      // Step 8: Navigate to redirect URL
      navigate(redirectUrl);
    } catch (error) {
      console.error("Google Sign-In Error:", error);
      toast.error(error.message || "Google Sign-In failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      type="button"
      className="btn btn-google"
      variant="contained"
      startIcon={<GoogleIcon />}
      onClick={handleGoogleSignIn}
      disabled={isLoading}
      style={{
        backgroundColor: "#4285F4", // Google blue color
        color: "white",
        textTransform: "none",
        fontWeight: "bold",
        padding: "10px 20px",
        borderRadius: "5px",
        opacity: isLoading ? 0.7 : 1,
      }}
    >
      {isLoading
        ? "Processing..."
        : `${type === "signup" ? "Sign up" : "Sign in"} with Google`}
    </Button>
  );
};

export default GoogleAuth;
