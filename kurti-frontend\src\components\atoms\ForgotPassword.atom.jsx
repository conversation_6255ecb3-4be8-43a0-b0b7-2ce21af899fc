import React, { useState } from 'react';
import { Modal, Button, Form } from 'react-bootstrap';
import { Formik, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useResetPassword } from 'api/auth.api';
import { toast } from 'react-toastify';

const forgotPasswordSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
});

const ForgotPasswordAtom = ({ show, handleClose }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: resetPassword } = useResetPassword();

  const handleSubmit = async (values, { resetForm }) => {
    setIsSubmitting(true);
    try {
      await resetPassword(values.email);
      toast.success('Password reset email sent. Please check your inbox.');
      resetForm();
      handleClose();
    } catch (error) {
      toast.error(error.message || 'Failed to send password reset email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Reset Password</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>Enter your email address and we'll send you a link to reset your password.</p>
        <Formik
          initialValues={{ email: '' }}
          validationSchema={forgotPasswordSchema}
          onSubmit={handleSubmit}
        >
          {({ handleSubmit }) => (
            <Form onSubmit={handleSubmit}>
              <Form.Group className="mb-3">
                <Form.Label>Email address</Form.Label>
                <Field
                  type="email"
                  name="email"
                  className="form-control"
                  placeholder="Enter your email"
                />
                <ErrorMessage
                  name="email"
                  component="div"
                  className="text-danger mt-1"
                />
              </Form.Group>
              <div className="d-grid gap-2">
                <Button 
                  variant="primary" 
                  type="submit" 
                  disabled={isSubmitting}
                  style={{
                    backgroundColor: "#6c5ebc",
                    borderColor: "#6c5ebc"
                  }}
                >
                  {isSubmitting ? 'Sending...' : 'Send Reset Link'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </Modal.Body>
    </Modal>
  );
};

export default ForgotPasswordAtom;
