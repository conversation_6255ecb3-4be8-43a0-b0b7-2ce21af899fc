import "./App.css";
import Header from "./components/Header";
import SiteRoutes from "./routes/SiteRoutes";
import "./styles/styles.scss";

import { Helmet } from "react-helmet";
import { ToastContainer } from "react-toastify";
import Footer from "./components/Footer";
import 'react-inner-image-zoom/lib/InnerImageZoom/styles.css';
import SuspendedUserAlert from "./components/SuspendedUserAlert";
import { useEffect } from "react";

function App() {
  useEffect(() => {
    const script = document.createElement("script");
    script.async = true;
    script.src = "https://www.googletagmanager.com/gtag/js?id=G-03RLDBDECW";
    document.head.appendChild(script);
  
    const inlineScript = document.createElement("script");
    inlineScript.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-03RLDBDECW');
    `;
    document.head.appendChild(inlineScript);
  }, []);  

  return (
    <>
      <Helmet>
        <meta charSet="utf-8" />
        <title>Gajan Creation</title>
        <link rel="canonical" href="https://gajanscreation.com" />
      </Helmet>
      <SuspendedUserAlert />
      <Header />
      <div role="main" className="site-content">
        <div className="header-spacer"></div>
        <SiteRoutes />
      </div>
      <div className="footer-outer">
        <Footer />
      </div>
      <ToastContainer hideProgressBar limit={1} />
    </>
  );
}

export default App;
