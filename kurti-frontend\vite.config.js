import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
    watch: {
      usePolling: true
    }
  },
  resolve: {
    // Aliases for directories
    alias: {
      api: '/src/api',
      assets: '/src/assets',
      components: '/src/components',
      globals: '/src/globals',
      pages: '/src/pages',
      stores: '/src/stores',
      styles: '/src/styles',
      routes: '/src/routes',
      utils: '/src/utils',
      config: '/src/config'
    }
  }
})
