const express = require('express');
const router = express.Router();
const validations = require('./AdminValidations');
const AdminController = require('./AdminController');
const { validate } = require('../../util/validations');
const { verifyTokenAdmin, verifyTokenUserOrAdmin } = require('../../util/auth');

router.post('/log-in', validate(validations.logIn), AdminController.logIn);
router.get('/log-out', AdminController.logout);
router.post('/forgot-password', validate(validations.forgotPassword), AdminController.forgotPassword);
router.post('/change-password', verifyTokenAdmin, validate(validations.updatePassword), AdminController.changePassword);
// router.post('/reset-password', validate(validations.resetPassword), AdminController.resetPassword);
router.delete('/delete-row', validate(validations.activeInactive), AdminController.deleteToggle);
router.put('/active-inactive-row', validate(validations.activeInactive), AdminController.activeInactive);
router.get('/test', AdminController.test);
router.get('/profile', verifyTokenAdmin, AdminController.getAdminProfile);
router.put('/profile', verifyTokenAdmin, AdminController.updateAdminProfile);
router.put('/settings', verifyTokenAdmin, AdminController.updateSettings);
router.get('/settings', AdminController.getSettings);
router.get('/sub-admins', verifyTokenAdmin, AdminController.getSubAdminList);
router.get('/sub-admins/:id', verifyTokenAdmin, AdminController.getSubAdminById);
router.post('/sub-admins', verifyTokenAdmin, AdminController.createSubAdmin);
router.put('/sub-admins/:id', verifyTokenAdmin, AdminController.updateSubAdmin);
router.put('/change-subadmin-password', verifyTokenAdmin, AdminController.updateSubAdminPassword);

module.exports = router;
