const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ShipmentSchema = new Schema({
    shipment_id: {
        type: Number,
        default: null,
    },
    order_id: {
        type: String,
        default: null,
    },
    tracking_number: {
        type: String,
        default: '',
    },
    courier_company_id: {
        type: Number,
        default: null,
    },
    courier_name: {
        type: String,
        default: '',
    },
    applied_weight: {
        type: Number,
        default: 0.0,
    },
    freight_charges: {
        type: Number,
        default: 0.0,
    },
    invoice_no: {
        type: String,
        default: '',
    },
    transporter_id: {
        type: String,
        default: '',
    },
    transporter_name: {
        type: String,
        default: '',
    },
    shipping_status: {
        type: String,
        default: 'NEW', // Default status of the shipment
    },
    awb_code: {
        type: String,
        default: '',
    },
    label_url: {
        type: String,
        default: '',
    },
    manifest_url: {
        type: String,
        default: '',
    },
    manifest_status: {
        type: String,
        default: 0,
    },
    pickup_status: {
        type: String,
        default: 0,
    },
    pickup_token_number: {
        type: String,
        default: null,
    },
    pickup_scheduled_date: {
        type: Date,
        default: null,
    },
    pickup_generated_date: {
        type: Date,
        default: null,
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
    updatedAt: {
        type: Date,
        default: Date.now,
    },
});

module.exports = mongoose.model('Shipment', ShipmentSchema);
