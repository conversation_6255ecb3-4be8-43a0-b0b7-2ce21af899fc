import { FormControl, InputLabel, MenuItem, Select } from "@mui/material";
import { useCartQuery } from "api/cart.api";
import { useCreateOrderMutation, usePlaceOrderMutation } from "api/order.api";
import CheckOutForm from "components/CheckOutForm";
import CouponModal from "components/modals/CouponModal";
import { useFormik } from "formik";
import {
  SETTINGS,
  addressInitialValues,
  addressValidationSchema,
} from "globals";
import parse from "html-react-parser";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { setCartItems } from "stores/cart";
import useUserStore from "stores/user";
import "../styles/free-shipping-progressbar.scss";
import { useAddressQuery } from "api/user.api";
import { preFillValues } from "utils";

const Checkout = () => {
  const token = useUserStore((state) => state.userInfo.token);
  const navigate = useNavigate();

  const { data: cartSavedData } = useCartQuery(token);
  const [paymentType, setPaymentType] = useState("Credit Card");
  const [showCouponModal, setShowCouponModal] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState({});
  const [cartStoredData, setCartStoredData] = useState({});
  const [selectedAddress, setSelectedAddress] = useState({})

  const { mutateAsync: placeOrder } = usePlaceOrderMutation();
  const { mutateAsync: createOrder } = useCreateOrderMutation();

  const { data: addressItems = [] } = useAddressQuery();

  const handleShow = () => {
    setShowCouponModal(true);
  };

  const handleApplyCoupon = (couponData) => {
    setAppliedCoupon(couponData);
  };

  const handleClose = () => {
    setShowCouponModal(false);
  };

  const handlePlaceOrder = async (payload) => {
    try {
      const response = await placeOrder(payload);
      if (response?.success) {
        setCartItems([]);
        toast.success("Order placed successfully");
        navigate("/dashboard");
      }
    } catch (error) {
      toast.error("Something went wrong, please try again later");
    }
  };

  const [selectedAlias, setSelectedAlias] = useState("");

  const handleChange = (event) => {
    setSelectedAlias(event.target.value);
  };

  const handlePayment = (orderInfo, values) => {
    const options = {
      key: import.meta.env.VITE_RAZORPAY_KEY_ID,
      amount: orderInfo?.amount,
      currency: orderInfo?.currency,
      name: values?.cName,
      description: "Order Transaction",
      image: "https://gajancreation.com/assets/logo-BHwpWRfp.png",
      order_id: orderInfo?.id,
      handler: function (response) {
        const payload = {
          products: cartStoredData?.products || [],
          paymentMethod: paymentType,
          shippingAddress: values,
          status: "Processing",
          paymentInfo: response,
        };

        if (appliedCoupon?.code && appliedCoupon?.calculateDiscount) {
          payload.coupoun = {
            code: appliedCoupon?.code,
            cDiscount: appliedCoupon?.calculateDiscount,
          };
        }
        handlePlaceOrder(payload);
      },
      prefill: {
        name: `${values?.fName} ${values?.lName}`,
        email: values?.email,
        contact: values?.phone,
      },
      notes: {
        address: values?.addressLine1,
      },
      theme: {
        color: "#3399cc",
      },
    };

    const rzp1 = new window.Razorpay(options);
    rzp1.open();
  };

  const handleCreateOrder = async (values) => {
    try {
      const response = await createOrder({
        currency: "INR",
        amount: cartStoredData?.totalPrice || 0,
      });
      if (response?.success) {
        handlePayment(response?.data, values);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const formik = useFormik({
    initialValues: preFillValues(addressInitialValues, selectedAddress),
    enableReinitialize: true,
    validationSchema: addressValidationSchema,
    onSubmit: async (values) => {
      try {
        if (paymentType === "Cash on Delivery") {
          const payload = {
            products: cartStoredData?.products || [],
            paymentMethod: paymentType,
            shippingAddress: values,
            status: "Processing",
          };
          handlePlaceOrder(payload);
        } else {
          handleCreateOrder(values);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  useEffect(() => {
    if (cartSavedData) {
      setCartStoredData(cartSavedData);
    }
    if (appliedCoupon?.calculateDiscount) {
      setCartStoredData({
        ...cartStoredData,
        totalPrice:
          cartStoredData?.totalPrice - appliedCoupon?.calculateDiscount,
      });
    }
  }, [cartSavedData, appliedCoupon?.calculateDiscount]);

  const handleChangeAddress = (evt) => {
    setSelectedAddress(JSON.parse(evt.target.value))
  }

  return (
    <>
      <div className="gc-shop-hero gc-page-hero page-hero-mini">
        <div className="container-xl gc-container-xl">
          <div className="row">
            <div className="col-12">
              <div className="gc-page-hero-content gc-flex gc-align-center gc-justify-center">
                <h2>Checkout</h2>
                <nav className="gc-breadcrumbs">
                  <ul className="gc-breadcrumb">
                    <li className="breadcrumb-item">
                      <a href="/" rel="home">
                        <span>Home</span>
                      </a>
                    </li>
                    <li className="breadcrumb-item active">
                      <span>Cart</span>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="nt-gc-inner-container pt-5">
        <div className="container-xl gc-container-xl">
          <div className=" justify-content-center">
            <div className="row col2-set  row-cols-1 row-cols-lg-2">
              <div className="col woocommerce-form-login">
                <div className="row col1-set">
                  <div className="col">
                    <h4 className="gc-form-title">
                      Select address from saved list
                    </h4>
                    <FormControl fullWidth sx={{ marginBottom: "24px" }}>
                      <InputLabel id="address-alias-label">
                        Address Alias
                      </InputLabel>
                      <Select
                        labelId="address-alias-label"
                        id="address-alias-select"
                        value={JSON.stringify(selectedAddress)}
                        label="Address Alias"
                        onChange={handleChangeAddress}
                      >
                        {addressItems?.length > 0 && addressItems.map((alias) => (
                          <MenuItem key={alias?._id} value={JSON.stringify(alias)}>
                            {alias.nickName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </div>
                </div>
                <div className="woocommerce-billing-fields ">
                  <h4 className="gc-form-title">Billing details</h4>
                  <CheckOutForm formik={formik} />
                </div>
              </div>

              <div className="col woocommerce-form-login">
                <div className="gc-order-review mt-5">
                  <h4 className="gc-form-title">Your order</h4>

                  <div className="woocommerce-checkout-review-order">
                    <div className="gc-checkout-review-order-table shop_table woocommerce-checkout-review-order-table">
                      {cartStoredData?.products?.length > 0 && (
                        <div className="gc-cart-items gc-flex">
                          <div className="cart-item-scroll">
                            {cartStoredData?.products?.map((product) => (
                              <div className="gc-cart-item cart_item">
                                <div className="gc-product-name">
                                  <span className="product-img">
                                    <img
                                      width="113"
                                      height="150"
                                      src={product?.productId?.mainImage}
                                      className="attachment-thumbnail size-thumbnail"
                                      alt=""
                                    />
                                  </span>{" "}
                                  <span className="product-name">
                                    {product?.productId?.name}
                                  </span>{" "}
                                  <strong className="product-quantity">
                                    ×&nbsp;{product?.quantity}
                                  </strong>{" "}
                                </div>
                                <div className="gc-product-total product-total">
                                  <span className="woocommerce-Price-amount amount">
                                    <bdi>
                                      <span className="woocommerce-Price-currencySymbol">
                                        {parse(SETTINGS.CURRENCY)}
                                      </span>
                                      {product?.price * product?.quantity}
                                    </bdi>
                                  </span>{" "}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      <div className="gc-checkout-review-order-footer gc-flex">
                        <div className="gc-checkout-footer-item cart-subtotal">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Subtotal</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <span className="woocommerce-Price-amount amount">
                              <bdi>
                                <span className="woocommerce-Price-currencySymbol">
                                  {parse(SETTINGS.CURRENCY)}
                                </span>
                                {cartStoredData?.gTotalPrice || 0}
                              </bdi>
                            </span>
                          </div>
                        </div>
                        <div className="gc-checkout-footer-item cart-subtotal">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Discount</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <span className="woocommerce-Price-amount amount">
                              <bdi>
                                <span className="woocommerce-Price-currencySymbol">
                                  {parse(SETTINGS.CURRENCY)}
                                </span>
                                {cartStoredData?.totalDiscount || 0}
                              </bdi>
                            </span>
                          </div>
                        </div>
                        <div className="gc-checkout-footer-item cart-subtotal">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Coupon Discount</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            {appliedCoupon?.code &&
                            appliedCoupon?.calculateDiscount ? (
                              <div className="text-end">
                                <bdi>
                                  <span className="woocommerce-Price-currencySymbol">
                                    {parse(SETTINGS.CURRENCY)}
                                  </span>
                                  {appliedCoupon?.calculateDiscount}
                                </bdi>
                                <br />
                                <span
                                  style={{
                                    color: "#6C5EBC",
                                  }}
                                  className="cursor-pointer"
                                  onClick={() => {
                                    setAppliedCoupon(null)
                                  }}
                                >
                                  Remove Coupon
                                </span>
                              </div>
                            ) : (
                              <span
                                className="woocommerce-Price-amount amount cursor-pointer"
                                style={{
                                  color: "#6C5EBC",
                                }}
                                onClick={handleShow}
                              >
                                Apply Coupon
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="gc-checkout-footer-item order-total">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Total</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <strong>
                              <span className="woocommerce-Price-amount amount">
                                <bdi>
                                  <span className="woocommerce-Price-currencySymbol">
                                    {parse(SETTINGS.CURRENCY)}
                                  </span>
                                  {cartStoredData?.totalPrice || 0}
                                </bdi>
                              </span>
                            </strong>{" "}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="woocommerce-checkout-payment">
                      <ul className="wc_payment_methods payment_methods methods">
                        <li className="wc_payment_method payment_method_bacs">
                          <input
                            type="radio"
                            className="input-radio"
                            name="payment_method"
                            value="Credit Card"
                            onChange={(e) => setPaymentType(e.target.value)}
                            checked={paymentType === "Credit Card"}
                          />

                          <label for="payment_method_bacs">Credit Card </label>
                          <div className="payment_box payment_method_bacs">
                            <p>Pay via your credit card.</p>
                          </div>
                        </li>
                        <li className="wc_payment_method payment_method_cheque">
                          <input
                            type="radio"
                            className="input-radio"
                            name="payment_method"
                            value="Debit Card"
                            onChange={(e) => setPaymentType(e.target.value)}
                            checked={paymentType === "Debit Card"}
                          />

                          <label for="payment_method_cheque">Debit Card </label>
                          <div className="payment_box payment_method_cheque">
                            <p>Pay via your debit card.</p>
                          </div>
                        </li>
                        <li className="wc_payment_method payment_method_cheque">
                          <input
                            type="radio"
                            className="input-radio"
                            name="payment_method"
                            value="PayPal"
                            onChange={(e) => setPaymentType(e.target.value)}
                            checked={paymentType === "PayPal"}
                          />

                          <label for="payment_method_cheque">Paypal </label>
                          <div className="payment_box payment_method_cheque">
                            <p>Pay via Paypal.</p>
                          </div>
                        </li>
                        <li className="wc_payment_method payment_method_cod">
                          <input
                            type="radio"
                            className="input-radio"
                            name="payment_method"
                            value="Cash on Delivery"
                            onChange={(e) => setPaymentType(e.target.value)}
                            checked={paymentType === "Cash on Delivery"}
                          />

                          <label for="payment_method_cod">
                            Cash on delivery{" "}
                          </label>
                          <div className="payment_box payment_method_cod">
                            <p>Pay with cash upon delivery.</p>
                          </div>
                        </li>
                      </ul>
                      <div className="form-row place-order">
                        <button
                          type="button"
                          className="place_order button alt gc-btn-medium gc-btn gc-btn-black"
                          value="Place order"
                          onClick={formik.handleSubmit}
                        >
                          Place order
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CouponModal
        show={showCouponModal}
        handleClose={handleClose}
        handleSubmit={handleApplyCoupon}
        cartStoredData={cartStoredData}
      />
    </>
  );
};

export default Checkout;
