import { isMobile } from "react-device-detect";
import { useNavigate } from "react-router-dom";
import useCartStore from "stores/cart";
import { IconCart } from "utils/icons";

const CartIconWithCount = ({ onClick, activeLeftMenu }) => {
  const cartItems = useCartStore((state) => state.cartItems);
  const cartCount = cartItems.length ?? 0;

  const navigate = useNavigate();

  const handleClick = (path) => {
    navigate(path);
    if (activeLeftMenu) {
      onClick();
    }
  };
  return (
    <div
      className="top-action-btn"
      data-name="cart"
      onClick={(evt) => {
        evt.preventDefault();
        evt.stopPropagation();
        handleClick("/cart");
      }}
    >
      <span
        className="gc-cart-count gc-wc-count"
        style={isMobile ? {
          right: -5,
          top: -5,
          position: "absolute",
          width: "25px",
          height: "25px",
          fontSize: "15px",
        } : {}}
      >
        {cartCount}
      </span>
      <IconCart className={isMobile ? "shopBag gc-svg-icon mobile" : "shopBag gc-svg-icon"} />
    </div>
  );
};

export default CartIconWithCount;
