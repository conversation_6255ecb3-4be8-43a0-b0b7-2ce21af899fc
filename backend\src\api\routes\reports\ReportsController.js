const {
    models: { Coupon },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');
const slugify = require('slugify');

class ReportsController {
    async sales(req, res) {
        const { startDate, endDate } = req.query;

        try {
            const salesReport = await Order.aggregate([
                {
                    $match: {
                        createdAt: {
                            $gte: new Date(startDate),
                            $lte: new Date(endDate),
                        },
                    },
                },
                {
                    $group: {
                        _id: null,
                        totalSales: { $sum: '$totalPrice' },
                        totalOrders: { $sum: 1 },
                        averageOrderValue: { $avg: '$totalPrice' },
                    },
                },
            ]);

            return res.json({
                success: true,
                report: salesReport[0],
            });
        } catch (error) {
            console.error('Error generating sales report:', error);
            return res.status(500).json({ success: false, message: 'Internal server error.' });
        }
    }

    async productSales(req, res) {
        const { startDate, endDate } = req.query;

        try {
            const productSalesReport = await Order.aggregate([
                {
                    $match: {
                        createdAt: {
                            $gte: new Date(startDate),
                            $lte: new Date(endDate),
                        },
                    },
                },
                { $unwind: '$products' },
                {
                    $group: {
                        _id: '$products.productId',
                        totalSold: { $sum: '$products.quantity' },
                        totalRevenue: { $sum: '$products.total' },
                    },
                },
                {
                    $lookup: {
                        from: 'products',
                        localField: '_id',
                        foreignField: '_id',
                        as: 'productDetails',
                    },
                },
                { $unwind: '$productDetails' },
                {
                    $project: {
                        _id: 0,
                        productId: '$_id',
                        productName: '$productDetails.name',
                        totalSold: 1,
                        totalRevenue: 1,
                    },
                },
            ]);

            return res.json({
                success: true,
                report: productSalesReport,
            });
        } catch (error) {
            console.error('Error generating product sales report:', error);
            return res.status(500).json({ success: false, message: 'Internal server error.' });
        }
    }

    async customer(req, res) {
        const { startDate, endDate } = req.query;

        try {
            const customerReport = await Order.aggregate([
                {
                    $match: {
                        createdAt: {
                            $gte: new Date(startDate),
                            $lte: new Date(endDate),
                        },
                    },
                },
                {
                    $group: {
                        _id: '$user',
                        totalOrders: { $sum: 1 },
                        totalSpent: { $sum: '$totalPrice' },
                    },
                },
                {
                    $lookup: {
                        from: 'users',
                        localField: '_id',
                        foreignField: '_id',
                        as: 'userDetails',
                    },
                },
                { $unwind: '$userDetails' },
                {
                    $project: {
                        _id: 0,
                        userId: '$_id',
                        userName: '$userDetails.name',
                        totalOrders: 1,
                        totalSpent: 1,
                    },
                },
            ]);

            return res.json({
                success: true,
                report: customerReport,
            });
        } catch (error) {
            console.error('Error generating customer report:', error);
            return res.status(500).json({ success: false, message: 'Internal server error.' });
        }
    }
}

module.exports = new ReportsController();
