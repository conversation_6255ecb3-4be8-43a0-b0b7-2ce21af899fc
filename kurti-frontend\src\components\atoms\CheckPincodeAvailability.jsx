import { usePincodeAvailabilityMutation } from "api/util.api";
import React, { useRef, useState } from "react";

const isValidPincode = (pincode) => {
  const pincodeRegex = /^[1-9][0-9]{5}$/;
  return pincodeRegex.test(pincode);
};

const CheckPincodeAvailability = () => {
  const { mutateAsync: checkAvailability } = usePincodeAvailabilityMutation();
  const inputRef = useRef(null);
  const [result, setResult] = useState({});
  const [error, setError] = useState('');

  const handleCheck = async () => {
    try {
      const value = inputRef?.current?.value;
      if (!value) {
        setError('Please enter a pincode');
        return;
      }
      
      if (!isValidPincode(value)) {
        setError('Please enter a valid 6-digit pincode');
        return;
      }

      setError('');
      const response = await checkAvailability({ pincode: value });
      if(!response?.data?.data){
        toast.error(response?.data?.message);
        return;
      }
      setResult(response?.data);
    } catch (err) {
      console.log(err);
      setError('Failed to check pincode availability');
    }
  };
  return (
    <>
      <div className="fw-bold">Check Availability</div>
      <div className="d-flex flex-row w-50 gap-4 mb-1">
        <input
          placeholder="Enter pincode"
          maxLength={6}
          ref={inputRef}
          onChange={() => {
            setResult({})
            setError('')
          }}
          className="form-control"
        />
        <button className="text-light gc-btn gc-btn-medium gc-bg-primary single_add_to_cart_button" onClick={handleCheck}>
          Check
        </button>
      </div>
      <div>
        {error && <div className="text-danger small mb-2">{error}</div>}
        {result?.data?.available_courier_companies?.length > 0 && <span className="text-success">Cash On Delivery is available</span>}
      </div>
    </>
  );
};

export default CheckPincodeAvailability;
