module.exports = {
    Admin: require('./Admin.model'),
    AdminSettings: require('./AdminSettings.model'),
    Category: require('./Category.model'),
    Otp: require('./Otp.model'),
    Page: require('./Page.model'),
    User: require('./User.model'),
    SizeType: require('./SizeType.model'),
    CaptionTag: require('./CaptionTag.model'),
    ClothLength: require('./ClothLength.model'),
    Color: require('./Color.model'),
    Discount: require('./Discount.model'),
    Fabric: require('./Fabric.model'),
    Sections: require('./Sections.model'),
    Occasion: require('./Occasion.model'),
    Pattern: require('./Pattern.model'),
    PaymentMode: require('./PaymentMode.model'),
    Brand: require('./Brand.model'),
    Product: require('./Product.model'),
    Cart: require('./Cart.model'),
    Order: require('./Order.model'),
    Wishlist: require('./Wishlist.model'),
    UserAddress: require('./UserAddress.model'),
    Coupon: require('./Coupon.model'),
    ContactUs: require('./ContactUs.model'),
    Shipment: require('./Shipment.model'),
    Faq: require('./Faq.model'),
};
