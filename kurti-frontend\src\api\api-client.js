import axios from "axios";
import { toast } from "react-toastify";
import { setCartItems } from "stores/cart";
import useUserS<PERSON>, { getAuthToken, resetState } from "stores/user";
import { setWishlistItems } from "stores/wishlist";

export const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

const authRequestInterceptor = (config) => {
  const token = getAuthToken();
  if (config.headers) {
    config.headers["X-ESHOP-Platform"] = "ios";
    config.headers["X-ESHOP-Version"] = "1.0.0";
    config.headers["Accept-Language"] = "en";
  }
  if (token) {
    // Check if token already has 'Bearer ' prefix
    // const authHeader = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
    const authHeader = token.startsWith('Bearer ') ? token : `${token}`;
    config.headers.Authorization = authHeader;
    console.log(`Setting Authorization header: ${authHeader.substring(0, 20)}...`);
  } else {
    console.log("No token available for request to:", config.url);
  }
  return config;
};

apiClient.interceptors.request.use(authRequestInterceptor);

apiClient.interceptors.response.use(
  (response) => {
    if (!response?.data?.success) {
      if (response.request.responseURL.includes("products")) {
        console.error(response?.data?.message);
      } else {
        console.log(response?.data?.message);
      }
    }
    return response.data;
  },
  (error) => {
    const message = error.response?.data?.message || error.message;
    console.error("API Error:", {
      status: error.response?.status,
      url: error.config?.url,
      message: message,
      data: error.response?.data
    });

    // Check if the error is due to suspended account
    if (error.response?.data?.message === "YOUR_ACCOUNT_SUSPENDED") {
      // Don't redirect, just update the user state to show they're suspended
      const currentUserInfo = useUserStore.getState().userInfo;
      if (currentUserInfo && currentUserInfo.user) {
        const updatedUserInfo = {
          ...currentUserInfo,
          user: {
            ...currentUserInfo.user,
            isSuspended: true
          }
        };
        useUserStore.getState().setUserInfo(updatedUserInfo);
      }
      toast.error("Your account has been suspended by the administrator.");
    }
    // Handle 401 errors
    else if (error.response && error.response.status === 401) {
      // Log the current auth state for debugging
      const token = getAuthToken();
      console.error("401 Unauthorized Error:", {
        hasToken: !!token,
        tokenPrefix: token ? token.substring(0, 10) + '...' : 'none',
        endpoint: error.config?.url
      });

      toast.error(message || "Unauthorized, please log in again.");

      // Don't log out the user for review-related 401 errors
      if (!error.config?.url.includes('reviews')) {
        resetState();
        setCartItems([]);
        setWishlistItems([]);
        window.location.href = window.location.origin;
      }
    }
    return Promise.reject(error);
  }
);
