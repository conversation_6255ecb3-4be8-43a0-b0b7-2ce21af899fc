const {
    models: { Category },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');
const slugify = require('slugify');

class CategoryController {
    async list(req, res) {
        try {
            let { catId, search } = req.query;
            let query = {
                isDeleted: false,
                isSuspended: false,
            };

            if (!catId) {
                query.parentId = null;
            } else if (catId !== 'tree') {
                query.parentId = catId;
            }
            // Add search criteria if provided
            if (search) {
                // Create a case-insensitive regular expression for the search term
                const searchRegex = new RegExp(search, 'i');
                query.$or = [
                    { name: searchRegex },
                    { slug: searchRegex },
                    // { _id: search }, // Assumes search can directly match the ID
                ];
            }

            let categories = await Category.find(query);

            if (catId === 'tree') {
                categories = await categoryTree(categories);
            }

            return res.success(categories);
        } catch (error) {
            console.error('Error retrieving categories:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async add(req, res) {
        let { name, image, description, parentId, sizeChart, adBanner } = req.body;
        try {
            const catData = {
                name,
                slug: slugify(name, { remove: undefined, lower: true }),
                image,
                description,
                parentId,
                sizeChart,
                adBanner,
            };

            const category = new Category(catData);
            const newCat = await category.save();
            // Update hasChild parameter of parent category if parentId is provided
            if (parentId) {
                await Category.findByIdAndUpdate(parentId, { $set: { hasChild: true } });
            }

            return res.success({ ...newCat._doc }, req.__('CATEGORY_ADDED'));
        } catch (e) {
            if (e.code === 11000) {
                return res.badRequest({}, req.__('CATEGORY_ALREADY_EXISTS'));
            } else {
                return res.badRequest({}, req.__(e.message));
            }
        }
    }

    async update(req, res) {
        const { catId } = req.params;
        const { name, image, description, parentId, sizeChart, adBanner } = req.body;

        try {
            const updatedData = {
                name,
                slug: slugify(name, { remove: undefined, lower: true }),
                image,
                description,
                parentId,
                sizeChart,
                adBanner,
            };

            // Update the category
            const updatedCategory = await Category.findOneAndUpdate({ _id: catId }, updatedData, { new: true });

            if (!updatedCategory) {
                return res.notFound({}, req.__('CATEGORY_NOT_FOUND'));
            }

            // Update hasChild parameter of parent category if parentId is provided
            if (parentId) {
                await Category.findByIdAndUpdate(parentId, { $set: { hasChild: true } });
            }

            return res.success(updatedCategory, req.__('CATEGORY_UPDATED'));
        } catch (error) {
            console.log('Error updating category:', error);
            if (error.code === 11000) {
                return res.badRequest({}, req.__('CATEGORY_ALREADY_EXISTS'));
            } else {
                return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
            }
        }
    }

    async detail(req, res) {
        const { catId } = req.params;
        const mongoose = require('mongoose');
        const ObjectId = mongoose.Types.ObjectId;

        try {
            const category = await Category.findOne({ _id: catId });
            console.log(category)

            if (!category) {
                return res.notFound({}, req.__('CATEGORY_NOT_FOUND'));
            }

            return res.success(category, req.__('CATEGORY_DETAILS'));
        } catch (error) {
            console.log('Error fetching category:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

const categoryTree = (categories, parentId = null) => {
    const categoryList = [];
    let category;

    if (parentId === null) {
        category = categories.filter(cat => cat.parentId == undefined);
    } else {
        category = categories.filter(
            cat =>
                cat.parentId !== undefined &&
                cat.parentId !== null &&
                parentId !== undefined &&
                cat?.parentId?.toString() == parentId?.toString()
        );
    }

    for (let cate of category) {
        categoryList.push({
            _id: cate._id,
            name: cate.name,
            image: cate.image,
            sizeChart: cate.sizeChart,
            adBanner: cate.adBanner,
            slug: cate.slug,
            description: cate.description,
            parentId: cate.parentId,
            hasChild: cate.hasChild,
            created: cate.created,
            updated: cate.updated,

            children: categoryTree(categories, cate._id),
        });
    }

    return categoryList;
};

module.exports = new CategoryController();
