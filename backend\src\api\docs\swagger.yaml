openapi: '3.0.2'
info:
    version: 1.0.0
    title: Gajan Creation App
servers:
    - url: https://api.gajancreation.com/api
    - url: http://localhost:3005/api
tags:
    - name: Admin
      description: Operations related to admin
    - name: Authentication
      description: Operations related to user authentication
    - name: Cart
      description: Operations related to product on cart
    - name: Categories
      description: Operations related to product categories
    - name: Coupons
      description: Operations related to coupons
    - name: Masters
      description: Operations related to manage masters
    - name: Orders
      description: Operations related to orders
    - name: Page
      description: Static pages related operations
    - name: Products
      description: Operation related to product creation & modifications
    - name: Shipping
      description: Operation related to shipping creation & modifications
    - name: User
      description: User related operations
    - name: Utility
      description: Miscellaneous utility functions
    - name: Wishlist
      description: Operation related to user wishlist
    - name: FAQs
      description: Operations related to frequently asked questions
paths:
    /admin/change-password:
        post:
            summary: reset user password
            operationId: resetPassword
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'Qwerty12#'
                            newPassword: 'Qwerty123$'
                            confirmPassword: 'Qwerty123$'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - newPassword
                                - confirmPassword
                            properties:
                                currentPassword:
                                    type: string
                                    format: password
                                    description: Old Password of user
                                newPassword:
                                    type: string
                                    format: password
                                    description: New Password of user
                                confirmPassword:
                                    type: string
                                    format: password
                                    description: New Password of user
            responses:
                200:
                    description: Password changed successfully message
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/test:
        get:
            summary: logout user
            operationId: logOutUser
            tags:
                - Admin
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: hostname
                  in: query
                  required: false
                  schema:
                      type: string
            responses:
                200:
                    description: Success message indicating a user is logged out successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/log-in:
        post:
            summary: log in into admin app
            operationId: adminLogIn
            tags:
                - Admin
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            email: '<EMAIL>'
                            password: 'Qwerty12#'
                        schema:
                            type: object
                            required:
                                - email
                                - password
                            properties:
                                email:
                                    type: string
                                    description: email of register admin
                                password:
                                    type: string
                                    format: password
                                    description: Password of registered user
            responses:
                200:
                    description: User profile and auth token
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        type: object
                                        properties:
                                            token:
                                                type: string
                                            user:
                                                $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/sub-admins:
      get:
          summary: Get list of sub-admins
          operationId: getSubAdminList
          tags:
              - Admin
          security:
              - ApiKeyAuth: []
          parameters:
              - $ref: '#/components/parameters/headerLanguage'
              - $ref: '#/components/parameters/headerPlatform'
              - $ref: '#/components/parameters/headerVersion'
          responses:
              200:
                  description: List of sub-admins retrieved successfully
                  content:
                      application/json:
                          schema:
                              allOf:
                                  - $ref: '#/components/schemas/ApiResponse'
              400:
                  $ref: '#/components/responses/BadRequestError'

      post:
          summary: Create a new sub-admin
          operationId: createSubAdmin
          tags:
              - Admin
          security:
              - ApiKeyAuth: []
          requestBody:
              required: true
              content:
                  application/json:
                      schema:
                          type: object
                          required:
                              - firstName
                              - lastName
                              - email
                              - password
                              - countryCode
                              - contactNumber
                          properties:
                              firstName:
                                  type: string
                                  description: First name of the sub-admin
                              lastName:
                                  type: string
                                  description: Last name of the sub-admin
                              email:
                                  type: string
                                  format: email
                                  description: Email of the sub-admin
                              password:
                                  type: string
                                  format: password
                                  description: Password for the sub-admin account
                              countryCode:
                                  type: string
                                  description: Country code of the sub-admin
                              contactNumber:
                                  type: string
                                  description: Contact number of the sub-admin
          responses:
              201:
                  description: Sub-admin created successfully
                  content:
                      application/json:
                          schema:
                              allOf:
                                  - $ref: '#/components/schemas/ApiResponse'
              400:
                  $ref: '#/components/responses/BadRequestError'

    /admin/sub-admins/{id}:
        put:
            summary: Update sub-admin information
            operationId: updateSubAdmin
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the sub-admin to update
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                firstName:
                                    type: string
                                    description: First name of the sub-admin
                                lastName:
                                    type: string
                                    description: Last name of the sub-admin
                                email:
                                    type: string
                                    format: email
                                    description: Email of the sub-admin
                                password:
                                    type: string
                                    format: password
                                    description: Password for the sub-admin account
                                countryCode:
                                    type: string
                                    description: Country code of the sub-admin
                                contactNumber:
                                    type: string
                                    description: Contact number of the sub-admin
            responses:
                200:
                    description: Sub-admin updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/active-inactive-row:
        put:
            summary: active or inactive row from any model
            operationId: activeInactive
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Message indicating row deleted successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/delete-row:
        delete:
            summary: delete any row from any model
            operationId: deleteRow
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Message indicating row deleted successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /admin/settings:
        get:
            summary: Get Admin Settings
            operationId: getAdminSettings
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Success response with the settings
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    androidAppVersion:
                                        type: string
                                        description: The version of the Android app.
                                    iosAppVersion:
                                        type: string
                                        description: The version of the iOS app.
                                    androidForceUpdate:
                                        type: boolean
                                        description: Whether to force update the Android app.
                                    iosForceUpdate:
                                        type: boolean
                                        description: Whether to force update the iOS app.
                                    maintenance:
                                        type: boolean
                                        description: Whether the application is in maintenance mode.
                                    homePageBanner:
                                        type: string
                                        description: URL of the homepage banner image.
                                    address:
                                        type: string
                                        description: Address for the application.
                                    email:
                                        type: string
                                        description: Contact email for the application.
                                    contact:
                                        type: string
                                        description: Contact number for the application.
                                    googleApiKey:
                                        type: string
                                        description: Google API key for services.
                                    googleLat:
                                        type: number
                                        description: Google Latitude coordinate.
                                    googleLong:
                                        type: number
                                        description: Google Longitude coordinate.
                                    commonMetaTags:
                                        type: object
                                        additionalProperties:
                                            type: string
                                            description: Common meta tags for SEO.
                                    socialLinks:
                                        type: object
                                        additionalProperties:
                                            type: string
                                            description: Social media links for the application.
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Update Admin Settings
            operationId: updateAdminSettings
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                androidAppVersion:
                                    type: string
                                    description: The version of the Android app.
                                iosAppVersion:
                                    type: string
                                    description: The version of the iOS app.
                                androidForceUpdate:
                                    type: boolean
                                    description: Whether to force update the Android app.
                                iosForceUpdate:
                                    type: boolean
                                    description: Whether to force update the iOS app.
                                maintenance:
                                    type: boolean
                                    description: Whether the application is in maintenance mode.
                                homePageBanner:
                                    type: string
                                    description: URL of the homepage banner image.
                                address:
                                    type: string
                                    description: Address for the application.
                                email:
                                    type: string
                                    description: Contact email for the application.
                                contact:
                                    type: string
                                    description: Contact number for the application.
                                googleApiKey:
                                    type: string
                                    description: Google API key for services.
                                googleLat:
                                    type: number
                                    description: Google Latitude coordinate.
                                googleLong:
                                    type: number
                                    description: Google Longitude coordinate.
                                commonMetaTags:
                                    type: object
                                    additionalProperties:
                                        type: string
                                        description: Common meta tags for SEO.
                                socialLinks:
                                    type: object
                                    additionalProperties:
                                        type: string
                                        description: Social media links for the application.
                            required:
                                - androidAppVersion
                                - iosAppVersion
                                - androidForceUpdate
                                - iosForceUpdate
                                - maintenance
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /admin/profile:
        get:
            summary: Get Admin Profile
            operationId: getAdminProfile
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Success response with the admin profile
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    firstName:
                                        type: string
                                        description: First name of the admin.
                                    lastName:
                                        type: string
                                        description: Last name of the admin.
                                    email:
                                        type: string
                                        description: Email of the admin.
                                    countryCode:
                                        type: string
                                        description: Country code for the contact number.
                                    contactNumber:
                                        type: string
                                        description: Contact number of the admin.
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    description: Admin not found
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                '500':
                    $ref: '#/components/responses/ServerError'

        put:
            summary: Update Admin Profile
            operationId: updateAdminProfile
            tags:
                - Admin
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                firstName:
                                    type: string
                                    description: Admin's first name
                                    example: John
                                lastName:
                                    type: string
                                    description: Admin's last name
                                    example: Doe
                                email:
                                    type: string
                                    description: Admin's email address
                                    example: <EMAIL>
                                countryCode:
                                    type: string
                                    description: Country code for the contact number
                                    example: '+1'
                                contactNumber:
                                    type: string
                                    description: Admin's contact number
                                    example: '1234567890'
                            required:
                                - firstName
                                - lastName
                                - email
            responses:
                '200':
                    description: Admin profile updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/Admin'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /auth/request-otp:
        post:
            summary: request otp
            operationId: requestOtp
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            type: 'SIGN_UP'
                            countryCode: '+1'
                            phone: '0001112223'
                        schema:
                            type: object
                            required:
                                - type
                                - countryCode
                                - phone
                            properties:
                                type:
                                    type: string
                                    description: Available otp type i.e SIGN_UP, FORGOT_PASSWORD, CHANGE_PHONE
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
            responses:
                200:
                    description: Message indicating a OTP has been sent successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/verify-otp:
        post:
            summary: verify otp
            operationId: verifyOtp
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            type: 'SIGN_UP'
                            countryCode: '+1'
                            phone: '0001112223'
                            token: '1234'
                        schema:
                            type: object
                            required:
                                - type
                                - countryCode
                                - phone
                                - token
                            properties:
                                type:
                                    type: string
                                    description: Available otp type i.e SIGN_UP, FORGOT_PASSWORD, CHANGE_PHONE
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
                                token:
                                    type: string
                                    description: OTP received on mobile
            responses:
                200:
                    description: Message indicating otp verified successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        type: object
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/sign-up:
        post:
            summary: sign up into app
            operationId: signUp
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            fullName: 'Ronak Sethi'
                            age: '22'
                            email: '<EMAIL>'
                            countryCode: '+1'
                            phone: '0001112223'
                            password: 'Qwerty12#'
                            deviceToken: ''
                        schema:
                            type: object
                            required:
                                - fullName
                                - age
                                - countryCode
                                - phone
                                - password
                            properties:
                                fullName:
                                    type: string
                                    description: Full name of user
                                age:
                                    type: string
                                    description: Age of user
                                email:
                                    type: string
                                    description: Email adderss of user
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
                                password:
                                    type: string
                                    format: password
                                    description: Password of user
                                deviceToken:
                                    type: string
            responses:
                200:
                    description: User profile and Auth token
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        type: object
                                        properties:
                                            token:
                                                type: string
                                            user:
                                                $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/log-in:
        post:
            summary: log in into app
            operationId: logIn
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            countryCode: '+1'
                            phone: '0001112223'
                            password: 'Qwerty12#'
                            deviceToken: ''
                        schema:
                            type: object
                            required:
                                - countryCode
                                - phone
                                - password
                            properties:
                                countryCode:
                                    type: string
                                    description: Country code of registered user
                                phone:
                                    type: string
                                    description: Phone of registered user
                                password:
                                    type: string
                                    format: password
                                    description: Password of registered user
                                deviceToken:
                                    type: string
            responses:
                200:
                    description: User profile and auth token
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        type: object
                                        properties:
                                            token:
                                                type: string
                                            user:
                                                $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/log-out:
        get:
            summary: logout user
            operationId: logOut
            tags:
                - Authentication
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Success message indicating a user is logged out successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /auth/reset-password:
        post:
            summary: reset user password
            operationId: authresetPassword
            tags:
                - Authentication
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            countryCode: '+1'
                            phone: '0001112223'
                            password: 'Qwerty12#'
                        schema:
                            type: object
                            required:
                                - countryCode
                                - phone
                                - password
                            properties:
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
                                password:
                                    type: string
                                    format: password
                                    description: Password of user
            responses:
                200:
                    description: Password changed successfully message
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /categories/{catId}:
        get:
            summary: Main Category Detail
            operationId: categoriesDetail
            tags:
                - Categories
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: catId
                  in: path
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: user category data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/User'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        put:
            summary: Update a category
            operationId: updateCategory
            tags:
                - Categories
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: catId
                  in: path
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                slug:
                                    type: string
                                image:
                                    type: string
                                description:
                                    type: string
                                parentId:
                                    type: string
                                isSuspended:
                                    type: boolean
                                isDeleted:
                                    type: boolean
                            required:
                                - name
                                - slug
                        examples:
                            example1:
                                value:
                                    name: Sample Category
                                    slug: sample-category
                                    image: http://example.com/sample-image.jpg
                                    description: This is a sample category description.
                                    parentId: 60fbf97f127bd200152adf3b
                                    isSuspended: false
                                    isDeleted: false
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /categories:
        get:
            summary: Main Category List
            operationId: categories
            tags:
                - Categories
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search keyword(Search by name, slug, id)
                - name: catId
                  in: query
                  required: false
                  schema:
                      type: string
            responses:
                '200':
                    description: user profile data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/User'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new category
            operationId: addCategory
            tags:
                - Categories
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
                                slug:
                                    type: string
                                image:
                                    type: string
                                description:
                                    type: string
                                parentId:
                                    type: string
                                isSuspended:
                                    type: boolean
                                isDeleted:
                                    type: boolean
                            required:
                                - name
                                - slug
                        examples:
                            example1:
                                summary: Sample category example
                                value:
                                    name: Sample Category
                                    slug: sample-category
                                    image: http://example.com/sample-image.jpg
                                    description: This is a sample category description.
                                    parentId: 60fbf97f127bd200152adf3b
                                    isSuspended: false
                                    isDeleted: false
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /coupons/{id}:
        get:
            summary: Get Coupon Details
            operationId: getCouponDetails
            tags:
                - Coupons
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: path
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Coupon detail response
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/Coupon'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /coupons/{id}/{status}:
        put:
            summary: Update Coupon Status
            operationId: updateCouponStatus
            tags:
                - Coupons
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: path
                  required: true
                  schema:
                      type: string
                - name: status
                  in: path
                  required: true
                  schema:
                      type: string
                      enum:
                          - Active
                          - Inactive
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /coupons:
        get:
            summary: List Coupons
            operationId: listCoupons
            tags:
                - Coupons
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search keyword (Search by code, name, etc.)
                - in: query
                  name: cartAmount
                  schema:
                      type: string
                  description: cart amount keyword (pass your cart amount)
                - in: query
                  name: status
                  schema:
                      type: string
                      enum:
                          - Active
                          - Inactive
                  description: Filter by coupon status
                - in: query
                  name: page
                  schema:
                      type: integer
                      default: 1
                  description: Page number for pagination
                - in: query
                  name: limit
                  schema:
                      type: integer
                      default: 10
                  description: Number of items per page
            responses:
                '200':
                    description: List of coupons
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/CouponList'
                '400':
                    $ref: '#/components/responses/BadRequestError'

        post:
            summary: Add a New Coupon
            operationId: addCoupon
            tags:
                - Coupons
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                code:
                                    type: string
                                description:
                                    type: string
                                discountType:
                                    type: string
                                discountValue:
                                    type: number
                                maxDiscount:
                                    type: number
                                bundleDetails:
                                    type: object
                                expiry:
                                    type: string
                                    format: date-time
                                maxUses:
                                    type: number
                                minCartValue:
                                    type: number
                                status:
                                    type: string
                                    enum:
                                        - Active
                                        - Inactive
                            required:
                                - code
                                - discountType
                                - discountValue
                                - expiry
                                - maxUses
                                - minCartValue
                                - status
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /coupons/validate:
        post:
            summary: Validate a Coupon Code
            operationId: validateCoupon
            tags:
                - Coupons
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - couponCode
                                - cartTotal
                                - productsInCart
                            properties:
                                couponCode:
                                    type: string
                                    description: The code of the coupon to be validated
                                cartTotal:
                                    type: number
                                    description: The total value of the cart
                                productsInCart:
                                    type: array
                                    items:
                                        type: object
                                        properties:
                                            productId:
                                                type: string
                                                description: The ID of the product
                                            category:
                                                type: string
                                                description: The category of the product
                                            quantity:
                                                type: number
                                                description: The quantity of the product
            responses:
                '200':
                    description: Successful response with validation results
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  isValid:
                                                      type: boolean
                                                      description: Indicates if the coupon is valid
                                                  discountAmount:
                                                      type: number
                                                      description: The amount of discount applied
                                                  message:
                                                      type: string
                                                      description: Additional message
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'

    /masters/size-type:
        get:
            summary: Get all size types
            operationId: getSizeTypes
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new size type
            operationId: addSizeType
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - values
                            properties:
                                name:
                                    type: string
                                    description: Name of the size type (e.g., "Clothing", "Shoe", "Accessory")
                                values:
                                    type: array
                                    description: Possible values for the size type
                                    items:
                                        type: string
                            example:
                                name: 'clothing'
                                values: ['S', 'M', 'L', 'XL', 'XXL', 'XXXL']
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/size-type/{dataId}:
        put:
            summary: Update size type
            operationId: updateSizeType
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the size to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - values
                            properties:
                                name:
                                    type: string
                                    description: Name of the size type (e.g., "Clothing", "Shoe", "Accessory")
                                values:
                                    type: array
                                    description: Possible values for the size type
                                    items:
                                        type: string
                            example:
                                name: 'clothing'
                                values: ['S', 'M', 'L', 'XL', 'XXL', 'XXXL']
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/caption-tag:
        get:
            summary: Get all caption tags
            operationId: getCaptionTag
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new caption tag
            operationId: addCaptionTag
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the size type (e.g., "Clothing", "Shoe", "Accessory")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Trending'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/caption-tag/{dataId}:
        put:
            summary: Add a new caption tag
            operationId: updateCaptionTag
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the caption to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the size type (e.g., "Clothing", "Shoe", "Accessory")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Trending'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/cloth-length:
        get:
            summary: Get all cloth length
            operationId: getClothLength
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new cloth length
            operationId: addClothLength
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the cloth type (e.g., "Ankle Length", "  Calf Length ", "Knee Length")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Ankle Length'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/cloth-length/{dataId}:
        put:
            summary: Update a new cloth length
            operationId: updateClothLength
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the cloth length to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the cloth type (e.g., "  Ankle Length", "  Calf Length ", "Knee Length")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Ankle Length'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/colors:
        get:
            summary: Get all colors list
            operationId: getColors
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new color
            operationId: addColor
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - hexCode
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the color (e.g., "Black", "Blue", "Green")
                                hexCode:
                                    type: number
                                    description: value for color code in Hexa
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Black'
                                hexCode: '#000000'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/colors/{dataId}:
        put:
            summary: Update a color
            operationId: UpdateColor
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the colors to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - hexCode
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the color (e.g., "Black", "Blue", "Green")
                                hexCode:
                                    type: number
                                    description: value for color code in Hexa
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Black'
                                hexCode: '#000000'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/discount:
        get:
            summary: Get all discount list
            operationId: getDiscountList
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new discount
            operationId: addDiscount
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - value
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the discount (e.g., "5% off", "10% off", "15% off")
                                value:
                                    type: number
                                    description: value for discount
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: '5% off'
                                value: 5
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/discount/{dataId}:
        put:
            summary: update discount
            operationId: updateDiscount
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the discount to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - value
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the discount (e.g., "5% off", "10% off", "15% off")
                                value:
                                    type: number
                                    description: value for discount
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: '5% off'
                                value: 5
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/fabric:
        get:
            summary: Get all fabrics list
            operationId: getFabrics
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new fabric
            operationId: addFabric
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the discount (e.g., "Cotton", "Rayon", "Neon")
                                image:
                                    type: string
                                    description: image path of fabric
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Cotton'
                                image: '/fabrics/cotton.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/fabric/{dataId}:
        put:
            summary: Update fabric
            operationId: updateFabric
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the fabric to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the discount (e.g., "Cotton", "Rayon", "Neon")
                                image:
                                    type: string
                                    description: image path of fabric
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Cotton'
                                image: '/fabrics/cotton.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/occasions:
        get:
            summary: Get all occasions list
            operationId: getOccasions
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new occasion
            operationId: addOccasion
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the Occasion (e.g., "Casual ", " Formal", "Wedding")
                                image:
                                    type: string
                                    description: banner image path of Occasion
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Wedding'
                                image: '/occasion/wedding.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/occasions/{dataId}:
        put:
            summary: Update occasion
            operationId: UpdateOccasion
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the occasions to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the Occasion (e.g., "Casual ", " Formal", "Wedding")
                                image:
                                    type: string
                                    description: banner image path of Occasion
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Wedding'
                                image: '/occasion/wedding.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/patterns:
        get:
            summary: Get all patterns list
            operationId: getPatterns
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new patter
            operationId: addPattern
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the Pattern (e.g., "Flower", "Printed", "Solid")
                                image:
                                    type: string
                                    description: banner image path of Pattern
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Flower'
                                image: '/pattern/cotton.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/patterns/{dataId}:
        put:
            summary: Update pattern
            operationId: UpdatePattern
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the pattern to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the Pattern (e.g., "Flower", "Printed", "Solid")
                                image:
                                    type: string
                                    description: banner image path of Pattern
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Flower'
                                image: '/pattern/cotton.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/brands:
        get:
            summary: Get all barnds list
            operationId: getBrands
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new brand
            operationId: addBrand
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the Brand (e.g., "BIBA", "LIBAS", "JAIPURI")
                                image:
                                    type: string
                                    description: banner image path of brand
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'LIBAS'
                                image: '/brand/libasbrand.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/brands/{dataId}:
        put:
            summary: Update brand
            operationId: UpdateBrand
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the brand to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - image
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the Brand (e.g., "BIBA", "LIBAS", "JAIPURI")
                                image:
                                    type: string
                                    description: banner image path of brand
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'LIBAS'
                                image: '/brand/libasbrand.jpg'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/sections:
        get:
            summary: Get all sections list
            operationId: getSections
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new section
            operationId: addSection
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the section (e.g., "Top Products", "Featured Products", "Best Selling Products ")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Top Products'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/sections/{dataId}:
        put:
            summary: Update section
            operationId: updateSection
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the sections to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the section (e.g., "Top Products", "Featured Products", "Best Selling Products ")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'Top Products'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/payment-modes:
        get:
            summary: Get all payment modes list
            operationId: getPaymentMode
            tags:
                - Masters
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add a new payment mode
            operationId: addPaymentMode
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the payment mode (e.g., "COD", "UPI", "CARDS")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'COD'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /masters/payment-modes/{dataId}:
        put:
            summary: Update payment mode
            operationId: UpdatePaymentMode
            tags:
                - Masters
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: dataId
                  in: path
                  description: ID of the payment mode to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - sortOrder
                            properties:
                                name:
                                    type: string
                                    description: Name of the payment mode (e.g., "COD", "UPI", "CARDS")
                                sortOrder:
                                    type: number
                                    description: value for sort order
                            example:
                                name: 'COD'
                                sortOrder: 1
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /products/:
        get:
            summary: Get list of products
            description: Retrieve a list of products.
            operationId: getProductsList
            tags:
                - Products
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search keyword
                - in: query
                  name: catId
                  schema:
                      type: string
                  description: Category ID
                - in: query
                  name: name
                  schema:
                      type: string
                  description: Product name
                - in: query
                  name: sku
                  schema:
                      type: string
                  description: Product SKU
                - in: query
                  name: type
                  schema:
                      type: string
                      enum:
                          - all
                          - B2B
                          - B2C
                  description: Product type (B2B, B2C, etc.)
                - in: query
                  name: brand
                  schema:
                      type: string
                  description: Product brand
                - in: query
                  name: occasion
                  schema:
                      type: string
                  description: Product occasion
                - in: query
                  name: fabric
                  schema:
                      type: string
                  description: Product fabric
                - in: query
                  name: length
                  schema:
                      type: string
                  description: Product length
                - in: query
                  name: pattern
                  schema:
                      type: string
                  description: Product pattern
                - in: query
                  name: color
                  schema:
                      type: string
                  description: Product color (comma-separated list if multiple)
                - in: query
                  name: tags
                  schema:
                      type: string
                  description: Product tags (comma-separated list if multiple)
                - in: query
                  name: sizeType
                  schema:
                      type: string
                  description: Product size type (comma-separated list if multiple)
                - in: query
                  name: minPrice
                  schema:
                      type: number
                  description: Minimum product price
                - in: query
                  name: maxPrice
                  schema:
                      type: number
                  description: Maximum product price
                - in: query
                  name: page
                  description: Page number for pagination
                  schema:
                      type: integer
                      default: 1
                - in: query
                  name: limit
                  description: Number of items per page
                  schema:
                      type: integer
                      default: 10
            responses:
                200:
                    description: products list
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  slug:
                                                      type: string
                                                  title:
                                                      type: string
                                                  description:
                                                      type: string
                400:
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add Product detail
            description: Add a new product.
            operationId: addProductDetail
            tags:
                - Products
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/User'
                        examples:
                            example1:
                                summary: Example of adding a new product
                                value:
                                    sku: 'ABC123'
                                    name: 'Product Name'
                                    categoryId: '60f2f920c869f70020816c1a'
                                    description: 'Product description'
                                    additionalInformation: 'Additional information about the product'
                                    type: 'B2C'
                                    attributes:
                                        brand: 'Brand Name'
                                        sections: ['Section 1', 'Section 2']
                                        occasion: ['Occasion 1', 'Occasion 2']
                                        fabric: ['Fabric 1', 'Fabric 2']
                                        length: 'Length'
                                        pattern: 'Pattern'
                                        color:
                                            - name: 'Red'
                                              hexCode: '#FF0000'
                                            - name: 'Blue'
                                              hexCode: '#0000FF'
                                        tags: ['Tag 1', 'Tag 2']
                                    isSize: true
                                    sizeType: ['Small', 'Medium', 'Large']
                                    seo:
                                        keywords: ['Keyword 1', 'Keyword 2']
                                        metaTitle: 'Meta Title'
                                        metaDescription: 'Meta Description'
                                    b2cPrice:
                                        originalPrice: 100
                                        sellingPrice: 80
                                        discount: 20
                                    b2bPrice:
                                        originalPrice: 90
                                        sellingPrice: 70
                                        discount: 20
                                    stock:
                                        - size: 'Small'
                                          unitPrice: '10'
                                          sellingPrice: '8'
                                          minqty: '5'
                                          productType: 'Type 1'
                                        - size: 'Medium'
                                          unitPrice: '15'
                                          sellingPrice: '12'
                                          minqty: '8'
                                          productType: 'Type 2'
                                    mainImage: 'main-image-url.jpg'
                                    images: ['image-url1.jpg', 'image-url2.jpg']
                                    shopId: '6097a13d4cfb7014e40928b8'
                                    shop:
                                        name: 'Shop Name'
                                        location: 'Shop Location'
                                    sold_out: 0
                                    isSuspended: false
                                    isDeleted: false
            responses:
                200:
                    description: Product created successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        type: object
                                        properties:
                                            productId:
                                                type: string
                400:
                    $ref: '#/components/responses/BadRequestError'

    /products/{productId}:
        get:
            summary: Get a product by ID
            operationId: getProductById
            tags:
                - Products
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: productId
                  in: path
                  description: ID of the product to retrieve
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Product retrieved successfully
                    content:
                        application/json:
                            schema:
                                type: string
                '400':
                    $ref: '#/components/responses/BadRequestError'

        put:
            summary: Update a product
            operationId: updateProduct
            tags:
                - Products
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: productId
                  in: path
                  description: ID of the product to update
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/User'
            responses:
                '200':
                    description: Product updated successfully
                    content:
                        application/json:
                            schema:
                                type: string
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /cart:
        get:
            summary: Get all cart list
            operationId: getCart
            tags:
                - Cart
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add or update product on cart
            operationId: addProductOnCart
            tags:
                - Cart
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - productId
                                - quantity
                            properties:
                                productId:
                                    type: string
                                    description: ID of the product
                                quantity:
                                    type: number
                                    description: Quantity of the product
                                    minimum: 1
                                selectedSize:
                                    type: string
                                    description: Selected size of the product
                                selectedColor:
                                    type: object
                                    properties:
                                        name:
                                            type: string
                                            description: Name of the selected color
                                        hexCode:
                                            type: string
                                            description: Hex code of the selected color
                            example:
                                productId: '60d0fe4f5311236168a109ca'
                                quantity: 2
                                selectedSize: 'M'
                                selectedColor:
                                    name: 'Red'
                                    hexCode: '#FF0000'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /cart/{productId}:
        delete:
            summary: Remove a product from the cart
            operationId: removeProductFromCart
            tags:
                - Cart
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: productId
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the product to remove
            responses:
                '200':
                    description: Product removed from cart
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /orders/my-orders:
        get:
            summary: Get user orders
            operationId: MyOrdersList
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search keyword (Order Id or  Tracking Id )
                - in: query
                  name: status
                  schema:
                      type: string
                      enum:
                          - Pending
                          - Processing
                          - Shipped
                          - Delivered
                          - Cancelled
                  description: order by status
                - name: page
                  in: query
                  description: Page number
                  required: false
                  schema:
                      type: integer
                      default: 1
                - name: limit
                  in: query
                  description: Number of items per page
                  required: false
                  schema:
                      type: integer
                      default: 10
                - name: paymentMethod
                  in: query
                  description: Payment method
                  required: false
                  schema:
                      type: string
                - name: minTotalPrice
                  in: query
                  description: Minimum total price
                  required: false
                  schema:
                      type: number
                - name: maxTotalPrice
                  in: query
                  description: Maximum total price
                  required: false
                  schema:
                      type: number
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'

    /orders/order-detail/{orderId}:
        get:
            summary: Get user order detail
            operationId: MyOrderDetail
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'

    /orders/track-order/{trackingId}:
        get:
            summary: Get user order tracking detail
            operationId: TrackOrderShipmentDetail
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: trackingId
                  in: path
                  description: TrackingId id of order shipment
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'

    /orders/all-orders:
        get:
            summary: Get all orders (admin)
            operationId: AllOrdersList
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search keyword (Order Id or  Tracking Id )
                - in: query
                  name: status
                  schema:
                      type: string
                      enum:
                          - Pending
                          - Processing
                          - Shipped
                          - Delivered
                          - Cancelled
                  description: order by status
                - in: query
                  name: startDate
                  schema:
                      type: string
                      format: date-time
                  description: Filter orders created on or after this date
                - in: query
                  name: endDate
                  schema:
                      type: string
                      format: date-time
                  description: Filter orders created on or before this date
                - name: page
                  in: query
                  description: Page number
                  required: false
                  schema:
                      type: integer
                      default: 1
                - name: limit
                  in: query
                  description: Number of items per page
                  required: false
                  schema:
                      type: integer
                      default: 10

            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'

    /orders/place-order:
        post:
            summary: Place a new order
            operationId: placeOrder
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - products
                                - paymentMethod
                                - shippingAddress
                            properties:
                                products:
                                    type: array
                                    items:
                                        type: object
                                        required:
                                            - productId
                                            - quantity
                                            - price
                                            - total
                                        properties:
                                            productId:
                                                type: string
                                            quantity:
                                                type: number
                                            selectedSize:
                                                type: string
                                                nullable: true
                                            selectedColor:
                                                type: object
                                                properties:
                                                    name:
                                                        type: string
                                                    hexCode:
                                                        type: string
                                            price:
                                                type: number
                                            total:
                                                type: number
                                paymentMethod:
                                    type: string
                                    enum: [Credit Card, Debit Card, PayPal, Cash on Delivery]
                                status:
                                    type: string
                                    enum: ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled']
                                paymentInfo:
                                    type: object
                                    required:
                                        - razorpay_payment_id
                                        - razorpay_order_id
                                        - razorpay_signature
                                shippingAddress:
                                    type: object
                                    required:
                                        - addressLine1
                                        - city
                                        - state
                                        - postalCode
                                        - phone
                                        - email
                                        - fName
                                        - lName
                                        - cName
                                    properties:
                                        addressLine1:
                                            type: string
                                        addressLine2:
                                            type: string
                                            nullable: true
                                        city:
                                            type: string
                                        state:
                                            type: string
                                        postalCode:
                                            type: string
                                        country:
                                            type: string
                                        phone:
                                            type: string
                                        email:
                                            type: string
                                        fName:
                                            type: string
                                        lName:
                                            type: string
                                        cName:
                                            type: string
                                couponCode:
                                    type: string
                                    nullable: true

            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'

    /orders/create-order:
        post:
            summary: Create a new order
            operationId: createOrder
            tags:
                - Orders
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - amount
                                - currency
                            properties:
                                amount:
                                    type: number
                                currency:
                                    type: string

            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
    /orders/cancel-order:
        post:
          summary: Cancel an order
          tags:
            - Orders
          security:
            - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  required:
                    - orderId
                  properties:
                    orderId:
                      type: string
                      description: The ID of the order to be cancelled
                      example: "60c72b2f9b1d8c1b4a4b1f8b"
          responses:
            200:
              description: Order cancelled successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      message:
                        type: string
                        example: "Order cancelled successfully"
                      data:
                         $ref: '#/components/schemas/ApiResponse'
            400:
                $ref: '#/components/responses/BadRequestError'
            401:
                $ref: '#/components/responses/UnauthorizedError'
            404:
                $ref: '#/components/responses/NotFoundError'
            500:
                $ref: '#/components/responses/ServerError'


    /orders/get-refund:
        post:
          summary: Refund for an order
          tags:
            - Orders
          security:
            - ApiKeyAuth: []
          parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
          requestBody:
            required: true
            content:
              application/json:
                schema:
                  type: object
                  required:
                    - orderId
                  properties:
                    orderId:
                      type: string
                      description: The ID of the order to be refunded
                      example: "60c72b2f9b1d8c1b4a4b1f8b"
          responses:
            200:
              description: Order refunded successfully
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      message:
                        type: string
                        example: "Order refunded successfully"
                      data:
                         $ref: '#/components/schemas/ApiResponse'
            400:
                $ref: '#/components/responses/BadRequestError'
            401:
                $ref: '#/components/responses/UnauthorizedError'
            404:
                $ref: '#/components/responses/NotFoundError'
            500:
                $ref: '#/components/responses/ServerError'

    /orders/update-order-status:
        put:
            summary: Update the status of an order
            tags:
            - Orders
            security:
            - ApiKeyAuth: []
            parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            requestBody:
              required: true
              content:
                  application/json:
                       schema:
                          type: object
                          required:
                              - status
                              - orderId
                          properties:
                              status:
                                  type: string
                                  description: The new status of the order
                                  enum: [Pending, Processing, Shipped, Delivered, Cancelled]
                                  example: "Shipped"
                              orderId:
                                  type: string
                                  description: The ID of the order to be updated
                                  example: "60c72b2f9b1d8c1b4a4b1f8b"
            responses:
              200:
                  description: Order status updated successfully
                  content:
                    application/json:
                        schema:
                          type: object
                          properties:
                              success:
                                type: boolean
                                example: true
                              message:
                                type: string
                                example: "Order status updated successfully"
                                data:
                                $ref: '#/components/schemas/ApiResponse'
              400:
                  $ref: '#/components/responses/BadRequestError'
              401:
                  $ref: '#/components/responses/UnauthorizedError'
              404:
                  $ref: '#/components/responses/NotFoundError'
              500:
                  $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/generate:
        get:
            summary: Generate an invoice for an order
            operationId: generateInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to generate the invoice
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice generated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  invoiceUrl:
                                                      type: string
                                                      description: URL to download the invoice
                                                      example: "/api/orders/invoice/60c72b2f9b1d8c1b4a4b1f8b/download"
                                                  invoicePath:
                                                      type: string
                                                      description: Path to the invoice file on the server
                                                      example: "/tmp/invoice-SNK-123456789.pdf"
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/download:
        get:
            summary: Download an invoice for an order
            operationId: downloadInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to download the invoice
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice PDF file
                    content:
                        application/pdf:
                            schema:
                                type: string
                                format: binary
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/email:
        post:
            summary: Send an invoice via email
            operationId: emailInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to email the invoice
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice sent via email successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/whatsapp:
        post:
            summary: Send an invoice via WhatsApp
            operationId: whatsappInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to send the invoice via WhatsApp
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice sent via WhatsApp successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /shipping/shiprocket-create-shipment:
        post:
            summary: Create a new shipment
            operationId: shiprocketCreateShipment
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                order_id:
                                  type: string
                                  description: The order id you want to specify to the order. Max
                                  example: "224477"
            responses:
                200:
                    description: Shipment successfully created
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  shipment_id:
                                                      type: string
                                                      description: ID of the created shipment

    /shipping/shiprocket-create-quick-order:
        post:
            summary: Create a new complete order
            operationId: wrapperApiToCreateOrder
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                order_id:
                                  type: string
                                  description: The order id you want to specify to the order. Max
                                  example: "224477"
            responses:
                200:
                    description: Order successfully created
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  shipment_id:
                                                      type: string
                                                      description: ID of the created shipment


    /shipping/shiprocket-return-order:
        post:
            summary: Create a new return order
            operationId: wrapperApiToReturnOrder
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                order_id:
                                  type: string
                                  description: The order id you want to specify to the order. Max
                                  example: "224477"
            responses:
                200:
                    description: Order return created sucessfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  shipment_id:
                                                      type: string
                                                      description: ID of the returned shipment

    /shipping/shiprocket-generate-awb:
        post:
            summary: Generate a AWB number
            operationId: shiprocketGenerateAWB
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                shipment_id:
                                    type: number
                                    description: Details relating to the shipment id.
                                    example: 16016920
                                courier_id:
                                    type: number
                                    description: Details relating to the courier id.
                                    example: 10
                                status:
                                    type: string
                                    description: Details relating to the status of the order.
                                    example: "reassign"
            responses:
                200:
                    description: AWB for shipment successfully generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    awb_assign_status:
                                        type: integer
                                        description: Status of AWB assignment (1 if successful, 0 if failed).
                                        example: 1
                                    response:
                                        type: object
                                        properties:
                                            data:
                                                type: object
                                                properties:
                                                    courier_company_id:
                                                        type: integer
                                                        description: ID of the courier company.
                                                        example: 142
                                                    awb_code:
                                                        type: string
                                                        description: AWB code for the shipment.
                                                        example: "321055706540"
                                                    cod:
                                                        type: integer
                                                        description: Cash on Delivery status (0 for no COD, 1 for COD).
                                                        example: 0
                                                    order_id:
                                                        type: integer
                                                        description: The order ID associated with the shipment.
                                                        example: 281248157
                                                    shipment_id:
                                                        type: integer
                                                        description: The shipment ID.
                                                        example: 280640636
                                                    awb_code_status:
                                                        type: integer
                                                        description: The status of the AWB code.
                                                        example: 1
                                                    assigned_date_time:
                                                        type: string
                                                        format: date-time
                                                        description: The date and time when the AWB was assigned.
                                                        example: "2022-11-25 11:17:52.878599"
                                                    applied_weight:
                                                        type: number
                                                        format: float
                                                        description: The applied weight for the shipment.
                                                        example: 0.5
                                                    company_id:
                                                        type: integer
                                                        description: ID of the company associated with the shipment.
                                                        example: 25149
                                                    courier_name:
                                                        type: string
                                                        description: The name of the courier company.
                                                        example: "Amazon Surface"
                                                    child_courier_name:
                                                        type: string
                                                        nullable: true
                                                        description: The name of the child courier company (if applicable).
                                                        example: null
                                                    pickup_scheduled_date:
                                                        type: string
                                                        format: date-time
                                                        description: The scheduled pickup date and time.
                                                        example: "2022-11-25 14:00:00"
                                                    routing_code:
                                                        type: string
                                                        description: The routing code for the shipment (if applicable).
                                                        example: ""
                                                    rto_routing_code:
                                                        type: string
                                                        description: The Return To Origin (RTO) routing code (if applicable).
                                                        example: ""
                                                    invoice_no:
                                                        type: string
                                                        description: The invoice number for the shipment.
                                                        example: "retail5769122647118"
                                                    transporter_id:
                                                        type: string
                                                        description: The ID of the transporter (if applicable).
                                                        example: ""
                                                    transporter_name:
                                                        type: string
                                                        description: The name of the transporter (if applicable).
                                                        example: ""
                                                    shipped_by:
                                                        type: object
                                                        properties:
                                                            shipper_company_name:
                                                                type: string
                                                                description: The name of the company shipping the product.
                                                                example: "manoj"
                                                            shipper_address_1:
                                                                type: string
                                                                description: The first address line of the shipper.
                                                                example: "Aligarh"
                                                            shipper_address_2:
                                                                type: string
                                                                description: The second address line of the shipper.
                                                                example: "noida"
                                                            shipper_city:
                                                                type: string
                                                                description: The city of the shipper.
                                                                example: "Jammu"
                                                            shipper_state:
                                                                type: string
                                                                description: The state of the shipper.
                                                                example: "Jammu & Kashmir"
                                                            shipper_country:
                                                                type: string
                                                                description: The country of the shipper.
                                                                example: "India"
                                                            shipper_postcode:
                                                                type: string
                                                                description: The postcode of the shipper.
                                                                example: "110030"
                                                            shipper_first_mile_activated:
                                                                type: integer
                                                                description: Whether the first mile service is activated for the shipper.
                                                                example: 0
                                                            shipper_phone:
                                                                type: string
                                                                description: The phone number of the shipper.
                                                                example: "8976967989"
                                                            lat:
                                                                type: string
                                                                description: Latitude coordinate of the shipper.
                                                                example: "32.731899"
                                                            long:
                                                                type: string
                                                                description: Longitude coordinate of the shipper.
                                                                example: "74.860376"
                                                            shipper_email:
                                                                type: string
                                                                description: The email address of the shipper.
                                                                example: "<EMAIL>"
                                                            rto_company_name:
                                                                type: string
                                                                description: The name of the RTO company.
                                                                example: "test"
                                                            rto_address_1:
                                                                type: string
                                                                description: The first address line of the RTO.
                                                                example: "Unnamed Road, Bengaluru, Karnataka 560060, India"
                                                            rto_address_2:
                                                                type: string
                                                                description: The second address line of the RTO.
                                                                example: "Katrabrahmpur"
                                                            rto_city:
                                                                type: string
                                                                description: The city of the RTO.
                                                                example: "Bangalore"
                                                            rto_state:
                                                                type: string
                                                                description: The state of the RTO.
                                                                example: "Karnataka"
                                                            rto_country:
                                                                type: string
                                                                description: The country of the RTO.
                                                                example: "India"
                                                            rto_postcode:
                                                                type: string
                                                                description: The postcode of the RTO.
                                                                example: "560060"
                                                            rto_phone:
                                                                type: string
                                                                description: The phone number of the RTO.
                                                                example: "9999999999"
                                                            rto_email:
                                                                type: string
                                                                description: The email address of the RTO.
                                                                example: "<EMAIL>"
    /shipping/shiprocket-generate-label:
        post:
            summary: Generate an label
            operationId: shiprocketGenerateLabel
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                shipment_id:
                                    type: number
                                    description: Details relating to the shipment ID.
                                    example: [16104408,16104409]
            responses:
                200:
                    description: Label successfully generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    label_created:
                                        type: integer
                                        description: Status of the label creation (1 if successful, 0 if failed).
                                        example: 1
                                    label_url:
                                        type: string
                                        description: The URL to the generated shipping label.
                                        example: "https://kr-shipmultichannel.s3.ap-southeast-1.amazonaws.com/25149/labels/shipping-label-16104408-788830567028.pdf"
                                    response:
                                        type: string
                                        description: Message indicating whether the label was successfully created or not.
                                        example: "Label has been created and uploaded successfully!"
                                    not_created:
                                        type: array
                                        items:
                                            type: string
                                        description: A list of labels that were not created (if any).
                                        example: []

    /shipping/shiprocket-generate-manifest:
        post:
            summary: Generate manifest
            operationId: shiprocketGenerateManifest
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                shipment_id:
                                    type: array
                                    items:
                                        type: number
                                        description: A list of shipment IDs for which the labels need to be generated.
                                        example: 16090109
            responses:
                200:
                    description: Manifest successfully generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: integer
                                        description: Status of the manifest generation (1 for success, 0 for failure).
                                        example: 1
                                    manifest_url:
                                        type: string
                                        description: The URL to the generated manifest PDF containing the labels.
                                        example: "https://s3-ap-southeast-1.amazonaws.com/kr-shipmultichannel/25149/manifest/MANIFEST-3051.pdf"

    /shipping/shiprocket-generate-pickup:
        post:
            summary: Generate pickup
            operationId: shiprocketGeneratePickup
            tags:
                - Shipping
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                shipment_id:
                                    type: array
                                    items:
                                        type: number
                                        description: A list of shipment ID for which the pickups need to be generated.
                                        example: 16090109
            responses:
                200:
                    description: Manifest successfully generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: integer
                                        description: Status of the manifest generation (1 for success, 0 for failure).
                                        example: 1
                                    manifest_url:
                                        type: string
                                        description: The URL to the generated manifest PDF containing the labels.
                                        example: "https://s3-ap-southeast-1.amazonaws.com/kr-shipmultichannel/25149/manifest/MANIFEST-3051.pdf"


    /shipping/generate-shipping-label:
        get:
          operationId: shippingLabel
          summary: Get shipping label
          description: Retrieves shipping label charges based on provided parameters.
          tags:
                - Shipping
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: waybill
              in: query
              required: true
              description: waybill of shipment
              schema:
                type: string
            - name: pdf
              in: query
              required: true
              description: Is pdf download or not status
              schema:
                type: boolean
                enum:
                  - true
                  - false
                example: true
          responses:
            '200':
              description: Successfully retrieved invoice charges
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        properties:
                          charges:
                            type: array
                            items:
                              type: object
                              properties:
                                charge_type:
                                  type: string
                                  example: "Delivery"
                                amount:
                                  type: number
                                  format: float
                                  example: 100.0
            '400':
              description: Bad Request
            '401':
              description: Unauthorized

    /shipping/calculate-cost:
        get:
          operationId: calculateShipping
          summary: Get Invoice Charges
          description: Retrieves invoice charges based on provided parameters.
          tags:
                - Shipping
          parameters:
            - $ref: '#/components/parameters/headerLanguage'
            - $ref: '#/components/parameters/headerPlatform'
            - $ref: '#/components/parameters/headerVersion'
            - name: md
              in: query
              required: true
              description: Mode of delivery 'E' for express and 'S' for surface
              schema:
                type: string
                enum:
                  - S
                  - E
            - name: ss
              in: query
              required: true
              description: Shipment status
              schema:
                type: string
                enum:
                  - Delivered
                  - RTO
                  - DTO
                example: Delivered
            - name: d_pin
              in: query
              required: true
              description: 6 digit valid pin code of destination
              schema:
                type: string
                example: 302033
            - name: o_pin
              in: query
              required: true
              description: 6 digit valid pin code of origin
              schema:
                type: string
                example: 302003
            - name: cgm
              in: query
              required: true
              description: Charged gross mass (Weight in grams)
              schema:
                type: integer
                example: 500
            - name: pt
              in: query
              required: true
              description: Payment type
              schema:
                type: string
                enum:
                  - COD
                  - Pre-paid
                  - Pickup
                  - REPL
                example: Pre-paid
            - name: cod
              in: query
              required: true
              description: Cash on delivery amount (aplicable if Payment type is COD else 0)
              schema:
                type: integer
                example: 0
          responses:
            '200':
              description: Successfully retrieved invoice charges
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      success:
                        type: boolean
                        example: true
                      data:
                        type: object
                        properties:
                          charges:
                            type: array
                            items:
                              type: object
                              properties:
                                charge_type:
                                  type: string
                                  example: "Delivery"
                                amount:
                                  type: number
                                  format: float
                                  example: 100.0
            '400':
              description: Bad Request
            '401':
              description: Unauthorized

    /shipping/shiprocket-check-area-availability:
      get:
        operationId: checkAreaAvailability
        summary: Check Area Availability
        description: Checks if a specified destination pin code is serviceable.
        tags:
          - Shipping
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: pincode
            in: query
            required: false
            description: The 6-digit pin code of the delivery area to check for serviceability.
            schema:
              type: string
              example: "110001"
        responses:
          '200':
            description: Successfully checked area availability
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    success:
                      type: boolean
                      example: true
                    data:
                      type: object
                      properties:
                        isServiceable:
                          type: boolean
                          example: true
                        estimatedDeliveryDays:
                          type: integer
                          example: 3
                        availableCouriers:
                          type: array
                          items:
                            type: object
                            properties:
                              courier_name:
                                type: string
                                example: "Blue Dart"
                              service_type:
                                type: string
                                example: "Express"
                              delivery_time:
                                type: string
                                example: "1-2 days"
          '400':
            description: Bad Request - Invalid or missing input
          '401':
            description: Unauthorized - Invalid credentials or token
          '500':
            description: Internal Server Error - Unable to process request

    /shipping/shiprocket-get-tracking-by-awb:
      get:
        operationId: getTrackingByAWB
        summary: Check tracking
        description: Checks if a specified awb code is trackable.
        tags:
          - Shipping
        parameters:
          - $ref: '#/components/parameters/headerLanguage'
          - $ref: '#/components/parameters/headerPlatform'
          - $ref: '#/components/parameters/headerVersion'
          - name: awb_code
            in: query
            required: false
            description: The awb code of the order area to check for tracking.
            schema:
              type: string
              example: I49893950
        responses:
          '200':
            description: Successfully Tracked
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    success:
                      type: boolean
                      example: true
                    data:
                      type: object
                      tracking_data:
                        type: object
                        properties:
                            track_status:
                            type: integer
                            example: 1
                            shipment_status:
                            type: integer
                            example: 7
                            shipment_track:
                            type: array
                            items:
                                type: object
                                properties:
                                id:
                                    type: integer
                                    example: *********
                                awb_code:
                                    type: string
                                    example: "141123221084922"
                                courier_company_id:
                                    type: integer
                                    example: 51
                                shipment_id:
                                    type: integer
                                    example: *********
                                order_id:
                                    type: integer
                                    example: *********
                                pickup_date:
                                    type: string
                                    example: "2022-07-18 20:28:00"
                                delivered_date:
                                    type: string
                                    example: "2022-07-19 11:37:00"
                                weight:
                                    type: string
                                    example: "0.30"
                                packages:
                                    type: integer
                                    example: 1
                                current_status:
                                    type: string
                                    example: "Delivered"
                                delivered_to:
                                    type: string
                                    example: "Chittoor"
                                destination:
                                    type: string
                                    example: "Chittoor"
                                consignee_name:
                                    type: string
                                    example: ""
                                origin:
                                    type: string
                                    example: "Banglore"
                                courier_name:
                                    type: string
                                    example: "Xpressbees Surface"
                                pod:
                                    type: string
                                    example: "Available"
                                pod_status:
                                    type: string
                                    example: "https://s3-ap-southeast-1.amazonaws.com/kr-shipmultichannel/courier/51/pod/141123221084922.png"
                      shipment_track_activities:
                        type: array
                        items:
                            type: object
                            properties:
                            date:
                                type: string
                                example: "2022-07-19 11:37:00"
                            status:
                                type: string
                                example: "DLVD"
                            activity:
                                type: string
                                example: "Delivered"
                            location:
                                type: string
                                example: "MADANPALLI, Madanapalli, ANDHRA PRADESH"
                            sr-status:
                                type: string
                                example: "7"
                            sr-status-label:
                                type: string
                                example: "DELIVERED"
                        track_url:
                        type: string
                        example: "https://shiprocket.co//tracking/141123221084922"
                        etd:
                        type: string
                        example: "2022-07-20 19:28:00"
                        qc_response:
                        type: object
                        properties:
                            qc_image:
                            type: string
                            example: ""
                            qc_failed_reason:
                            type: string
                            example: ""
          '400':
            description: Bad Request - Invalid or missing input
          '401':
            description: Unauthorized - Invalid credentials or token
          '500':
            description: Internal Server Error - Unable to process request

    /users/address:
        post:
            summary: Add a new address
            operationId: addAddress
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                nickName:
                                    type: string
                                    example: 'Home'
                                addressLine1:
                                    type: string
                                    example: '123 Main St'
                                addressLine2:
                                    type: string
                                    example: 'Apt 4B'
                                city:
                                    type: string
                                    example: 'New York'
                                state:
                                    type: string
                                    example: 'NY'
                                postalCode:
                                    type: string
                                    example: '10001'
                                country:
                                    type: string
                                    example: 'USA'
                                phone:
                                    type: string
                                    example: '+1234567890'
                                email:
                                    type: string
                                    example: '<EMAIL>'
                                fName:
                                    type: string
                                    example: 'John'
                                lName:
                                    type: string
                                    example: 'Doe'
                                cName:
                                    type: string
                                    example: 'Company Inc.'
                            required:
                                - nickName
                                - addressLine1
                                - city
                                - state
                                - postalCode
                                - country
                                - phone
                                - email
                                - fName
                                - lName
                                - cName
            responses:
                '200':
                    description: Address added successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

        get:
            summary: List all addresses
            operationId: listAddresses
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: List of addresses
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  $ref: '#/components/schemas/UserAddress'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /users/address/{addressId}:
        put:
            summary: Update an address
            operationId: editAddress
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: addressId
                  in: path
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                nickName:
                                    type: string
                                    example: 'Home'
                                addressLine1:
                                    type: string
                                    example: '123 Main St'
                                addressLine2:
                                    type: string
                                    example: 'Apt 4B'
                                city:
                                    type: string
                                    example: 'New York'
                                state:
                                    type: string
                                    example: 'NY'
                                postalCode:
                                    type: string
                                    example: '10001'
                                country:
                                    type: string
                                    example: 'USA'
                                phone:
                                    type: string
                                    example: '+1234567890'
                                email:
                                    type: string
                                    example: '<EMAIL>'
                                fName:
                                    type: string
                                    example: 'John'
                                lName:
                                    type: string
                                    example: 'Doe'
                                cName:
                                    type: string
                                    example: 'Company Inc.'
                            required:
                                - addressLine1
                                - city
                                - state
                                - postalCode
                                - country
                                - phone
                                - email
                                - fName
                                - lName
                                - cName
            responses:
                '200':
                    description: Address updated successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

        get:
            summary: Get address details
            operationId: getAddressDetail
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: addressId
                  in: path
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Address details retrieved successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/UserAddress'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /users/list:
        get:
            summary: get users list
            operationId: getUsersList
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: search
                  in: query
                  required: false
                  schema:
                      type: string
                - name: page
                  in: query
                  required: true
                  schema:
                      type: string
                      default: 1
                - name: limit
                  in: query
                  required: true
                  schema:
                      type: string
                      default: 10
                - name: orderby
                  in: query
                  required: false
                  schema:
                      type: string
                      enum:
                          - 'desc'
                          - 'asc'
            responses:
                200:
                    description: Users List
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /users/profile:
        get:
            summary: get user profile
            operationId: getProfile
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: id
                  in: query
                  description: ObjectId of user
                  required: false
                  schema:
                      type: string
                      format: objectId
            responses:
                200:
                    description: User profile
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'
        put:
            summary: update user profile
            operationId: updateProfile
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            type: 'PROFILE_UPDATE'
                            fullName: 'Ronak Sethi'
                            age: '22'
                            userName: 'ronak.s'
                            bio: 'my bio'
                            description: 'my description'
                            avatar: ''
                        schema:
                            type: object
                            required:
                                - type
                                - fullName
                                - age
                                - userName
                            properties:
                                type:
                                    type: string
                                    description: Available update type i.e. PROFILE_UPDATE, ACCOUNT_COMPLETE,
                                fullName:
                                    type: string
                                    description: Full name of user
                                age:
                                    type: string
                                    description: Age of user
                                userName:
                                    type: string
                                    description: user-name of user
                                bio:
                                    type: string
                                    description: Bio of user
                                description:
                                    type: string
                                    description: Description of user
                                avatar:
                                    type: string
                                    description: Profile picture of user
            responses:
                200:
                    description: Message indicating profile has been updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /users/password:
        put:
            summary: update user pasword
            operationId: updatePassword
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'Qwerty12#'
                            newPassword: 'Qwerty12#5'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - newPassword
                            properties:
                                currentPassword:
                                    type: string
                                    description: Current password of registered user
                                newPassword:
                                    type: string
                                    format: password
                                    description: Password of user
            responses:
                200:
                    description: Message indicating password changed successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /users/email:
        put:
            summary: update user email
            operationId: updateEmail
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'Qwerty12#'
                            email: '<EMAIL>'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - email
                            properties:
                                currentPassword:
                                    type: string
                                    description: Current password of registered user
                                email:
                                    type: string
                                    description: Email of user
            responses:
                200:
                    description: Message indicating email updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /users/phone:
        put:
            summary: update user phone
            operationId: updatePhone
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            currentPassword: 'Qwerty12#'
                            countryCode: '+1'
                            phone: '0001112223'
                        schema:
                            type: object
                            required:
                                - currentPassword
                                - countryCode
                                - phone
                            properties:
                                currentPassword:
                                    type: string
                                    description: Current password of registered user
                                countryCode:
                                    type: string
                                    description: Country code of user
                                phone:
                                    type: string
                                    description: Phone of user
            responses:
                200:
                    description: Message indicating phone updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /users/notification-toggle:
        get:
            summary: turn on or off push notifications
            operationId: notificationToggle
            tags:
                - User
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: Message indicating push notifications is on or off
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                properties:
                                    data:
                                        $ref: '#/components/schemas/User'
                400:
                    $ref: '#/components/responses/BadRequestError'

    /wishlist:
        get:
            summary: Get all wishlist
            operationId: getWishlist
            tags:
                - Wishlist
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: Successful response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
        post:
            summary: Add or update product on wishlist
            operationId: addProductOnWishlist
            tags:
                - Wishlist
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - productId
                            properties:
                                productId:
                                    type: string
                                    description: ID of the product
                            example:
                                productId: '60d0fe4f5311236168a109ca'
            responses:
                '200':
                    description: Success response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /wishlist/{productId}:
        delete:
            summary: Remove a product from the wishlist
            operationId: removeProductFromWishlist
            tags:
                - Wishlist
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: productId
                  in: path
                  required: true
                  schema:
                      type: string
                  description: ID of the product to remove
            responses:
                '200':
                    description: Product removed from cart
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'

    /pages/:
        post:
            summary: update static page
            operationId: updateStaticPage
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            title: 'Refund Policy'
                            slug: 'refundpolicy'
                            description: 'Loren lipsum is a simply dummy text.'
                        schema:
                            type: object
                            required:
                                - title
                                - slug
                                - description
                            properties:
                                title:
                                    type: string
                                    description: Title of static page
                                slug:
                                    type: string
                                    description: Slug of static page
                                description:
                                    type: string
                                    description: Description of static page
            responses:
                200:
                    description: static page data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  slug:
                                                      type: string
                                                  title:
                                                      type: string
                                                  description:
                                                      type: string
                400:
                    $ref: '#/components/responses/BadRequestError'

    /pages/{slug}:
        get:
            summary: get static page
            operationId: getStaticPage
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: slug
                  in: path
                  description: slug for static page
                  required: true
                  schema:
                      type: string
            responses:
                200:
                    description: static page data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  slug:
                                                      type: string
                                                  title:
                                                      type: string
                                                  description:
                                                      type: string
                400:
                    $ref: '#/components/responses/BadRequestError'
        put:
            summary: update static page
            operationId: updateStaticPages
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: slug
                  in: path
                  description: slug for static page
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        example:
                            title: 'Qwerty12#'
                            description: '+1'
                        schema:
                            type: object
                            required:
                                - title
                                - description
                            properties:
                                title:
                                    type: string
                                    description: Title of static page
                                description:
                                    type: string
                                    description: Description of static page
            responses:
                200:
                    description: static page data
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  slug:
                                                      type: string
                                                  title:
                                                      type: string
                                                  description:
                                                      type: string
                400:
                    $ref: '#/components/responses/BadRequestError'

    /pages:
        get:
            summary: List Pages
            operationId: listPages
            tags:
                - Page
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: search
                  in: query
                  required: false
                  schema:
                      type: string
                      description: Search keyword to filter pages by title or slug
            responses:
                '200':
                    description: Successfully retrieved list of pages
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  $ref: '#/components/schemas/Page'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'


    /utils/upload-file:
        get:
            summary: request signed urls for uploading documents
            operationId: uploadFile
            tags:
                - Utility
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: location
                  in: query
                  required: true
                  schema:
                      type: string
                - name: type
                  in: query
                  required: true
                  schema:
                      type: string
                      enum:
                          - IMAGE
                          - DOCUMENT.PDF
                - name: count
                  in: query
                  required: true
                  schema:
                      type: string
                      format: int32
                      pattern: '^[\d]+$'
            responses:
                200:
                    description: Array of signed urls
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  type: object
                                                  properties:
                                                      url:
                                                          type: string
                                                          description: URL on which the document needs to be uploaded
                                                      preview:
                                                          type: string
                                                          description: URL for fetching the document

    /utils/delhivery-pincode-serviceability:
        get:
            summary: Delhivery pincode serviceability
            operationId: delhiveryPincodeServiceability
            tags:
                - Utility
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: pincode
                  in: query
                  required: true
                  schema:
                      type: string
            responses:
                200:
                    description: Object of delivery status
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  type: object
                                                  properties:
                                                      url:
                                                          type: string
                                                          description: URL on which the document needs to be uploaded
                                                      preview:
                                                          type: string
                                                          description: URL for fetching the document

    /utils/delhivery-track-shipment:
        get:
            summary: delhivery-track-shipment
            operationId: delhivery-track-shipment
            tags:
                - Utility
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: waybill
                  in: query
                  required: true
                  schema:
                      type: string
            responses:
                200:
                    description: object of delivery status
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  type: object
                                                  properties:
                                                      url:
                                                          type: string
                                                          description: URL on which the document needs to be uploaded
                                                      preview:
                                                          type: string
                                                          description: URL for fetching the document
    /utils/dashboard:
        get:
            summary: dashboard data
            operationId: dashboard data
            tags:
                - Utility
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                200:
                    description: object of dashboard status
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  type: object
                                                  properties:
                                                      url:
                                                          type: string
                                                          description: URL on which the document needs to be uploaded
                                                      preview:
                                                          type: string
                                                          description: URL for fetching the document

    /utils/contact-us:
        post:
            summary: Submit a contact form
            operationId: contactUs
            tags:
                - Utility
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                          type: object
                          properties:
                              name:
                                  type: string
                                  description: Name of the person contacting
                                  example: John Doe
                              email:
                                  type: string
                                  description: Email address of the person contacting
                                  example: <EMAIL>
                              message:
                                  type: string
                                  description: Message from the person contacting
                                  example: I have a question about your service.
                              type:
                                  type: string
                                  description: Type of the message (e.g., inquiry, feedback)
                                  example: inquiry
            responses:
                '200':
                    description: Contact message sent successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                      message:
                                          type: string
                                          example: Contact message sent successfully
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /faqs:
        get:
            summary: Get list of FAQs
            operationId: getFaqsList
            tags:
                - FAQs
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: query
                  name: page
                  schema:
                      type: integer
                      default: 1
                  description: Page number for pagination
                - in: query
                  name: limit
                  schema:
                      type: integer
                      default: 10
                  description: Number of items per page
                - in: query
                  name: search
                  schema:
                      type: string
                  description: Search term to filter FAQs by question or answer
                - in: query
                  name: category
                  schema:
                      type: string
                  description: Filter FAQs by category
                - in: query
                  name: sortBy
                  schema:
                      type: string
                      default: sortOrder
                  description: Field to sort by
                - in: query
                  name: sortOrder
                  schema:
                      type: string
                      enum: [asc, desc]
                      default: asc
                  description: Sort direction
            responses:
                '200':
                    description: List of FAQs retrieved successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  items:
                                                      type: array
                                                      items:
                                                          $ref: '#/components/schemas/Faq'
                                                  count:
                                                      type: integer
                                                      description: Total number of FAQs
                                                  page:
                                                      type: integer
                                                      description: Current page number
                                                  limit:
                                                      type: integer
                                                      description: Number of items per page
                                                  totalPages:
                                                      type: integer
                                                      description: Total number of pages
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'
        post:
            summary: Create a new FAQ
            operationId: createFaq
            tags:
                - FAQs
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - question
                                - answer
                            properties:
                                question:
                                    type: string
                                    description: The question text
                                answer:
                                    type: string
                                    description: The answer text
                                category:
                                    type: string
                                    description: Category of the FAQ
                                    default: General
                                sortOrder:
                                    type: integer
                                    description: Order for sorting FAQs
                                    default: 0
            responses:
                '200':
                    description: FAQ created successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/Faq'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /faqs/categories:
        get:
            summary: Get list of FAQ categories
            operationId: getFaqCategories
            tags:
                - FAQs
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
            responses:
                '200':
                    description: List of FAQ categories retrieved successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: array
                                              items:
                                                  type: string
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /faqs/{id}:
        get:
            summary: Get FAQ details
            operationId: getFaqDetails
            tags:
                - FAQs
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the FAQ to retrieve
            responses:
                '200':
                    description: FAQ details retrieved successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/Faq'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'
        put:
            summary: Update FAQ
            operationId: updateFaq
            tags:
                - FAQs
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the FAQ to update
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                question:
                                    type: string
                                    description: The question text
                                answer:
                                    type: string
                                    description: The answer text
                                category:
                                    type: string
                                    description: Category of the FAQ
                                sortOrder:
                                    type: integer
                                    description: Order for sorting FAQs
            responses:
                '200':
                    description: FAQ updated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              $ref: '#/components/schemas/Faq'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'
        delete:
            summary: Delete FAQ
            operationId: deleteFaq
            tags:
                - FAQs
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the FAQ to delete
            responses:
                '200':
                    description: FAQ deleted successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /faqs/{id}/toggle-status:
        put:
            summary: Toggle FAQ status (activate/deactivate)
            operationId: toggleFaqStatus
            tags:
                - FAQs
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - in: path
                  name: id
                  schema:
                      type: string
                  required: true
                  description: ID of the FAQ to toggle status
            responses:
                '200':
                    description: FAQ status toggled successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'
components:
    parameters:
        headerLanguage:
            name: Accept-Language
            in: header
            required: true
            schema:
                type: string
                default: en
                enum:
                    - en
                    - ru
                    - kk
        headerPlatform:
            name: X-ESHOP-Platform
            in: header
            required: true
            schema:
                type: string
                default: ios
                enum:
                    - ios
                    - android
        headerVersion:
            name: X-ESHOP-Version
            in: header
            required: true
            schema:
                type: string
                default: 1.0.0
                pattern: '^[\d]+\.[\d]+\.[\d]+$'
        queryPerPage:
            in: query
            name: perPage
            description: number of records per page
            allowEmptyValue: false
            required: true
            schema:
                type: integer
        queryPage:
            in: query
            name: page
            description: page number
            allowEmptyValue: false
            required: true
            schema:
                type: integer

    schemas:
        ApiResponse:
            required:
                - success
                - message
                - meta
            properties:
                success:
                    type: boolean
                message:
                    type: string
                data: {}
                meta:
                    type: object
                    properties:
                        version:
                            type: string
                        forceUpdate:
                            type: boolean
                        maintenance:
                            type: boolean
                        hasUpdate:
                            type: boolean

        Admin:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                countryCode:
                    type: string
                contactNumber:
                    type: string

        Coupon:
            type: object
            properties:
                _id:
                    type: string
                code:
                    type: string
                description:
                    type: string
                discountType:
                    type: string
                discountValue:
                    type: number
                maxDiscount:
                    type: number
                bundleDetails:
                    type: object
                expiry:
                    type: string
                    format: date-time
                numberOfUses:
                    type: number
                maxUses:
                    type: number
                minCartValue:
                    type: number
                status:
                    type: string
                    enum:
                        - Active
                        - Inactive
            required:
                - code
                - discountType
                - discountValue
                - expiry
                - maxUses
                - minCartValue
                - status

        CouponList:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/Coupon'
                count:
                    type: integer
                page:
                    type: integer
                limit:
                    type: integer

        Error:
            type: object
            properties:
                message:
                    type: string
                    example: Resource not found

        User:
            properties:
                fullName:
                    type: string
                age:
                    type: number
                email:
                    type: string
                    format: email
                countryCode:
                    type: string
                phone:
                    type: string
                formattedPhone:
                    type: string
                avatar:
                    type: string
                pushNotificationAllowed:
                    type: string

        UserAddress:
            type: object
            properties:
                _id:
                    type: string
                    description: The unique identifier for the user address.
                userId:
                    type: string
                    description: The unique identifier for the user.
                nickName:
                    type: string
                    description: Nickname for the address.
                addressLine1:
                    type: string
                    description: The first line of the address.
                addressLine2:
                    type: string
                    description: The second line of the address.
                city:
                    type: string
                    description: The city of the address.
                state:
                    type: string
                    description: The state of the address.
                postalCode:
                    type: string
                    description: The postal code of the address.
                country:
                    type: string
                    description: The country of the address.
                phone:
                    type: string
                    description: The phone number associated with the address.
                email:
                    type: string
                    description: The email address associated with the address.
                fName:
                    type: string
                    description: The first name of the person associated with the address.
                lName:
                    type: string
                    description: The last name of the person associated with the address.
                cName:
                    type: string
                    description: The company name associated with the address.
                created:
                    type: string
                    format: date-time
                    description: The date and time when the address was created.
                updated:
                    type: string
                    format: date-time
                    description: The date and time when the address was last updated.

        Page:
            type: object
            properties:
                _id:
                    type: string
                    description: Unique identifier for the page
                title:
                    type: string
                    description: Title of the page
                description:
                    type: string
                    description: Description of the page
                slug:
                    type: string
                    description: URL-friendly identifier for the page
                isSuspended:
                    type: boolean
                    description: Status indicating if the page is suspended
                created:
                    type: string
                    format: date-time
                    description: Page creation timestamp
                updated:
                    type: string
                    format: date-time
                    description: Page last updated timestamp

        Faq:
            type: object
            properties:
                _id:
                    type: string
                    description: Unique identifier for the FAQ
                question:
                    type: string
                    description: The question text
                answer:
                    type: string
                    description: The answer text
                category:
                    type: string
                    description: Category of the FAQ
                sortOrder:
                    type: integer
                    description: Order for sorting FAQs
                isSuspended:
                    type: boolean
                    description: Status indicating if the FAQ is suspended
                isDeleted:
                    type: boolean
                    description: Status indicating if the FAQ is deleted
                created:
                    type: string
                    format: date-time
                    description: FAQ creation timestamp
                updated:
                    type: string
                    format: date-time
                    description: FAQ last updated timestamp
    securitySchemes:
        ApiKeyAuth:
            type: apiKey
            in: header
            name: Authorization

    responses:
        ServerError:
            description: Internal Server Error
            content:
                application/json:
                    schema:
                        type: object
                        properties:
                            message:
                                type: string
                                example: Internal Server Error
                            code:
                                type: integer
                                example: 500
        BadRequestError:
            description: Request fails validation or doesn't meet all conditions for request
            content:
                application/json:
                    schema:
                        example:
                            success: false
                            message: 'Validation failed'
                            data: ''
                        allOf:
                            - $ref: '#/components/schemas/ApiResponse'
                            - type: object
                              properties:
                                  data:
                                      type: object
                                      nullable: true
        NotFoundError:
            description: Requested resource was not found
        UnauthorizedError:
            description: Access token is missing or invalid
