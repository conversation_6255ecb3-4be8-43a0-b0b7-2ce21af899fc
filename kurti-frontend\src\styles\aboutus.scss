.about-page {
    .about-banner {
        background: url(../assets/images/slideshow-1920-31.png) 50% top no-repeat;
        background-size: auto;
        background-attachment: fixed;
        min-height: 520px;
        position: relative;
        margin-bottom: 80px;
        @media(max-width:767.98px) {
            min-height: 360px;
        }

        &:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--e-global-color-accent);
            opacity: 0.15;
        }

        .about-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            @media(min-width:1200px) {
                width: 36%;
            }

            @media(max-width:1199.98px) {
                width: 100%;
            }
            @media(max-width:767.98px) {
                padding-right: 15px;
                padding-left: 15px;
            }


            h4 {
                color: var(--gc-light);
                font-size: 10px;
            }

            h3 {
                color: var(--gc-light);
                font-size: 48px;
                @media(max-width:767.98px) {
                    font-size: 24px;
                }
            }

            p {
                color: var(--gc-light);
                font-size: 16px;
            }
        }

    }


    .about-content-grid {
        &+.about-content-grid {
            margin-top: 60px;
            @media(max-width:767.98px) {
                margin-top: 30px;
            }
        }

        .img-responsive {
            max-width: 100%;
        }

        .grid-content-column {
            @media(min-width:1200px) {
                padding-left: 15px;
            }

            @media(max-width:1199.98px) {
                padding-top: 40px;
            }
        }

        .desc {
            @media(min-width:1200px) {
                max-width: 70%;
            }
        }

        h6 {
            color: var(--gc-purple);
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 24px;
            font-weight: 600;
        }

        h2 {
            color: var(--gc-dark);
            font-size: 36px;
            margin-bottom: 24px;
            line-height: 1.2;
            @media(max-width:767.98px) {
                font-size: 24px;
                margin-bottom: 12px;
            }
        }

        p {
            line-height: 1.6;
            font-size: 14.6px;
            margin-bottom: 18px;
        }

        ul.numbers {
            padding: 0;
            margin: 0;
            display: flex;
            column-gap: 24px;

            li {
                list-style-type: none;

                h3 {
                    font-size: 32px;
                    line-height: 1.2;
                    margin-bottom: 12px;
                    @media(max-width:767.98px) {
                        font-size: 24px;
                        margin-bottom: 0px;
                    }
                }

                p {
                    font-size: 14px;
                    line-height: 1.6;
                    margin-bottom: 18px;
                }
            }
        }
    }

}