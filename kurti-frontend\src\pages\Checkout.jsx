import { FormControl, InputLabel, MenuItem, Select, Chip } from "@mui/material";
import { useCartQuery } from "api/cart.api";
import { useCreateOrderMutation, usePlaceOrderMutation } from "api/order.api";
import CheckOutForm from "components/CheckOutForm";
import CouponModal from "components/modals/CouponModal";
import { useFormik } from "formik";
import {
  SETTINGS,
  addressInitialValues,
  addressValidationSchema,
} from "globals";
import parse from "html-react-parser";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { setCartItems } from "stores/cart";
import useUserStore from "stores/user";
import "../styles/free-shipping-progressbar.scss";
import { useAddressQuery } from "api/user.api";
import { preFillValues } from "utils";
import PriceWithStrikeAtom from "components/atoms/PriceWithStrike.atom";
import logo from "../assets/images/logo.png";
import { useGeneralSettingsQuery } from "api/util.api";

const Checkout = () => {
  const { token, user: userDetails } = useUserStore((state) => state.userInfo);
  const navigate = useNavigate();

  const { data: cartSavedData } = useCartQuery(token);
  const [paymentType, setPaymentType] = useState("Credit Card");
  const [showCouponModal, setShowCouponModal] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState(null);
  const [cartStoredData, setCartStoredData] = useState({});
  const [selectedAddress, setSelectedAddress] = useState({});

  const { mutateAsync: placeOrder } = usePlaceOrderMutation();
  const { mutateAsync: createOrder } = useCreateOrderMutation();

  const { data: addressItems = [] } = useAddressQuery();
  const { data: settingData = {} } = useGeneralSettingsQuery();
  const shippingPrice = paymentType === "Cash on Delivery" ? settingData?.codShipping : 0;

  const handleShow = () => {
    setShowCouponModal(true);
  };

  const handleApplyCoupon = (couponData) => {
    setAppliedCoupon(couponData);
  };

  const handleClose = () => {
    setShowCouponModal(false);
  };

  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
  };

  const handlePlaceOrder = async (payload) => {
    try {
      if(appliedCoupon?.code){
        payload.couponCode = appliedCoupon?.code
      }
      const response = await placeOrder(payload);
      if (response?.success) {
        setCartItems([]);
        toast.success("Order placed successfully");
        navigate("/dashboard");
      }
    } catch (error) {
      toast.error("Something went wrong, please try again later");
    }
  };

  const handlePayment = (orderInfo, values) => {
    if (!window.Razorpay) {
      console.error("Razorpay SDK failed to load.");
      return;
    }
    const options = {
      key: import.meta.env.VITE_RAZORPAY_KEY_ID,
      amount: orderInfo?.amount,
      currency: orderInfo?.currency,
      name: values?.fullName,
      description: "Order Transaction",
      image: logo,
      order_id: orderInfo?.id,
      handler: function (response) {
        const payload = {
          products: cartStoredData?.products || [],
          paymentMethod: paymentType,
          shippingAddress: values,
          status: "68",
          paymentInfo: response,
          totalShipping: 0
        };

        if (appliedCoupon?.code && appliedCoupon?.calculateDiscount) {
          payload.coupon = {
            code: appliedCoupon?.code,
            cDiscount: appliedCoupon?.calculateDiscount,
          };
        }
        handlePlaceOrder(payload);
      },
      prefill: {
        name: values?.fullName,
        email: values?.email,
        contact: values?.phone,
      },
      notes: {
        address: values?.addressLine1,
      },
      theme: {
        color: "#3399cc",
      },
    };

    const rzp1 = new window.Razorpay(options);
    rzp1.open();
  };

  const handleCreateOrder = async (values) => {
    try {
      const response = await createOrder({
        currency: "INR",
        amount: cartStoredData?.totalPrice || 0,
      });
      if (response?.success) {
        handlePayment(response?.data, values);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const formik = useFormik({
    initialValues: selectedAddress?.nickName
      ? {...preFillValues(addressInitialValues, selectedAddress), fullName: selectedAddress?.fName}
      : {
          ...addressInitialValues,
          fullName: userDetails.fullName,
          email: userDetails.email,
          phone: userDetails.phone,
        },
    enableReinitialize: true,
    validationSchema: addressValidationSchema,
    onSubmit: async (values) => {
      try {
        if (paymentType === "Cash on Delivery") {
          const payload = {
            products: cartStoredData?.products || [],
            paymentMethod: paymentType,
            shippingAddress: values,
            status: "68",
            totalShipping: shippingPrice
          };
          handlePlaceOrder(payload);
        } else {
          handleCreateOrder(values);
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  useEffect(() => {
    console.log(cartSavedData);
    if (cartSavedData) {
      setCartStoredData(cartSavedData);
    }
    if (appliedCoupon?.calculateDiscount) {
      setCartStoredData((prevData) => ({
        ...prevData,
        totalPrice:
          cartStoredData?.totalPrice - appliedCoupon?.calculateDiscount,
      }));
    }
  }, [cartSavedData, appliedCoupon]);

  const handleChangeAddress = (evt) => {
    setSelectedAddress(JSON.parse(evt.target.value));
  };

  const AddressList = () => {
    return (
      <div className="col">
        <h4 className="gc-form-title">Select address from saved list</h4>
        <FormControl fullWidth sx={{ marginBottom: "24px" }}>
          <InputLabel id="address-alias-label">Address Alias</InputLabel>
          <Select
            labelId="address-alias-label"
            id="address-alias-select"
            value={JSON.stringify(selectedAddress)}
            label="Address Alias"
            onChange={handleChangeAddress}
          >
            {addressItems?.length > 0 &&
              addressItems.map((alias) => (
                <MenuItem key={alias?._id} value={JSON.stringify(alias)}>
                  {alias.nickName}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
      </div>
    );
  };

  return (
    <>
      <div className="gc-shop-hero gc-page-hero page-hero-mini">
        <div className="container-xl gc-container-xl">
          <div className="row">
            <div className="col-12">
              <div className="gc-page-hero-content gc-flex gc-align-center gc-justify-center">
                <h2>Checkout</h2>
                <nav className="gc-breadcrumbs">
                  <ul className="gc-breadcrumb">
                    <li className="breadcrumb-item">
                      <a href="/" rel="home">
                        <span>Home</span>
                      </a>
                    </li>
                    <li className="breadcrumb-item active">
                      <span>Cart</span>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="nt-gc-inner-container pt-5">
        <div className="container-xl gc-container-xl">
          <div className=" justify-content-center">
            <div className="row col2-set  row-cols-1 row-cols-lg-2">
              <div className="col woocommerce-form-login">
                <div className="row col1-set">
                  {addressItems?.length > 0 ? <AddressList /> : null}
                </div>
                <div className="woocommerce-billing-fields ">
                  <h4 className="gc-form-title">Billing details</h4>
                  <CheckOutForm formik={formik} />
                </div>
              </div>

              <div className="col woocommerce-form-login">
                <div className="gc-order-review mt-5">
                  <h4 className="gc-form-title">Your order</h4>

                  <div className="woocommerce-checkout-review-order">
                    <div className="gc-checkout-review-order-table shop_table woocommerce-checkout-review-order-table">
                      {cartStoredData?.products?.length > 0 && (
                        <div className="gc-cart-items gc-flex">
                          <div className="cart-item-scroll">
                            {cartStoredData?.products?.map((product) => (
                              <div
                                className="gc-cart-item cart_item"
                                key={product?.productId?._id}
                              >
                                <div className="gc-product-name">
                                  <span className="product-img">
                                    <img
                                      width="113"
                                      height="150"
                                      src={product?.productId?.mainImage}
                                      className="attachment-thumbnail size-thumbnail"
                                      alt={product?.productId?.name}
                                    />
                                  </span>{" "}
                                  <span className="product-name">
                                    {product?.productId?.name}
                                  </span>{" "}
                                  <strong className="product-quantity">
                                    ×&nbsp;{product?.quantity}
                                  </strong>{" "}
                                </div>
                                <div className="gc-product-total product-total">
                                  <span className="woocommerce-Price-amount amount">
                                    <bdi>
                                      {/* <span className="woocommerce-Price-currencySymbol">
                                        {parse(SETTINGS.CURRENCY)}
                                      </span> */}
                                      <PriceWithStrikeAtom
                                        price={
                                          product?.gTotal ??
                                          product?.price * price?.quantity
                                        }
                                        sellingprice={
                                          product?.price * product?.quantity
                                        }
                                      />
                                      {/* {product?.price * product?.quantity} */}
                                    </bdi>
                                  </span>{" "}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      <div className="gc-checkout-review-order-footer gc-flex">
                        <div className="gc-checkout-footer-item cart-subtotal">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Subtotal</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <bdi>
                              <span className="woocommerce-Price-currencySymbol">
                                {parse(SETTINGS.CURRENCY)}
                              </span>
                              {cartStoredData?.gTotalPrice || 0}
                            </bdi>
                          </div>
                        </div>

                        <div className="gc-checkout-footer-item cart-shipping">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Discount</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <span className="woocommerce-Price-amount amount">
                              <bdi>
                                <span className="woocommerce-Price-currencySymbol">
                                  {parse(SETTINGS.CURRENCY)}
                                </span>
                                {cartStoredData?.totalDiscount || 0}
                              </bdi>
                            </span>
                          </div>
                        </div>
                        {paymentType === "Cash on Delivery" && <div className="gc-checkout-footer-item cart-shipping">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Shipping Fee</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <span className="woocommerce-Price-amount amount">
                              <bdi>
                                <span className="woocommerce-Price-currencySymbol">
                                  {parse(SETTINGS.CURRENCY)}
                                </span>
                                {shippingPrice || 0}
                              </bdi>
                            </span>
                          </div>
                        </div>}

                        <div className="gc-checkout-footer-item cart-subtotal">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Coupon Discount</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            {appliedCoupon?.code &&
                            appliedCoupon?.calculateDiscount ? (
                              <div className="mt-2 text-end">
                                <bdi>
                                  <span className="woocommerce-Price-currencySymbol">
                                    {parse(SETTINGS.CURRENCY)}
                                  </span>
                                  {appliedCoupon?.calculateDiscount}
                                </bdi>
                                <br />
                                <span
                                  style={{
                                    color: "rgb(108 94 188)",
                                  }}
                                  className="cursor-pointer"
                                  onClick={() => {
                                    setAppliedCoupon(null);
                                  }}
                                >
                                  Remove Coupon
                                </span>
                              </div>
                            ) : (
                              <span
                                className="woocommerce-Price-amount amount cursor-pointer"
                                style={{
                                  color: "rgb(108 94 188)",
                                }}
                                onClick={handleShow}
                              >
                                Apply Coupon
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="gc-checkout-footer-item order-total">
                          <div className="gc-checkout-footer-item-label">
                            <strong>Total</strong>
                          </div>
                          <div className="gc-checkout-footer-item-value">
                            <bdi>
                              <span className="woocommerce-Price-currencySymbol">
                                {parse(SETTINGS.CURRENCY)}
                              </span>
                              {cartStoredData?.totalPrice + shippingPrice || 0}
                            </bdi>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <h3 className="mt-4 fs-5">
                    Payment Method: &nbsp;&nbsp;
                    <strong>{paymentType}</strong>
                  </h3>

                  <div className="gc-checkout-footer-item cart-subtotal mt-4">
                    {/* <div className="gc-checkout-footer-item-label">
                      <strong>Change Payment Method</strong>
                    </div> */}
                    <div className="gc-checkout-footer-item-value">
                      <FormControl fullWidth>
                        <InputLabel id="payment-method-label">
                          Payment Method
                        </InputLabel>
                        <Select
                          labelId="payment-method-label"
                          id="payment-method-select"
                          value={paymentType}
                          label="Payment Method"
                          onChange={(event) =>
                            setPaymentType(event.target.value)
                          }
                        >
                          <MenuItem value="Credit Card">Credit Card</MenuItem>
                          <MenuItem value="Debit Card">Debit Card</MenuItem>
                          <MenuItem value="UPI">UPI</MenuItem>
                          <MenuItem value="Cash on Delivery">
                            Cash on Delivery
                          </MenuItem>
                        </Select>
                      </FormControl>
                    </div>
                  </div>

                  <div className="gc-order-footer mt-5">
                    <button
                      type="button"
                      className="text-light gc-btn gc-btn-medium gc-bg-primary single_add_to_cart_button"
                      onClick={formik.handleSubmit}
                    >
                      Place Order
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <CouponModal
        show={showCouponModal}
        handleClose={handleClose}
        handleSubmit={handleApplyCoupon}
        cartStoredData={cartStoredData}
      />
    </>
  );
};

export default Checkout;
