import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import "bootstrap/dist/css/bootstrap.min.css";
import { getFormattedDate } from "utils";
import Placeholder150 from "../assets/images/Placeholder-150.png";
import { useOrdersQuery } from "api/order.api";
import useUserStore from "stores/user";

const ResponsiveOrderList = () => {
  const token = useUserStore(state => state.userInfo.token)
  const { data, isLoading, isFetching } = useOrdersQuery({ token });

  const [items, setItems] = useState([]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Update state only when fresh data is available
  useEffect(() => {
    if (data?.items) {
      setItems(data.items);
    } else {
      setItems([]); // Ensure old cached data isn't shown when API returns empty
    }
  }, [data]);

  return (
    <div className="container">
      {/* Loading State */}
      {(isLoading || isFetching) && (
        <div className="text-center py-4">
          <div className="" role="status">
            <span>Loading...</span>
          </div>
        </div>
      )}

      {/* No Data Available State */}
      {!isLoading && !isFetching && items.length === 0 && (
        <div className="text-center py-4">
          <p className="text-muted">No data available.</p>
        </div>
      )}

      {/* Orders Table */}
      {!isLoading && !isFetching && items.length > 0 && (
        <>
          <div className="d-none d-md-flex row font-weight-bold border-bottom py-2">
            <div className="col-2">Image</div>
            <div className="col-2">Order Id</div>
            <div className="col-2">Date</div>
            <div className="col-2">Status</div>
            <div className="col-2">Total</div>
            <div className="col-2">Actions</div>
          </div>

          {items.map((item) => (
            <div className="row py-2 align-items-center border-bottom" key={item._id}>
              {/* Image */}
              <div className="col-12 col-md-2 text-center mb-2 mb-md-0">
                <img
                  loading="lazy"
                  decoding="async"
                  className="img-fluid"
                  src={item?.products[0]?.mainImage || Placeholder150}
                  alt="Product"
                  style={{ maxWidth: "100px", maxHeight: "120px" }}
                />
              </div>

              {/* Desktop View */}
              <div className="d-none d-md-block col-2">#{item?.orderId}</div>
              <div className="d-none d-md-block col-2">{getFormattedDate(item?.createdAt)}</div>
              <div className="d-none d-md-block col-2">{item?.statusText}</div>
              <div className="d-none d-md-block col-2">{item?.totalPrice}/-</div>
              <div className="d-none d-md-block col-2 text-center">
                <Link
                  to={`/view-order/${item._id}`}
                  className="btn btn-primary btn-sm"
                  style={{ backgroundColor: "#6c5ebc", color: "white", border: "none" }}
                >
                  Detail
                </Link>
              </div>

              {/* Mobile View */}
              <div className="d-md-none col-12 py-3">
                <div className="row">
                  <div className="col-6"><strong>Order Id:</strong></div>
                  <div className="col-6">#{item?.orderId}</div>
                </div>
                <div className="row">
                  <div className="col-6"><strong>Date:</strong></div>
                  <div className="col-6">{getFormattedDate(item?.createdAt)}</div>
                </div>
                <div className="row">
                  <div className="col-6"><strong>Status:</strong></div>
                  <div className="col-6">{item?.statusText}</div>
                </div>
                <div className="row">
                  <div className="col-6"><strong>Total:</strong></div>
                  <div className="col-6">{item?.totalPrice}/-</div>
                </div>
                <div className="row mt-2">
                  <div className="col-12 text-center">
                    <Link
                      to={`/view-order/${item._id}`}
                      className="btn btn-primary btn-sm"
                      style={{ backgroundColor: "#6c5ebc", color: "white", border: "none", padding: ".8rem 2rem" }}
                    >
                      Detail
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </>
      )}
    </div>
  );
};

export default ResponsiveOrderList;
