import React from 'react'
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import ProductTileOrganisms from 'components/organisms/ProductTile.organisms';

const getImage = (item) => {
  const imagesObj = {mainImage: '', secondImage: ''}

  const imagesArr = [item?.images?.mainImage, ...item?.images?.images];

  const filteredImages = imagesArr.filter(imageItem => imageItem?.mediaType === 'image');

  imagesObj.mainImage = filteredImages[0]?.url;
  imagesObj.secondImage = filteredImages[1]?.url || filteredImages[0]?.url;

  return imagesObj;
}

const ProductSliderTemplate = ({ products =[] }) => {
  return (
    <div className="gc-products">
    <Swiper
      //slidesPerView={5}
      breakpoints={{
        479: {
          slidesPerView: 1,
          spaceBetween: 15,
        },
        480: {
          slidesPerView: 2,
          spaceBetween: 15,
        },
        720: {
          slidesPerView: 3,
          spaceBetween: 20,
        },
        960: {
          slidesPerView: 4,
          spaceBetween: 20,
        },
        1200: {
          slidesPerView: 5,
          spaceBetween: 20,
        },
      }}
      loop={true}
      pagination={false}
      navigation={true}
      modules={[Pagination, Navigation]}
      className="swiper-wrapper"
    >
      {products?.length > 0 &&
        products.map((item, index) => {
          return (
            <SwiperSlide
              key={item._id}
              className="swiper-slide home-slider"
              data-swiper-autoplay="3000"
              data-anim-in="fadeInUp"
            >
              <ProductTileOrganisms
                key={item._id}
                pId={item?._id}
                mainImage={item?.mainImage || getImage(item?.attributes?.color?.[0])?.mainImage}
                secondImage={item?.images?.length > 0 ? item?.images[0] : getImage(item?.attributes?.color?.[0])?.secondImage}
                plink={item?.productLink}
                pname={item?.name}
                price={item?.prices?.b2cPrice?.originalPrice}
                sellingPrice={item?.prices?.b2cPrice?.sellingPrice}
                tag={
                  item?.attributes?.tags?.length > 0 &&
                  item?.attributes?.tags[0]
                }
                brand={item?.attributes?.brand}
                colors=""
                size=""
                productInfo={item}
              />
            </SwiperSlide>
          );
        })}
    </Swiper>
  </div>
  )
}

export default ProductSliderTemplate