import { useUpdateProfileMutation, useUserProfileQuery } from "api/user.api";
import { useFormik } from "formik";
import { myProfileValidationSchema } from "globals";
import { useRef, useState } from "react";
import { toast } from "react-toastify";
import useUserStore, {
  setTempAvatar,
  setUserAvatar,
  setUserInfo,
} from "stores/user";
import { preFillValues } from "utils";
import { handleFileUpload } from "utils/fileUpload";
import defaultAvatar from "../../assets/images/14.jpg";
import "./MyProfile.css";

const MyProfile = () => {
  const { data: profileInfo = {} } = useUserProfileQuery();
  const { mutateAsync: updateProfile } = useUpdateProfileMutation();
  const userInfo = useUserStore((state) => state.userInfo);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  const formik = useFormik({
    initialValues: preFillValues(
      {
        type: "PROFILE_UPDATE",
        fullName: "",
        phone: userInfo?.user?.phone || "",
        email: userInfo?.user?.email || "",
        avatar: userInfo?.user?.avatar || "",
        upiId: userInfo?.user?.upiId || "",
      },
      profileInfo
    ),
    enableReinitialize: true,
    validationSchema: myProfileValidationSchema,
    onSubmit: async (values) => {
      try {
        const result = await updateProfile(values);
        console.log(result);
        if (result?.success) {
          toast.success("Profile updated successfully!");
          setUserInfo({
            ...userInfo,
            user: {
              ...userInfo.user,
              email: result?.data?.email,
              fullName: result?.data?.fullName || values?.fullName,
              phone: result?.data?.phone || values?.phone,
              // avatar: result?.data?.avatar || values?.avatar,
              upiId: result?.data?.upiId || values?.upiId,
            },
          });
        } else {
          toast.error(result?.message);
        }
      } catch (err) {
        console.log(err);
        toast.error("Failed to update profile");
      }
    },
  });

  const renderError = (fieldName) => {
    if (formik.touched[fieldName] && formik.errors[fieldName]) {
      return <span className="error">{formik.errors[fieldName]}</span>;
    }
    return null;
  };

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.includes("image")) {
      toast.error("Please upload a valid image file");
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error("Image size should be less than 2MB");
      return;
    }

    try {
      setIsUploading(true);

      // Upload the file to Firebase Storage
      handleFileUpload(
        file,
        "users/avatars",
        (progress) => {
          setUploadProgress(progress);
        },
        async (downloadURL) => {
          // Update the form value with the download URL
          formik.setFieldValue("avatar", downloadURL);
          setUploadProgress(100);
          const result = await updateProfile({ type: "PROFILE_UPDATE",avatar: downloadURL });
          if (result?.success) {
            toast.success("Profile picture uploaded successfully");
            setUserInfo({
              ...userInfo,
              user: {
                ...userInfo.user,
                avatar: result?.data?.avatar || downloadURL,
              },
            });
          } else {
            toast.error(result?.message);
          }

          // Reset the file input and uploading state
          setIsUploading(false);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }
      );
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Error uploading profile picture");
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const disableFieldConfig = {
    email: true,
    phone: false,
  };
  return (
    <>
      <h4 className="anarkali-form-title">My Profile</h4>
      <div className="contact-form edit-account">
        <form onSubmit={formik.handleSubmit}>
          <div className="profile-picture-container mb-4">
            <div className="profile-picture-wrapper">
              <img
                src={formik.values.avatar || defaultAvatar}
                alt="Profile"
                className="profile-picture"
              />
              <div
                className="profile-picture-overlay"
                onClick={triggerFileInput}
              >
                <span>Change Photo</span>
              </div>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: "none" }}
              accept="image/*"
              onChange={handleFileChange}
            />
            {isUploading && (
              <div className="upload-progress">
                <div
                  className="progress-bar"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            )}
          </div>

          <div className="mb-4">
            <label>Full Name</label>
            <input
              type="text"
              className="form-control"
              placeholder="Full Name"
              name="fullName"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.fullName}
            />
            {renderError("fullName")}
          </div>
          <div className="mb-4">
            <label>Phone</label>
            <input
              type="text"
              className="form-control"
              placeholder="Phone"
              name="phone"
              disabled={disableFieldConfig.phone}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
            />
            {renderError("phone")}
          </div>
          <div className="mb-4">
            <label>Email</label>
            <input
              type="email"
              className="form-control"
              placeholder="Email"
              name="email"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
              disabled={disableFieldConfig.email}
            />
          </div>
          <div className="mb-4">
            <label>
              UPI ID{" "}
              <span className="text-muted">(Required for order returns)</span>
            </label>
            <input
              type="text"
              className="form-control"
              placeholder="username@upi"
              name="upiId"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.upiId || ""}
            />
            {renderError("upiId")}
          </div>
          <div className="mb-4">
            <input
              type="submit"
              value="Update Profile"
              className="gc-btn-medium gc-btn gc-btn-black"
              style={{
                backgroundColor: "rgb(108,94,188)",
                color: "white",
                border: "none",
              }}
            />
          </div>
        </form>
      </div>
    </>
  );
};

export default MyProfile;
