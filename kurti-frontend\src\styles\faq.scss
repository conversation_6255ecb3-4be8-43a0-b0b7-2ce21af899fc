.faq-page {
  .category-sidebar {
    background-color: #f8f8f8;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

    h4 {
      margin-bottom: 15px;
      font-weight: 600;
      color: var(--gc-dark);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
      padding-bottom: 10px;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        background-color: var(--gc-purple);
      }
    }

    .category-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: 10px 12px;
        cursor: pointer;
        border-radius: 4px;
        margin-bottom: 5px;
        transition: all 0.3s ease;
        border-left: 2px solid transparent;
        font-weight: 500;

        &:hover {
          background-color: rgba(237, 75, 75, 0.05);
          border-left-color: var(--gc-purple);
        }

        &.active {
          background-color: var(--gc-purple);
          color: white;
          border-left-color: var(--gc-purple);
        }
      }
    }
  }

  .faq-accordion {
    .accordion-item {
      margin-bottom: 15px;
      border-radius: 5px;
      overflow: hidden;
      border: 1px solid #e0e0e0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      }
    }

    .accordion-header {
      button {
        font-weight: 500;
        padding: 18px 20px;
        background-color: #f8f8f8;
        color: var(--gc-dark);
        transition: all 0.3s ease;
        position: relative;

        &:not(.collapsed) {
          background-color: var(--gc-purple);
          color: white;

          &::after {
            filter: brightness(0) invert(1)
          }
        }

        &:focus {
          box-shadow: none;
          border-color: var(--gc-purple);
        }
      }
    }

    .accordion-body {
      padding: 20px;
      line-height: 1.6;
      background-color: white;
      border-top: 1px solid #eee;
    }
  }

  .search-container {
    max-width: 600px;
    margin: 0 auto 40px;

    .input-group {
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
      border-radius: 5px;
      overflow: hidden;

      input {
        border: 1px solid #e0e0e0;
        padding: 12px 15px;
        font-size: 16px;

        &:focus {
          box-shadow: none;
          border-color: var(--gc-purple);
        }
      }

      .search-btn {
        background-color: var(--gc-purple);
        border-color: var(--gc-purple);
        color: white;
        padding: 0 25px;
        transition: all 0.3s ease;
        font-weight: 500;

        &:hover, &:focus {
          background-color: var(--gc-purple);
          border-color: var(--gc-purple);
          color: white;
        }
      }
    }
  }

  .no-faqs-found {
    text-align: center;
    padding: 40px;
    background-color: #f8f8f8;
    border-radius: 5px;
    font-size: 18px;
    color: var(--gc-dark-soft);
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  h1.text-center {
    margin-bottom: 40px;
    color: var(--gc-dark);
    position: relative;
    padding-bottom: 15px;
    display: inline-block;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background-color: var(--gc-purple);
    }
  }
}
