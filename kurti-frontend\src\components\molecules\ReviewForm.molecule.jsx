import React, { useState } from 'react';
import { Button, Form } from 'react-bootstrap';
import { Rating } from '@mui/material';
import { useAddReviewMutation, useUpdateReviewMutation } from 'api/review.api';
import { toast } from 'react-toastify';
import useUserStore from 'stores/user';
import { useGetAdminSettingsQuery } from 'api/settings.api';
import { useQueryClient } from '@tanstack/react-query';

const ReviewForm = ({ productId, reviewId = null, initialRating = 0, initialComment = '', onSuccess }) => {
  const [rating, setRating] = useState(initialRating);
  const [comment, setComment] = useState(initialComment);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get the query client for cache invalidation
  const queryClient = useQueryClient();

  // Get admin settings to check if reviews are enabled
  const { data: adminSettings, isError: isSettingsError } = useGetAdminSettingsQuery();
  // Default to false if settings API fails or enableReviews is not explicitly set to true
  // Use strict equality to ensure we're checking for exactly true or false
  const reviewsEnabled = !isSettingsError && adminSettings?.enableReviews === true;

  const { mutateAsync: addReview } = useAddReviewMutation();
  const { mutateAsync: updateReview } = useUpdateReviewMutation();
  const token = useUserStore((state) => state.userInfo.token);

  // If reviews are disabled, don't render anything
  if (reviewsEnabled !== true) {
    return null;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check again if reviews are enabled
    if (reviewsEnabled !== true) {
      toast.error('Reviews are currently disabled');
      return;
    }

    if (!token) {
      toast.error('Please log in to submit a review');
      return;
    }

    if (rating === 0) {
      toast.error('Please select a rating');
      return;
    }

    if (comment.trim().length < 3) {
      toast.error('Please enter a comment (minimum 3 characters)');
      return;
    }

    setIsSubmitting(true);

    try {
      if (reviewId) {
        // Update existing review
        await updateReview({
          productId,
          reviewId,
          rating,
          comment
        });
        toast.success('Review updated successfully');
      } else {
        // Add new review
        await addReview({
          productId,
          rating,
          comment
        });
        toast.success('Review submitted successfully');
        // Reset form after successful submission
        setRating(0);
        setComment('');
      }

      // Invalidate and refetch queries to ensure data is updated
      await queryClient.invalidateQueries({
        queryKey: ['product-info', productId],
        exact: true
      });

      await queryClient.invalidateQueries({
        queryKey: ['product-reviews', productId],
        exact: false // Allow invalidating all product reviews queries for this product
      });

      // Force a refetch of the product reviews
      await queryClient.refetchQueries({
        queryKey: ['product-reviews', productId],
        exact: false
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      toast.error(error?.response?.data?.message || 'Failed to submit review');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="review-form-container">
      <h4>{reviewId ? 'Edit Your Review' : 'Write a Review'}</h4>
      <Form onSubmit={handleSubmit}>
        <Form.Group className="mb-3">
          <Form.Label>Rating</Form.Label>
          <div>
            <Rating
              value={rating}
              onChange={(event, newValue) => {
                setRating(newValue);
              }}
              precision={1}
              size="large"
            />
          </div>
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label>Your Review</Form.Label>
          <Form.Control
            as="textarea"
            rows={4}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Share your experience with this product..."
            required
            minLength={3}
            maxLength={1000}
          />
          <Form.Text className="text-muted">
            {comment.length}/1000 characters
          </Form.Text>
        </Form.Group>

        <Button
          type="submit"
          className="gc-btn gc-btn-medium gc-bg-primary"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : (reviewId ? 'Update Review' : 'Submit Review')}
        </Button>
      </Form>
    </div>
  );
};

export default ReviewForm;
