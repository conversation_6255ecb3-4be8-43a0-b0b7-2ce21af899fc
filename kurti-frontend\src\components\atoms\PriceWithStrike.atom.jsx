import React from "react";

const PriceWithStrikeAtom = ({ price, sellingprice }) => {
  return (
    <div className="gc-price-rating gc-inline-two-block gc-sm-flex-column">
      <div className="gc-block-left">
        <span className="gc-price price">
          <span className="gc-primary-color del">
            <span>
              <span className="price-amount amount original-price">
                <bdi>
                  <span className="price-currencySymbol">₹</span>
                  {price}
                </bdi>
              </span>
            </span>
            –
          </span>
          <span className="gc-primary-color ins">
            <span className="price-amount amount discounted-price">
              <bdi>
                <span className="price-currencySymbol">₹</span>
                {sellingprice}
              </bdi>
            </span>
          </span>
        </span>
      </div>
      <div className="gc-block-right"></div>
    </div>
  );
};

export default PriceWithStrikeAtom;
