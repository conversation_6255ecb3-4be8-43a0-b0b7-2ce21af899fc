import { firebaseStorage } from "config/firebaseConfig";
import { getDownloadURL, ref, uploadBytesResumable } from "firebase/storage";

/**
 * Uploads a file to Firebase Storage
 * @param {File} file - The file to upload
 * @param {string} folderPath - The folder path in Firebase Storage
 * @param {Function} onUploadProgress - Callback function for upload progress
 * @param {Function} onUploadComplete - Callback function when upload is complete
 * @returns {Promise<void>}
 */
export const handleFileUpload = async (
  file,
  folderPath,
  onUploadProgress,
  onUploadComplete
) => {
  try {
    const storage = firebaseStorage;
    
    // Create a unique filename to prevent overwriting
    const fileName = `${Date.now()}_${file.name}`;
    const storageRef = ref(storage, `${folderPath}/${fileName}`);

    const uploadTask = uploadBytesResumable(storageRef, file);

    uploadTask.on(
      "state_changed",
      (snapshot) => {
        const progress =
          (snapshot.bytesTransferred / snapshot.totalBytes) * 100;

        if (onUploadProgress) {
          onUploadProgress(progress);
        }
      },
      (error) => {
        console.error("Error uploading file:", error);
        throw error;
      },
      async () => {
        const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
        if (onUploadComplete) {
          onUploadComplete(downloadURL);
        }
      }
    );
  } catch (error) {
    console.error("Error uploading file:", error);
    throw error;
  }
};
