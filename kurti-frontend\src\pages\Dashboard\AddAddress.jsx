import { Box } from "@mui/material";
import {
  useAddAddressMutation,
  useAddressInfoQuery,
  useUpdateAddressMutation,
} from "api/user.api";
import { useFormik } from "formik";
import { addressInitialValues, addressValidationSchema } from "globals";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import useUserStore from "stores/user";
import { preFillValues } from "utils";
import "./Address.css";

const AddAddress = ({ setValue }) => {
  const [searchParams] = useSearchParams();
  const addressId = searchParams.get("addressId");
  const { mutateAsync: addAddress } = useAddAddressMutation();
  const { mutateAsync: updateAddress } = useUpdateAddressMutation();
  const { data: addressInfo = {} } = useAddressInfoQuery(addressId);
  const navigate = useNavigate();
  const userDetails = useUserStore((state) => state.userInfo.user);

  const renderError = (fieldName) => {
    if (formik.touched[fieldName] && formik.errors[fieldName]) {
      return <span className="error">{formik.errors[fieldName]}</span>;
    }
    return null;
  };

  const formik = useFormik({
    initialValues:
      addressId && addressInfo?._id
        ? preFillValues(addressInitialValues, addressInfo)
        : {
            ...addressInitialValues,
            fullName: userDetails.fullName,
            email: userDetails.email,
            phone: userDetails.phone,
          },
    enableReinitialize: true,
    validationSchema: addressValidationSchema,
    onSubmit: async (values) => {
      try {
        let result = null;
        if (addressId) {
          result = await updateAddress({
            id: addressId,
            payload: values,
          });
        } else {
          result = await addAddress(values);
        }
        if (result?.success) {
          formik.resetForm();
          toast.success(
            `Address ${addressId ? "updated" : "added"} successfully!`
          );
          if (addressId) {
            navigate("/dashboard");
          } else {
            setValue(1);
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
  });
  return (
    <div className="add-address-container">
      <h4 className="add-address-title">
        {addressId ? "Edit" : "Add New"} Address
      </h4>
      <p className="mb-4">The following address will be used on the checkout page.</p>

      <form className="add-address-form" onSubmit={formik.handleSubmit}>
        <div className="row">
          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="nickName">
                Address Type <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="nickName"
                name="nickName"
                placeholder="Home, Office, etc."
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.nickName}
              />
              {renderError("nickName")}
            </div>
          </div>

          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="fullName">
                Full Name <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                placeholder="Enter your full name"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.fullName}
              />
              {renderError("fullName")}
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="addressLine1">
                Address <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="addressLine1"
                name="addressLine1"
                placeholder="Enter your address"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.addressLine1}
              />
              {renderError("addressLine1")}
            </div>
          </div>

          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="phone">
                Phone <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="phone"
                name="phone"
                placeholder="Enter your phone number"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.phone}
              />
              {renderError("phone")}
            </div>
          </div>

          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="email">
                Email Address <span className="text-danger">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Enter your email address"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.email}
              />
              {renderError("email")}
            </div>
          </div>
          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="country">
                Country <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="country"
                name="country"
                placeholder="Enter your country"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.country}
              />
              {renderError("country")}
            </div>
          </div>

          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="state">
                State <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="state"
                name="state"
                placeholder="Enter your state"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.state}
              />
              {renderError("state")}
            </div>
          </div>

          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="city">
                City <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="city"
                name="city"
                placeholder="Enter your city"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.city}
              />
              {renderError("city")}
            </div>
          </div>

          <div className="col-md-6">
            <div className="form-row">
              <label htmlFor="postalCode">
                Postal Code <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                id="postalCode"
                name="postalCode"
                placeholder="Enter your postal code"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.postalCode}
              />
              {renderError("postalCode")}
            </div>
          </div>

          <div className="col-12 mt-4">
            <button
              type="submit"
              className="btn-submit"
            >
              {addressId ? "Update" : "Save"} Address
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AddAddress;
