const mongoose = require('mongoose'),
    Schema = mongoose.Schema;

const AdminSettingsSchema = new Schema(
    {
        androidAppVersion: {
            type: String,
            trim: true,
            required: true,
        },
        iosAppVersion: {
            type: String,
            trim: true,
            required: true,
        },
        androidForceUpdate: {
            type: Boolean,
            default: true,
        },
        iosForceUpdate: {
            type: Boolean,
            default: true,
        },
        maintenance: {
            type: Boolean,
            default: true,
        },
        homePageBanner: [{
            image: {
                type: String,
                trim: true,
                required: false,
                default: ''
            },
            redirectURL: {
                type: String,
                trim: true,
                required: false,
                default: ''
            },
        }],
        advertisementBanner: [{
            image: {
                type: String,
                trim: true,
                required: false,
                default: ''
            },
            redirectURL: {
                type: String,
                trim: true,
                required: false,
                default: ''
            },
        }],
        address: {
            type: String,
            trim: true,
            required: false,
        },
        email: {
            type: String,
            trim: true,
            lowercase: true,
            required: false,
        },
        contact: {
            type: String,
            trim: true,
            required: false,
        },
        codShipping: {
            type: Number,
            default:0
        },
        packageWeight: {
            type: Number,
            default:0
        },
        googleApiKey: {
            type: String,
            trim: true,
            required: false,
        },
        googleLat: {
            type: Number,
            required: false,
        },
        googleLong: {
            type: Number,
            required: false,
        },
        commonMetaTags: [],
        metaTitle: {
            type: String,
            default: ''
        },
        metaDescription: {
            type: String,
            default: ''
        },
        socialLinks: {
            type: Map,
            of: String,
            required: false,
        },
        enableReviews: {
            type: Boolean,
            default: true,
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('AdminSettings', AdminSettingsSchema);
