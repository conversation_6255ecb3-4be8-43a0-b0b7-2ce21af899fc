import {
  <PERSON>complete,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>Field,
  Typography,
  useTheme
} from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import FileUploadProgress from "components/FileUploadProgress";
import Header from "components/Header";
import {
  categoryInitialValues,
  categoryValidationSchema,
} from "constants/schemas";
import { useFormik } from "formik";
import { useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  useCreateCategoryMutation,
  useGetCategoriesQuery,
  useGetCategoryInfoQuery,
  useUpdateCategoryMutation,
} from "services";
import { handleFileUpload } from "utils/common";
import dataGridStyles from "../../styles/dataGridStyles";

const Add = () => {
  const fileRef = useRef(null);
  const isNonMobile = useMediaQuery("(min-width:600px)");
  const [adBannerData, setAdBannerData] = useState({
    redirectURL: "",
    image: "",
  });

  const { id: categoryId } = useParams();

  const theme = useTheme();
  const styles = dataGridStyles(theme.palette.mode);
  // hooks
  const navigate = useNavigate();

  // state
  const [uploadProgress, setUploadProgress] = useState(0);

  // query
  const { data: categories } = useGetCategoriesQuery();
  const { data: categoryData } = useGetCategoryInfoQuery(categoryId, {
    skip: !categoryId,
    refetchOnMountOrArgChange: true,
  });

  // mutation
  const [createCategory] = useCreateCategoryMutation();
  const [updateCategory] = useUpdateCategoryMutation();

  const savedCategoryValues = {
    name: categoryData?.name || "",
    parentId: categoryData?.parentId || undefined,
    image: categoryData?.image || "",
    description: categoryData?.description || "",
    sizeChart: categoryData?.sizeChart || "",
    adBanner: categoryData?.adBanner || [],
  };

  const formik = useFormik({
    initialValues: { ...categoryInitialValues, ...savedCategoryValues },
    enableReinitialize: true,
    validationSchema: categoryValidationSchema,
    onSubmit: (values) => {
      if (categoryId) {
        updateCategory({
          id: categoryId,
          body: values,
        })
          .unwrap()
          .then((result) => {
            if (result?._id) {
              setUploadProgress(0);
              navigate("/categories");
            }
          });
      } else {
        createCategory(values)
          .unwrap()
          .then((result) => {
            if (result?._id) {
              formik.resetForm();
              setUploadProgress(0);
              navigate("/categories");
            }
          });
      }
    },
  });

  const handleFileChange = (evt) => {
    const file = evt.target.files[0];
    const fieldName = evt.target.name || "image";
    if (file) {
      handleFileUpload(
        file,
        "categories",
        (progress) => {
          setUploadProgress({ ...uploadProgress, [fieldName]: progress });
        },
        (url) => {
          formik.setFieldValue(fieldName, url || "");
        }
      );
    }
  };

  const filteredCategories = useMemo(() => {
    if (categories?.length) {
      return categories.filter((item) => item._id !== categoryId);
    }
  }, [categories]);

  const handleAdFileChange = (evt, type) => {
    const file = evt.target.files[0];
    if (file) {
      handleFileUpload(
        file,
        "general",
        (progress) => {
          setUploadProgress((prevUploadProgress) => ({
            ...prevUploadProgress,
            [type]: progress,
          }));
        },
        (url) => {
          setAdBannerData({
            ...adBannerData,
            image: url,
          });
        }
      );
    }
  };

  const handleRemoveAdImage = (idx) => {
    const newImages = [...formik.values.adBanner];
    newImages.splice(idx, 1);
    formik.setFieldValue("adBanner", newImages);
  };

  const onChangeAdBanner = (evt) => {
    setAdBannerData({
      ...adBannerData,
      redirectURL: evt.target.value,
    });
  };

  const onAddAdBanner = () => {
    const tempBanner = JSON.parse(JSON.stringify(adBannerData));
    formik.setFieldValue(
      "adBanner",
      formik.values.adBanner?.length <= 0
        ? [tempBanner]
        : [...formik.values.adBanner, tempBanner]
    );
    setAdBannerData({
      redirectURL: "",
      image: "",
    });
    setUploadProgress({});
    if (fileRef.current) {
      fileRef.current.value = "";
    }
    setUploadProgress({ adBanners: "" });
  };

  return (
    <Box m="20px">
      <Box sx={styles.mainHeadings}>
        <Header
          title={`${categoryId ? "EDIT" : "CREATE"} CATEGORY`}
          subtitle={`${categoryId ? "Edit Category" : "Create a New Category"}`}
        />
        {categoryId && (
          <Box display="flex" justifyContent="end" mt="20px">
            <Button
              onClick={() => navigate("/categories")}
              color="secondary"
              variant="contained"
              sx={styles.buttonMd}
            >
              Back to Categories
            </Button>
          </Box>
        )}
      </Box>
      <form onSubmit={formik.handleSubmit}>
        <Box
          sx={{
            ...styles.formContainer,
          }}
          className="extra-space"
        >
          <Box
            display="grid"
            gap="30px"
            gridTemplateColumns="repeat(4, minmax(0, 1fr))"
            sx={{
              "& > div": {
                gridColumn: isNonMobile ? undefined : "span 4",
              },
            }}
          >
            <TextField
              fullWidth
              variant="filled"
              type="text"
              label="Name"
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.name}
              name="name"
              error={!!formik.touched.name && !!formik.errors.name}
              helperText={formik.touched.name && formik.errors.name}
              sx={{ gridColumn: "span 4" }}
              className="input"
            />
            <Autocomplete
              options={filteredCategories}
              getOptionLabel={(option) => option.name}
              onBlur={formik.handleBlur}
              className="autocomplete"
              onChange={(_, value) =>
                formik.setFieldValue("parentId", value?._id)
              }
              name="parentId"
              sx={{ gridColumn: "span 4" }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  variant="filled"
                  label="Parent Category (If Any)"
                />
              )}
              value={
                filteredCategories?.length > 0
                  ? filteredCategories?.find(
                      (cat) => cat._id === formik.values.parentId
                    )
                  : null
              }
            />
            <TextField
              fullWidth
              variant="filled"
              type="file"
              onBlur={formik.handleBlur}
              onChange={handleFileChange}
              name="image"
              error={!!formik.errors.image}
              helperText={formik.errors.image}
              sx={{ gridColumn: "span 4" }}
              InputProps={{
                accept: "image/*",
                endAdornment: (
                  <FileUploadProgress progress={uploadProgress?.image} />
                ),
              }}
              InputLabelProps={{ shrink: true }}
              className="input"
              label="Image"
            />
            {formik.values.image && (
              <img
                src={formik.values.image}
                alt="Main"
                sx={{ gridColumn: "span 4" }}
                width={100}
                height={100}
              />
            )}
            <TextField
              fullWidth
              variant="filled"
              type="text"
              label="Description"
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.description}
              name="description"
              error={
                !!formik.touched.description && !!formik.errors.description
              }
              helperText={
                formik.touched.description && formik.errors.description
              }
              sx={{ gridColumn: "span 4" }}
              className="input"
            />
            <TextField
              fullWidth
              variant="filled"
              type="file"
              onBlur={formik.handleBlur}
              onChange={handleFileChange}
              name="sizeChart"
              error={!!formik.errors.sizeChart}
              helperText={formik.errors.sizeChart}
              sx={{ gridColumn: "span 4" }}
              InputProps={{
                accept: "image/*",
                endAdornment: (
                  <FileUploadProgress progress={uploadProgress?.sizeChart} />
                ),
              }}
              InputLabelProps={{ shrink: true }}
              className="input"
              label="Size Chart"
            />
            {formik.values.sizeChart && (
              <img
                src={formik.values.sizeChart}
                alt="Main"
                sx={{ gridColumn: "span 4" }}
                width={100}
                height={100}
              />
            )}
            {/* Advertisement Banner */}
            <Typography variant="h4" sx={{ gridColumn: "span 4" }}>
              Advertisement Banners:
            </Typography>
            <TextField
              fullWidth
              variant="filled"
              type="text"
              label="Redirect URL"
              onChange={onChangeAdBanner}
              name="redirectURL"
              sx={{ gridColumn: "span 2" }}
              className="input"
              InputLabelProps={{ shrink: true }}
              value={adBannerData?.redirectURL}
            />
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gridColumn: "span 2",
              }}
            >
              <TextField
                key={fileRef?.current?.value}
                fullWidth
                variant="filled"
                type="file"
                onChange={(evt) => handleAdFileChange(evt, "adBanners")}
                name="ad-image"
                sx={{ gridColumn: "span 2" }}
                className="input"
                InputProps={{
                  accept: "image/*",
                  endAdornment: (
                    <FileUploadProgress progress={uploadProgress?.adBanners} />
                  ),
                }}
                InputLabelProps={{ shrink: true }}
                label="Banner Image"
                ref={fileRef}
              />
              <small style={{ fontWeight: 700 }}>
                Advertisement Banner image's resolution should be 1920 x 950
              </small>
            </Box>
            <Box
              sx={{
                display: "flex",
                flexDirection: "row-reverse",
                gridColumn: "span 4",
              }}
            >
              <Button
                type="button"
                color="secondary"
                variant="contained"
                sx={{ ...styles.buttonMd }}
                onClick={onAddAdBanner}
                disabled={!adBannerData?.image || !adBannerData?.redirectURL}
              >
                Add Advertisement Banner
              </Button>
            </Box>

            <Box
              sx={{
                gridColumn: "span 4",
                display: "flex",
                gap: 2,
                overflow: "auto",
                flexWrap: "wrap",
              }}
            >
              {formik.values.adBanner?.length > 0 &&
                formik.values.adBanner.map((adBannerItem, idx) => {
                  return (
                    <Box
                      key={idx}
                      sx={{
                        position: "relative",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        width: "100%",
                        maxWidth: { xs: "100%", sm: "48%", md: "32%" },
                        marginBottom: 2,
                      }}
                    >
                      <img
                        src={adBannerItem?.image}
                        alt="banner"
                        style={{
                          width: "100%",
                          maxHeight: 300,
                          height: "auto",
                          display: "block",
                          borderRadius: 4,
                          objectFit: "fill",
                        }}
                      />
                      <Button
                        className="cursor-pointer"
                        onClick={() => handleRemoveAdImage(idx)}
                        sx={{
                          position: "absolute",
                          top: 8,
                          right: 8,
                          background: "rgba(255, 255, 255, 0.8)",
                          borderRadius: "50%",
                          minWidth: "auto",
                          padding: 0,
                          width: 24,
                          height: 24,
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        X
                      </Button>
                      <Typography
                        component="span"
                        sx={{
                          display: "block",
                          textAlign: "center",
                          paddingTop: 1,
                        }}
                      >
                        {adBannerItem?.redirectURL}
                      </Typography>
                    </Box>
                  );
                })}
            </Box>
          </Box>
          <Box display="flex" justifyContent="end" mt="20px">
            <Button
              type="submit"
              color="secondary"
              variant="contained"
              sx={styles.buttonMd}
            >
              {categoryData ? "Update Category" : "Create Category"}
            </Button>
          </Box>
        </Box>
      </form>
    </Box>
  );
};

export default Add;
