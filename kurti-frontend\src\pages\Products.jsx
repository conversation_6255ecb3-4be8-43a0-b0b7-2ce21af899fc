import { FormControl, MenuItem, Select } from "@mui/material";
import { useProductsQuery } from "api/product.api";
import FilterProducts from "components/FilterProducts";
import CustomPagination from "components/atoms/CustomPagination";
import CustomLoadingOverlay from "components/molecules/CustomLoadingOverlay";
import ProductTileBtnsMolecule from "components/molecules/ProductTileBtns.molecule";
import { useEffect, useMemo, useState } from "react";
import { Link, useSearchParams } from "react-router-dom";
import { getAllSearchParams } from "utils";

const Products = () => {
  const [activeFilterMenu, setActiveFilterMenu] = useState(false);
  const [params, setParams] = useState({
    page: 1,
    limit: 30,
  });
  const [searchParams] = useSearchParams();
  const allSearchParams = getAllSearchParams(searchParams);
  const [products, setProducts] = useState([]);
  const [sortOrder, setSortOrder] = useState("default");

  const {
    data: { items = [], count } = {},
    refetch,
    isSuccess,
    isLoading,
    isFetching,
  } = useProductsQuery({ ...params, ...allSearchParams });

  const handleFilterMenu = () => {
    setActiveFilterMenu(!activeFilterMenu);
  };

  const handleSetPage = (page) => {
    setParams({
      ...params,
      page,
    });
  };

  useEffect(() => {
    window.scrollTo(0, 0);
    refetch();
  }, [params]);

  useEffect(() => {
    if (isSuccess) {
      setProducts(items);
    } else {
      setProducts([]);
    }
  }, [items, isSuccess]);

  const handleChangeSortBy = (event) => {
    setSortOrder(event.target.value);
  };

  const handleSortProducts = (data, sortOrder) => {
    return data.slice().sort((a, b) => {
      switch (sortOrder) {
        case "date":
          return new Date(a.created) - new Date(b.created);

        case "lowtohigh":
          return (
            a.prices.b2cPrice.sellingPrice - b.prices.b2cPrice.sellingPrice
          );

        case "hightolow":
          return (
            b.prices.b2cPrice.sellingPrice - a.prices.b2cPrice.sellingPrice
          );

        case "default":
        default:
          return 0;
      }
    });
  };

  const sortedData = useMemo(
    () => handleSortProducts(products, sortOrder),
    [sortOrder, products, isSuccess]
  );

  return (
    <>
      <div className="gc-shop-hero gc-page-hero page-hero-mini">
        <div className="container-xl gc-container-xl">
          <div className="row">
            <div className="col-12">
              <div className="gc-page-hero-content gc-flex gc-align-center gc-justify-center">
                <h2>Shop</h2>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="nt-gc-inner-container shop-area section-padding">
        <div className="container-xl gc-container-xl">
          <div className="row justify-content-between">
            <div
              id="nt-sidebar"
              className={`nt-sidebar default-sidebar col-lg-3 ${
                activeFilterMenu ? "active" : ""
              }`}
            >
              <div
                className="gc-panel-close-button gc-close-sidebar"
                onClick={handleFilterMenu}
              ></div>
              <div className="nt-sidebar-inner-wrapper">
                <div className="nt-sidebar-inner gc-scrollbar">
                  <FilterProducts />
                </div>
              </div>
            </div>

            <div className="col-lg-9 gc-products-column">
              <div className="gc-inline-two-block gc-before-loop gc-shop-filter-top-area">
                <div className="gc-block-left"></div>
                <div className="gc-block-right">
                  <div
                    className="gc-open-fixed-sidebar"
                    onClick={handleFilterMenu}
                  >
                    <span>Filter</span>{" "}
                    <svg
                      className="svgFilter gc-svg-icon"
                      height="512"
                      viewBox="0 0 32 32"
                      width="512"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g fill="rgb(0,0,0)">
                        <path d="m1.917 24.75h17.333v2h-17.333z"></path>
                        <path d="m23.5 22.5h-2v6.5h2v-2.25h6.583v-2h-6.583z"></path>
                        <path d="m12.75 15h17.333v2h-17.333z"></path>
                        <path d="m8.5 19.25h2v-6.5h-2v2.25h-6.583v2h6.583z"></path>
                        <path d="m1.917 5.25h17.333v2h-17.333z"></path>
                        <path d="m23.5 5.25v-2.25h-2v6.5h2v-2.25h6.583v-2z"></path>
                      </g>
                    </svg>
                  </div>
                  <div className="gc-shop-filter-area gc-filter-ordering-area">
                    <div className="gc-shop-filter-ordering gc-shop-filter-item">
                      <form method="get">
                        <FormControl
                          fullWidth
                          sx={{
                            paddingTop: 1,
                            maxHeight: "30px",
                            marginBottom: "20px",
                          }}
                        >
                          <Select
                            labelId="sort-select-label"
                            id="sort-select"
                            value={sortOrder}
                            onChange={handleChangeSortBy}
                          >
                            <MenuItem value="default">Default sorting</MenuItem>
                            <MenuItem value="date">Sort by latest</MenuItem>
                            <MenuItem value="lowtohigh">
                              Sort by price: low to high
                            </MenuItem>
                            <MenuItem value="hightolow">
                              Sort by price: high to low
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </form>
                    </div>
                  </div>
                </div>
              </div>{" "}
              <div className="gc-choosen-filters-row row gc-hidden-on-mobile">
                <div className="col-12"></div>
              </div>
              <div className="gc-products-wrapper">
                <div className="shop-data-filters"></div>
                {sortedData?.length > 0 ? (
                  <>
                    <div className="gc-products products row row-cols-2 row-cols-sm-3 row-cols-lg-3 row-cols-xl-3">
                      {sortedData?.map((item) => {
                        return (
                          <div
                            className="gc-loop-product gc-product-type-3 nt-post-className product type-product post-13243 status-publish first instock product_cat-men product_cat-men-t-shirts product_tag-festive-wear product_tag-men-t-shirts has-post-thumbnail sale taxable shipping-taxable purchasable product-type-simple"
                            key={item?._id}
                          >
                            <div className=" gc-product">
                              <div className="gc-thumb-wrapper">
                                <Link
                                  to={`/product-details/${item?._id}`}
                                  className="product-link has-images"
                                >
                                  <img
                                    loading="lazy"
                                    width="450"
                                    height="600"
                                    src={
                                      item?.mainImage ||
                                      item?.attributes?.color?.[0]?.images?.mainImage?.url
                                    }
                                    className="attachment-woocommerce_thumbnail size-woocommerce_thumbnail"
                                    alt=""
                                    decoding="async"
                                  />
                                  <img
                                    loading="lazy"
                                    width="450"
                                    height="600"
                                    src={
                                      item?.images?.length > 0
                                        ? item?.images[0]
                                        : item?.attributes?.color?.[0]?.images?.images[0]?.url
                                    }
                                    className="overlay-thumb  is-shop"
                                    alt=""
                                    decoding="async"
                                  />
                                </Link>
                                <div className="gc-product-labels">
                                  <span className="gc-label gc-badge badge-def">
                                    {item?.attributes?.tags?.length > 0 &&
                                      item?.attributes?.tags[0]}
                                  </span>
                                  <span className="gc-label gc-discount">
                                    {item?.attributes?.brand}
                                  </span>
                                </div>
                                <div className="gc-loop-product-buttons-hover">
                                  <ProductTileBtnsMolecule
                                    pId={item?._id}
                                    plink={item?.productLink}
                                    productInfo={item}
                                  />
                                </div>
                              </div>
                              <div className="gc-loop-product-buttons-mobile gc-mini-icon">
                                <div className="gc-add-to-cart-btn gc-product-button type-simple">
                                  <a
                                    href="?add-to-cart=13243"
                                    className="gc-btn-small product_type_simple add_to_cart_button gc_ajax_add_to_cart"
                                  ></a>
                                  <svg
                                    className="svgaddtocart gc-svg-icon"
                                    width="512"
                                    height="512"
                                    fill="currentColor"
                                    viewBox="0 0 32 32"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <use href="#shopBag"></use>
                                  </svg>
                                </div>
                                <div className="gc-quickview-btn gc-product-button">
                                  <svg
                                    className="quickview gc-svg-icon"
                                    width="512"
                                    height="512"
                                    fill="currentColor"
                                    viewBox="0 0 32 32"
                                  >
                                    <use href="#shopEye"></use>
                                  </svg>
                                </div>
                                <div className="gc-compare-btn gc-product-button">
                                  <svg
                                    className="svgCompare gc-svg-icon"
                                    width="512"
                                    height="512"
                                    fill="currentColor"
                                    viewBox="0 0 32 32"
                                  >
                                    <use href="#shopCompare"></use>
                                  </svg>
                                </div>
                                <div className="gc-wishlist-btn gc-product-button">
                                  <svg
                                    className="svgwishlist gc-svg-icon"
                                    width="512"
                                    height="512"
                                    fill="currentColor"
                                    viewBox="0 0 32 32"
                                  >
                                    <use href="#shopLove"></use>
                                  </svg>
                                </div>
                              </div>
                              <div className="gc-title-cart-hover gc-title-block">
                                <h6 className="gc-product-name">
                                  <a href="https://none.com/themes/anarkali/product/Product/">
                                    {item?.name}
                                  </a>
                                </h6>
                              </div>
                              <div className="gc-price-rating gc-inline-two-block gc-sm-flex-column">
                                <div className="gc-block-left">
                                  <span className="gc-price price">
                                    <span className="gc-primary-color del">
                                      <span>
                                        <span className="price-amount amount original-price">
                                          <bdi>
                                            <span className="woocommerce-Price-currencySymbol">
                                              &#8377;
                                            </span>
                                            {item.prices.b2cPrice.originalPrice}
                                          </bdi>
                                        </span>
                                      </span>{" "}
                                      –{" "}
                                    </span>{" "}
                                    <span className="gc-primary-color ins">
                                      <span className="price-amount amount discounted-price">
                                        <bdi>
                                          <span className="woocommerce-Price-currencySymbol">
                                            &#8377;
                                          </span>
                                          {item.prices.b2cPrice.sellingPrice}
                                        </bdi>
                                      </span>
                                    </span>
                                  </span>
                                </div>
                                <div className="gc-block-right"></div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    <CustomPagination
                      totalData={count}
                      page={params.page}
                      setCurrentPage={handleSetPage}
                      limit={params.limit}
                    />
                  </>
                ) : (
                  <>
                    <CustomLoadingOverlay active={isLoading || isFetching} />
                    {(isLoading || !isFetching) && (
                      <div
                        className="d-flex justify-content-center align-items-center"
                        style={{
                          minHeight: "50vh",
                        }}
                      >
                        <h3>No Data Found!</h3>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Products;
