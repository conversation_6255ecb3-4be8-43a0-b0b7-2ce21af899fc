import React, { useState } from 'react';
import { Star, StarBorder, StarHalf } from '@mui/icons-material';

const StarRating = ({ rating, setRating, editable = false, size = 20 }) => {
  const [hover, setHover] = useState(null);

  // Convert size to MUI icon size
  const iconSize = size === 20 ? 'small' : size <= 16 ? 'small' : size >= 30 ? 'large' : 'medium';

  return (
    <div className="star-rating" style={{ display: 'flex', alignItems: 'center' }}>
      {[...Array(5)].map((star, index) => {
        const ratingValue = index + 1;
        const displayValue = hover || rating;

        // Determine which star icon to show
        let StarIcon;
        let starColor;

        if (ratingValue <= displayValue) {
          StarIcon = Star;
          starColor = '#f0ad4e'; // Gold color for filled stars
        } else if (ratingValue - 0.5 <= displayValue) {
          StarIcon = StarHalf;
          starColor = '#f0ad4e'; // Gold color for half stars
        } else {
          StarIcon = StarBorder;
          starColor = '#ccd6df'; // Gray color for empty stars
        }

        return (
          <label
            key={index}
            style={{
              cursor: editable ? 'pointer' : 'default',
              margin: '0 2px',
              display: 'flex'
            }}
          >
            <input
              type="radio"
              name="rating"
              value={ratingValue}
              onClick={() => editable && setRating(ratingValue)}
              style={{ display: 'none' }}
            />
            <StarIcon
              fontSize={iconSize}
              sx={{
                color: starColor,
                '&:hover': editable ? { color: '#f0ad4e' } : {}
              }}
              onMouseEnter={() => editable && setHover(ratingValue)}
              onMouseLeave={() => editable && setHover(null)}
            />
          </label>
        );
      })}
    </div>
  );
};

export default StarRating;
