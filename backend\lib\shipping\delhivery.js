const axios = require('axios');
DELHIVERY_API_KEY = process.env.DELHIVERY_TOKEN;

//Verifed Functions

/**
 * Calculate shipping cost
 * @param {Object} shipmentDetails - The shipment details.
 * @param {string} shipmentDetails.origin - Pickup pincode.
 * @param {string} shipmentDetails.destination - Delivery pincode.
 * @param {number} shipmentDetails.weight - Weight in grams.
 * @param {number} shipmentDetails.cod - COD amount (if applicable).
 * @param {string} shipmentDetails.mode - Shipping mode (Surface/Air).
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */

const calculateShippingCost = async shipmentDetails => {
    try {
        const apiUrl = `${process.env.DELHIVERY_SHIPPING_COST_API_URL}?md=${shipmentDetails.md}&ss=${shipmentDetails.ss}&d_pin=${shipmentDetails.d_pin}&o_pin=${shipmentDetails.o_pin}&cgm=${shipmentDetails.cgm}&pt=${shipmentDetails.pt}&cod=${shipmentDetails.cod}`;
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: apiUrl,
            headers: {
                Authorization: `Token ${process.env.DELHIVERY_TOKEN}`,
            },
        };

        const response = await axios.request(config);
        return response.data;
    } catch (error) {
        console.error('Error calculating shipping cost:', error);
        throw new Error('Error calculating shipping cost');
    }
};

//Unverifed Functions
/**
 * Creates a shipment order with Delhivery.
 *
 * @param {Object} pickup - The pickup location details.
 * @param {string} pickup.name - Name of the pickup location.
 * @param {string} pickup.address - Address of the pickup location.
 * @param {string} pickup.pin - PIN code of the pickup location.
 * @param {string} pickup.phone - Phone number of the pickup location.
 * @param {Object} order - The order details.
 * @param {string} order.address - Delivery address.
 * @param {string} order.pincode - Delivery PIN code.
 * @param {string} order.phone - Recipient's phone number.
 * @param {string} order.name - Recipient's name.
 * @param {string} order.orderId - Order ID.
 * @param {string} order.products - Product description.
 * @param {number} order.amount - Order amount.
 * @param {string} order.paymentMode - Payment mode.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */

const createShipment = async (shipment, pickup) => {
    const olddata = {
        shipments: [
            {
                name: shipment.name,
                add: shipment.address,
                pin: shipment.pincode,
                city: shipment.city,
                state: shipment.state,
                country: shipment.country,
                phone: shipment.phone,
                order: shipment.order,
                payment_mode: shipment.payment_mode,
                return_pin: shipment.return_pin || '',
                return_city: shipment.return_city || '',
                return_phone: shipment.return_phone || '',
                return_add: shipment.return_add || '',
                return_state: shipment.return_state || '',
                return_country: shipment.return_country || '',
                products_desc: shipment.products_desc,
                hsn_code: shipment.hsn_code || '',
                cod_amount: shipment.cod_amount,
                order_date: shipment.order_date,
                total_amount: shipment.total_amount || '',
                seller_add: shipment.seller_add || '',
                seller_name: shipment.seller_name,
                seller_inv: shipment.seller_inv || '',
                quantity: shipment.quantity || '',
                waybill: shipment.waybill || '',
                shipment_width: shipment.shipment_width || '',
                shipment_height: shipment.shipment_height || '',
                weight: shipment.weight || '',
                seller_gst_tin: shipment.seller_gst_tin || '',
                shipping_mode: shipment.shipping_mode,
                address_type: shipment.address_type,
            },
        ],
        pickup_location: {
            name: pickup.name,
            add: pickup.add,
            city: pickup.city,
            pin_code: pickup.pin_code,
            country: pickup.country,
            phone: pickup.phone,
        },
    };

    const payload = {
        format: 'json',
        data: {
            shipments: [
                {
                    name: 'Ronak Sethi',
                    add: '85/616 Sector 8 Pratap Nagar Sanganer',
                    pin: '302033',
                    city: 'Jaipur',
                    state: 'Rajasthan',
                    country: 'India',
                    phone: '9887223537',
                    order: 'GC-1722582937',
                    payment_mode: 'COD',
                    return_pin: '302033',
                    return_city: 'Jaipur',
                    return_phone: '9887223537',
                    return_add: '85/616 Sector 8 Pratap Nagar Sanganer',
                    return_state: 'Rajasthan',
                    return_country: 'India',
                    products_desc: 'Electronics',
                    hsn_code: '85171210',
                    cod_amount: '1000',
                    order_date: '2024-08-01',
                    total_amount: '1000',
                    seller_add: '85/616 Sector 8 Pratap Nagar Sanganer',
                    seller_name: 'Ronak Sethi',
                    seller_inv: 'INV123456',
                    quantity: '1',
                    waybill: 'WB123456789',
                    shipment_width: '10',
                    shipment_height: '5',
                    weight: '0.5',
                    seller_gst_tin: '27AAACB1234F1Z8',
                    shipping_mode: 'Surface',
                    address_type: 'Home',
                },
            ],
            pickup_location: {
                name: 'My Shop',
                add: '614 morya nagar opposite baba memorial hospital sahi point dindoli surat',
                city: 'Surat',
                pin_code: 394210,
                country: 'India',
                phone: '9712227585',
            },
        },
    };
    try {
        const apiUrl = process.env.DELHIVERY_CREATE_SHIPMENT_API_URL;
        console.log('DELHIVERY_CREATE_SHIPMENT_API_URL:', apiUrl); // https://staging-express.delhivery.com/api/cmu/create.json

        if (!apiUrl) {
            throw new Error('DELHIVERY_CREATE_SHIPMENT_API_URL is not defined');
        }
        const response = await axios.post(apiUrl, new URLSearchParams({ payload }), {
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
                Authorization: `Token ${process.env.DELHIVERY_TOKEN}`, //70fd59365b4a8a7201b402e9caaf97584f8c42e8
            },
        });

        return response.data;
    } catch (error) {
        console.log('error>>', error);
        console.error('Error creating shipment:', error.response ? error.response.data : error.message);
        throw new Error('Error creating shipment');
    }
};

/**
 * Track a shipment with tracking id
 * @param {string} trackingId - The tracking ID of the shipment.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const shipmentStatus = async trackingId => {
    try {
        const apiUrl = `https://staging-express.delhivery.com/api/v1/packages/json/?waybill=${trackingId}`;
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: apiUrl,
            headers: {
                Authorization: `Token ${process.env.DELHIVERY_TOKEN}`,
            },
        };
        console.log('apiUrl >>>', apiUrl);
        console.log('config >>>', JSON.stringify(config));
        const response = await axios.request(config);
        console.log('response>>>', JSON.stringify(response));
        console.log('response>>>', response.data);
        return response.data;
    } catch (error) {
        console.log('error', JSON.stringify(error));
        console.error('Error calculating shipping cost:', error);
        throw new Error('Error calculating shipping cost');
    }
};

/**
 * Creates a warehouse with Delhivery.
 *
 * @param {Object} warehouse - The warehouse details.
 * @param {string} warehouse.name - Name of the warehouse.
 * @param {string} warehouse.email - Email of the warehouse contact.
 * @param {string} warehouse.phone - Phone number of the warehouse contact.
 * @param {string} warehouse.address - Address of the warehouse.
 * @param {string} warehouse.city - City of the warehouse.
 * @param {string} warehouse.country - Country of the warehouse.
 * @param {string} warehouse.pin - PIN code of the warehouse.
 * @param {string} warehouse.return_address - Return address of the warehouse.
 * @param {string} warehouse.return_pin - Return PIN code of the warehouse.
 * @param {string} warehouse.return_city - Return city of the warehouse.
 * @param {string} warehouse.return_state - Return state of the warehouse.
 * @param {string} warehouse.return_country - Return country of the warehouse.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const createWarehouse = async warehouse => {
    const warehouseData = {
        name: warehouse.name,
        email: warehouse.email,
        phone: warehouse.phone,
        address: warehouse.address,
        city: warehouse.city,
        country: warehouse.country,
        pin: warehouse.pin,
        return_address: warehouse.return_address,
        return_pin: warehouse.return_pin,
        return_city: warehouse.return_city,
        return_state: warehouse.return_state,
        return_country: warehouse.return_country,
    };

    try {
        const response = await axios.post(process.env.DELHIVERY_WAREHOUSE_API_URL, warehouseData, {
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Token ${DELHIVERY_API_KEY}`,
            },
        });

        return response.data;
    } catch (error) {
        console.error('Error creating warehouse:', error.response ? error.response.data : error.message);
        throw new Error('Error creating warehouse');
    }
};

/**
 * Check pincode serviceability
 * @param {string} pincode - The pincode to be checked.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const checkPincodeServiceability = async pincode => {
    try {
        const response = await axios.get(`${process.env.DELHIVERY_PINCODE_SERVICEABILITY_API_URL}?pincode=${pincode}`, {
            headers: {
                Authorization: `Token ${DELHIVERY_API_KEY}`,
            },
        });

        return response.data;
    } catch (error) {
        console.error('Error checking pincode serviceability:', error.response ? error.response.data : error.message);
        throw new Error('Error checking pincode serviceability');
    }
};

/**
 * Fetch waybill
 * @param {string} clientCode - Your client code.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const fetchWaybill = async clientCode => {
    try {
        const response = await axios.post(
            process.env.DELHIVERY_WAYBILL_API_URL,
            { client_code: clientCode },
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Token ${DELHIVERY_API_KEY}`,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error fetching waybill:', error.response ? error.response.data : error.message);
        throw new Error('Error fetching waybill');
    }
};

/**
 * Generate shipping label
 * @param {string} waybill - The waybill number.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const generateShippingLabel = async (waybill, pdf) => {
    try {
        const apiUrl = `${process.env.DELHIVERY_LABEL_API_URL}?wbns=${waybill}&pdf=${pdf}`;
        let config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: apiUrl,
            headers: {
                Authorization: `Token ${process.env.DELHIVERY_TOKEN}`,
            },
        };
        console.log("config >>",config)
        const response = await axios.request(config);
        console.log('response >>', response.data);
        return response.data;
    } catch (error) {
        console.error('Error generating shipping label:', error.response ? error.response.data : error.message);
        throw new Error('Error generating shipping label');
    }
};

/**
 * Raise pickup request
 * @param {Object} pickupRequest - The pickup request details.
 * @param {string} pickupRequest.pickup_time - The pickup time in ISO format.
 * @param {string} pickupRequest.shipment_id - The shipment ID.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const raisePickupRequest = async (pickupTime, shipmentId) => {
    try {
        const response = await axios.post(
            process.env.DELHIVERY_PICKUP_API_URL,
            {
                pickup_time: pickupTime,
                shipment_id: shipmentId,
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Token ${DELHIVERY_API_KEY}`,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error raising pickup request:', error.response ? error.response.data : error.message);
        throw new Error('Error raising pickup request');
    }
};

/**
 * Take NDR action
 * @param {string} waybill - The waybill number.
 * @param {string} action - The NDR action (e.g., Reschedule, Return).
 * @param {string} comments - Additional comments.
 * @param {string} date - The action date in ISO format.
 * @returns {Promise<Object>} - The response data from Delhivery API.
 */
const takeNDRAction = async (waybill, action, comments, date) => {
    try {
        const response = await axios.post(
            process.env.DELHIVERY_NDR_API_URL,
            {
                waybill: waybill,
                action: action,
                comments: comments,
                date: date,
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Token ${DELHIVERY_API_KEY}`,
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error('Error taking NDR action:', error.response ? error.response.data : error.message);
        throw new Error('Error taking NDR action');
    }
};

module.exports = {
    createShipment,
    shipmentStatus,
    createWarehouse,
    checkPincodeServiceability,
    fetchWaybill,
    calculateShippingCost,
    generateShippingLabel,
    raisePickupRequest,
    takeNDRAction,
};
