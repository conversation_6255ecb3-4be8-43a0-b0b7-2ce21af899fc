import React from "react";

import footerBottom from "../assets/images/os_kartlar_x2.svg";
import {
  IconCustomerService,
  IconSecurePayment,
  IconShippingTruck,
  IconSpecialCampagin,
  MeeshoIcon,
} from "utils/icons";
import { Link } from "react-router-dom";
import useUserStore from "stores/user";
import { useGeneralSettingsQuery } from "api/util.api";
import { useCategoriesQuery } from "api/categories.api";

const Footer = () => {
  const token = useUserStore((state) => state.userInfo.token);
  const { data: settingsData = {} } = useGeneralSettingsQuery();

  const { data: categories = [] } = useCategoriesQuery();

  return (
    <>
      <div className="footer-services-width-parent">
        <div className="container">
          <div className="footer-services-width">
            <div className="row">
              <div className="col-md-6 col-lg-3 col-xl-3">
                <div className="gc-features-item">
                  <div className="gc-features-icon">
                    <IconShippingTruck />
                  </div>
                  <div className="gc-features-content">
                    <h5 className="features-title">Free shipping</h5>
                    <span className="features-desc">Standard Shipping</span>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-lg-3 col-xl-3">
                <div className="gc-features-item">
                  <div className="gc-features-icon">
                    <IconSecurePayment />
                  </div>
                  <div className="gc-features-content">
                    <h5 className="features-title">Secure Payment</h5>
                    <span className="features-desc">
                      100% risk-free shopping
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-lg-3 col-xl-3">
                <div className="gc-features-item">
                  <div className="gc-features-icon">
                    <IconSpecialCampagin />
                  </div>
                  <div className="gc-features-content">
                    <h5 className="features-title">Special Campaigns</h5>
                    <span className="features-desc">Guaranteed Saving</span>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-lg-3 col-xl-3">
                <div className="gc-features-item">
                  <div className="gc-features-icon">
                    <IconCustomerService />
                  </div>
                  <div className="gc-features-content">
                    <h5 className="features-title">Customer Service</h5>
                    <span className="features-desc">Give us feedback</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container footer-bottom-menu">
        <div className="row">
          <div className="col-12 col-md-12">
            <div className="row">
              <div className="col-sm-6  col-md-3">
                <div>
                  <div className="gc-transform transform-type-translate">
                    <div className="">
                      <h5 className="">Address </h5>{" "}
                    </div>
                  </div>
                  <div className="">
                    <div className="">
                      <p>
                        {settingsData?.address ||
                          "614 morya nagar opposite baba memorial hospital sahi point dindoli surat 394210."}
                        &nbsp;
                      </p>{" "}
                    </div>
                  </div>
                  {/* <div className="e-grid-align-mobile-left e-grid-align-left">
                    <div className="">
                      <div className="social-icons-wrapper">
                        <span className="">
                          <a
                            className="social-icon-facebook social-icon"
                            href={settingsData?.socialLinks?.facebook}
                            target="_blank"
                          >
                            <i className="fab fa-facebook"></i>{" "}
                          </a>
                        </span>
                        <span className="">
                          <a
                            className="social-icon-instagram social-icon"
                            href={settingsData?.socialLinks?.instagram}
                            target="_blank"
                          >
                            <i className="fab fa-instagram"></i>{" "}
                          </a>
                        </span>
                      </div>
                    </div>
                  </div> */}
                </div>
              </div>
              <div className="col-sm-6 col-md-3 foot-menu-list">
                <div className="gc-transform transform-type-translate ">
                  <div className="">
                    <h6 className="">Categories</h6>{" "}
                  </div>
                </div>
                <div className="">
                  <div className="">
                    <ul className="s">
                      {categories?.length > 0 &&
                        categories.slice(0, 5).map((category, idx) => {
                          return (
                            <li className="">
                              <Link to={`/category?catId=${category?._id}`}>
                                <span className="">{category?.name}</span>
                              </Link>
                            </li>
                          );
                        })}
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-sm-6 col-md-3 foot-menu-list">
                <div className="gc-transform transform-type-translate  ">
                  <div className="">
                    <h6 className=" ">Help</h6>{" "}
                  </div>
                </div>
                <div className="">
                  <div className="">
                    <ul className="">
                      <li className="">
                        <Link to={token ? "/dashboard" : "/sign-in"}>
                          <span className="">Order Tracking</span>
                        </Link>
                      </li>
                      <li className="">
                        <Link to="/terms-conditions">
                          <span className="">Terms &amp; Conditions</span>
                        </Link>
                      </li>
                      <li className="">
                        <Link to="/privacy-policy">
                          <span className="">Privacy Policy</span>
                        </Link>
                      </li>
                      <li className="">
                        <Link to="/return-policy">
                          <span className="">Return & Cancellation Policy</span>
                        </Link>
                      </li>
                      <li className="">
                        <Link to="/faq">
                          <span className="">FAQ</span>
                        </Link>
                      </li>
                      {/* <li className="">
                        <a href="#">
                          <span className="">Shipping Policy</span>
                        </a>
                      </li> */}
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-sm-6 col-md-3 foot-menu-list">
                <div className="gc-transform transform-type-translate ">
                  <div className="">
                    <h6 className="">Connect With Us</h6>{" "}
                  </div>
                </div>
                <div className="e-grid-align-mobile-left e-grid-align-left">
                  <div className="">
                    <div className="social-icons-wrapper">
                      <span className="">
                        <a
                          className="social-icon-facebook social-icon"
                          href={settingsData?.socialLinks?.facebook}
                          target="_blank"
                        >
                          <i className="fab fa-facebook"></i>{" "}
                        </a>
                      </span>
                      <span className="">
                        <a
                          className="social-icon-instagram social-icon"
                          href={settingsData?.socialLinks?.instagram}
                          target="_blank"
                        >
                          <i className="fab fa-instagram"></i>{" "}
                        </a>
                      </span>
                      <span className="">
                        <a
                          className="social-icon-instagram social-icon"
                          href={settingsData?.socialLinks?.meesho}
                          target="_blank"
                        >
                          <i className="fab fa-m"></i>{" "}
                        </a>
                      </span>
                    </div>
                  </div>
                </div>
                {/* <div className=" ">
                  <div className="">
                    <ul className="s">
                      <li className="">
                        <a href="#">
                          <span className="">Facebook</span>
                        </a>
                      </li>
                      <li className="">
                        <a href="#">
                          <span className="">Instagram</span>
                        </a>
                      </li>
                      <li className="">
                        <a href="#">
                          <span className="">%50 Sales</span>
                        </a>
                      </li>
                      <li className="">
                        <a href="#">
                          <span className="">Outlet</span>
                        </a>
                      </li>
                      <li className="">
                        <a href="#">
                          <span className="">Pre-Sale</span>
                        </a>
                      </li>
                    </ul>
                  </div>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="footer-bottom">
        <div className="footer-bottom-inner">
          <div className="row">
            <div className="col-lg-6 align-items-center d-flex">
              <div className="footer-bottom-menu">
                <ul>
                  <li>
                    tel : {settingsData?.contact || "9712227585, 7984544851"}{" "}
                  </li>
                  <li> - </li>
                  <li>
                    {" "}
                    mail : {settingsData?.email || "<EMAIL>"}
                  </li>
                </ul>
              </div>
            </div>
            <div className="col-lg-6">
              <img src={footerBottom} alt="" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Footer;
