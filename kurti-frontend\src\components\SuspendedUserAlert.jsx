import { useNavigate } from 'react-router-dom';
import { setCartItems } from 'stores/cart';
import useUserStore, { resetState } from 'stores/user';
import { setWishlistItems } from 'stores/wishlist';

const SuspendedUserAlert = () => {
  const navigate = useNavigate();
  const userInfo = useUserStore((state) => state.userInfo);

  // Check if user is suspended
  const isUserSuspended = userInfo?.user?.isSuspended;

  if (!isUserSuspended) {
    return null;
  }

  const handleLogout = () => {
    resetState();
    setCartItems([]);
    setWishlistItems([]);
    navigate('/');
  };

  return (
    <div className="suspended-user-alert d-flex justify-content-center align-items-center">
      <div>
        <strong>Account Suspended: </strong>
        Your account has been suspended by the administrator. You cannot perform any actions until your account is reactivated.
      </div>
      <button onClick={handleLogout}>
        Logout
      </button>
    </div>
  );
};

export default SuspendedUserAlert;
