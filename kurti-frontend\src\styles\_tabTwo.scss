body {
    &:not(.page-loaded) {
        .swiper-autoheight {
            .gc-swiper-wrapper {
                display: flex;
            }
        }
    }
}

.gc-tab-wrapper {
    &.ajax-loaded {
        .gc-products {
            &.loaded {
                &:not(.active) {
                    display: none;
                }
            }
        }
    }

    .gc-tab-banner {
        &:hover {
            img {
                transform: scale(1.2);
            }
        }
    }

    .gc-banner-link {
        z-index: 3;
    }

    .gc-banner-button {
        display: inline-flex;
        align-items: center;
        padding: 10px 15px;
        color: var(--gc-light);
        background: var(--gc-dark);
        gap: 14px;
    }

    .gc-shop-fast-filters {
        margin-bottom: 0;
    }

    .gc-swiper-container {
        .gc-swiper-next {
            right: 0;
            left: auto;
        }
    }

    &.style-2 {
        border: 2px solid var(--gc-green);
        border-radius: 10px;
        background-color: var(--gc-success-bg);

        .gc-btn-primary {
            border-color: var(--gc-green);
            background-color: var(--gc-green);
        }
    }

    &.style-3 {
        border: 2px solid var(--gc-green);
        border-radius: 10px;

        &>div {
            padding: 15px;
        }
    }

    .gc-woocommerce-pagination {
        margin-top: 20px;
    }
}

.gc-tab-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: nowrap;
    position: relative;
    z-index: 5;
    gap: 15px;

    .gc-tab-title {
        margin: 0;
        margin: 0;
        max-width: 100%;
        font-size: 22px;
    }
}

.gc-tab-menu {
    

    a {
        display: flex;
        align-items: center;
        gap: 10px;
        line-height: 1;
        color: var(--gc-purple);

        svg {
            width: 17px;
            height: 17px;
        }
    }

    &.menu-bordered {
        a {
            border: 1px solid var(--gc-border);
            border-radius: 20px;
            padding: 6px 15px;
        }
    }

    &:not(.fast-filters) {
        a {
            &:not(:last-child) {
                padding-right: 10px;
                border-right: 1px solid var(--gc-border);
            }
        }
    }

    &.active {
        opacity: 1;
        visibility: visible;
    }
}

.gc-tab-menu a.active,
.gc-tab-menu a.active svg {
    color: var(--gc-primary);
    fill: var(--gc-primary);
}

.fast-filters {
    a {
        padding: 5px 5px;
        min-height: 30px;
        border: 1px solid var(--gc-border);
    }
}

.fast-filters a:hover,
.fast-filters a.active {
    color: var(--gc-purple-dark) !important;
    background-color: var(--gc-purple-bg);
    border-color: var(--gc-blue-soft);
}



.gc-tab-header .gc-tab-button.gc-btn,
.gc-tab-header .gc-btn-text.gc-btn,
.gc-tab-header .gc-btn-text.gc-btn:hover {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    
}

.gc-tab-button {
    &.icon-after {
        flex-direction: row-reverse;
    }

    &.gc-btn {
        &>span {
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
    }

    &>span {
        &:before {
            content: attr(data-hover);
            transition: all 0.25s cubic-bezier(.31, -.105, .43, 1.40);
        }

        &:after {
            position: absolute;
            top: 100%;
            content: attr(data-hover);
            transition: all 0.35s cubic-bezier(.31, -.105, .43, 1.40);
        }
    }
}

.gc-tab-content {
    &.has-banner {
        display: flex;
        gap: 20px;
    }

    &.banner-none {
        display: block;
    }
}

.has-banner {
    .gc-tab-products {
        flex: 1;
    }
}

.layout-slider {
    .has-banner {
        .gc-tab-products {
            flex: 1 0 auto;
        }
    }
}

.gc-tab-banner {
    position: relative;
    overflow: hidden;

    &.fit_image {
        img {
            object-fit: cover;
            -webkit-transition: all 0.15s ease-in-out;
            transition: all 0.15s ease-in-out;
            z-index: 0;
        }
    }
}

.gc-tab-banner-content,
.gc-tab-banner.fit_image img,
.gc-tab-wrapper .gc-banner-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
}

.gc-tab-banner-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 15px;
    z-index: 2;
    flex-wrap: wrap;
}

.gc-banner-subtitle {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1.4;
}

.gc-banner-title {
    margin: 0;
}

.gc-tab-footer {
    font-size: 11px;
    text-transform: uppercase;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 15px;

    a {
        display: flex;
        align-items: center;
        gap: 5px;
        color: var(--gc-green);
    }
}

.gc-tab-banner-content .icon-after,
.gc-tab-footer .icon-after {
    flex-direction: row-reverse;
}

.gc-tab-banner-content .has-icon svg,
.gc-tab-footer .gc-footer-link svg {
    width: 1rem;
    height: 1rem;
    fill: currentColor;
}


.gc-products .swiper-button-prev,
.gc-products .swiper-button-next {
    position: absolute;
    width: 35px;
    height: 35px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gc-light);
    background-color: var(--gc-primary);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    z-index: 2;
    &:after{
        font-size: 20px;
    }
    &:before{
        display: none;
    }
}

.swiper-button-next {
    //left:0 !important;
    &:before {
        content: "\f105";
        cursor: pointer;
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        display: flex;
        align-items: center;
        justify-content: center;
        align-content: center;
        flex-direction: column;
        flex-wrap: nowrap;
        position: relative;
        margin: auto;
        margin-top: 6px;
    }
}

.swiper-button-prev {
    right:0 !important;
    &:before {
        content: "\f104";
        cursor: pointer;
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        display: flex;
        align-items: center;
        justify-content: center;
        align-content: center;
        flex-direction: column;
        flex-wrap: nowrap;
        position: relative;
        margin: auto;
        margin-top: 6px;
    }
}
.gc-products{
    .swiper-button-next{
        left:inherit !important;
    }
}

.gc-products:hover .swiper-button-prev,
.gc-products:hover .swiper-button-next {
    opacity: 1;
    visibility: visible;
    color: var(--gc-light);
    background-color: var(--gc-primary-red);
}

.style-1 {
    .gc-tab-header {
        padding: 20px;
        border: 1px solid var(--gc-green);
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .gc-tab-footer {
        margin-top: 20px;
    }
}

.style-4 {
    .gc-tab-header {
        border-bottom: 2px solid;
        border-color: var(--gc-border);
        padding-bottom: 10px;
    }
}

.style-5 {
    .gc-tab-header {
        border-bottom: 2px solid;
        border-color: var(--gc-border);
        background: var(--gc-gray-soft);
        padding: 20px 20px;
    }
}

.style-6 {
    .gc-tab-header {
        margin-bottom: 0;
        border: 2px solid;
        border-bottom: 0;
        border-color: var(--gc-border);
        background: var(--gc-gray-soft);
        padding: 20px 20px;
    }

    .gc-tab-content {
        gap: 15px;
        --gap: 15px;
        flex-direction: row;
        --flex-direction: row;
        width: var(--flex-direction);
        padding: 30px;
        border: 2px solid #eee;
        border-top: 0;
    }
}

.gc-tab-wrapper.style-2>div,
.gc-tab-wrapper.style-3>div {
    padding: 20px;
    padding: 20px;
}

.gc-tab-wrapper.style-2>div.gc-tab-footer,
.gc-tab-wrapper.style-3>div.gc-tab-footer {
    padding-top: 0;
}

.gc-tab-wrapper.style-2 .gc-tab-header,
.gc-tab-wrapper.style-3 .gc-tab-header {
    border-bottom: 2px solid var(--gc-green);
}

.gc-tab-wrapper.style-2 .gc-loop-product,
.gc-tab-wrapper.style-2 .gc-product-type-6 .gc-product-details-wrapper,
.gc-tab-wrapper.style-2 .fast-filters a {
    border-color: var(--gc-green-soft);
}

.fast-filters-trigger {
    display: none;

    svg {
        max-width: 16px;
        max-height: 16px;
        fill: var(--gc-gray);

        g {
            fill: var(--gc-dark);
        }
    }
}

.attr-wrapper {
    padding: 10px 15px;
    border: 2px solid var(--gc-green);
    border-radius: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
    line-height: 1;
}

.terms-menu {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 5px;

    .term-color {
        width: 12px;
        height: 12px;
        border: 1px solid var(--gc-border);
        border-radius: 10px;
    }
}

@media(max-width:800px) {
    .fast-filters-trigger {
        display: inline-flex;
        align-items: center;
        flex-direction: row-reverse;
        gap: 5px;
        background: var(--gc-light);
        padding: 5px 10px;
        border-radius: 5px;
        z-index: 5;
        border: 1px solid;
        border-color: var(--gc-border);
    }
}


.shop-loop-wrapper.pagination-loadmore .gc-woocommerce-pagination,
.shop-loop-wrapper.pagination-infinite .gc-woocommerce-pagination {
    display: none;
}

.shop-loadmore-wrapper {
    margin-top: 30px;
    text-align: center;

    a {
        &.loading {
            pointer-events: none;
        }
    }
}

.shop-loop-wrapper {
    .nt-pagination {
        margin-top: 30px;
    }
}

.gc-tab-button:hover>span:after,
.gc-tab-button:hover>span:before {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    transform: translateY(-100%);
    transition: all 0.2s cubic-bezier(.21, -.105, .43, 1.40);
}