import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

const initialState = {
  userInfo: {
    user: {},
    token: "",
  },
  signupInfo: {},
  tempAvatar: null,
};

const userStore = (set, get) => ({
  ...initialState,
  setUserInfo: (data) => set((state) => ({ ...state, userInfo: data })),
  setSignupInfo: (data) => set((state) => ({ ...state, signupInfo: data })),
  resetState: () => set(initialState),
  getAuthToken: () => get().userInfo.token,
  setUserAvatar: (data) => set((state) => ({ ...state, userInfo: { ...state.userInfo, user: { ...state.userInfo.user, avatar: data } } })),
  setTempAvatar: (avatar) => set(() => ({ tempAvatar: avatar })),
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
      partialize: (state) => {
        const { tempAvatar, ...persisted } = state;
        return persisted;
      },
    })
  )
);

export const getAuthToken = () => {
  // access the zustand store outside of React.
  return useUserStore.getState().userInfo.token;
};

export const getUserInfo = () => {
  return useUserStore.getState().userInfo.user;
};

export const { setUserInfo, setSignupInfo, resetState, setUserAvatar, setTempAvatar } =
  useUserStore.getState();

export default useUserStore;
