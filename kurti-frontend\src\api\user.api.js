import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./api-client";

export const useAddAddressMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(API_ENDPOINTS.ADD_ADDRESS, payload);
      return response;
    },
  });

export const useUpdateAddressMutation = () =>
  useMutation({
    mutationFn: async ({ id, payload }) => {
      const response = await apiClient.put(
        `${API_ENDPOINTS.GET_ADDRESS_LIST}/${id}`,
        payload
      );
      return response;
    },
  });

export const useAddressQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_ADDRESS_LIST);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["address-list"],
  });

export const useAddressInfoQuery = (id) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        `${API_ENDPOINTS.GET_ADDRESS_LIST}/${id}`
      );
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["address-info"],
    enabled: !!id,
  });

export const useUserProfileQuery = (id) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_USER_PROFILE);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["user-profile"],
  });

export const useGetProfileMutation = () =>
  useMutation({
    mutationFn: async (token) => {
      if (!token) {
        return null;
      }
      const response = await apiClient.get(API_ENDPOINTS.GET_USER_PROFILE, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response
    },
  });

export const useUpdateProfileMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.put(
        API_ENDPOINTS.GET_USER_PROFILE,
        payload
      );
      return response;
    },
  });
