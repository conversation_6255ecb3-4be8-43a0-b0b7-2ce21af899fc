.dashboard-page {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 30px 0;
}

/*/////////////////////////////////
02 >-----> MYACCOUNT NAVIGATION
/////////////////////////////////*/
.gc-myaccount-page-content-inner {
  padding: 0;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.gc-myaccount-navigation {
  border-color: transparent !important;
  background-color: #fff;
  padding: 20px 15px;
  border-radius: 8px 8px 0 0;

  .MuiTabs-scroller {
    .MuiTab-root {
      position: relative;
      display: flex;
      width: auto;
      align-items: center;
      justify-content: flex-start;
      font-weight: 500;
      font-size: 15px;
      padding: 15px 20px;
      margin-bottom: 5px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: rgba(108,94,188, 0.05);
        color: var(--gc-purple);
      }

      &.Mui-selected {
        background-color: var(--gc-purple);
        color: white;
      }
    }

    .MuiTabs-indicator {
      display: none;
    }

    .MuiTabs-flexContainer {
      flex-wrap: nowrap;
    }
  }
}

.button {
  font-size: 14px;
  font-weight: 500;
  background-color: var(--gc-purple);
  color: white !important;
  padding: 0px 20px;
  min-height: 42px;
  min-width: 160px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  letter-spacing: 0.3px;
  text-align: center;
  text-decoration: none;
  text-shadow: none;
  background-image: none;
  outline: none;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(108,94,188, 0.2);
  white-space: nowrap;
  user-select: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: darken(#6c5ebc, 10%);
    box-shadow: 0 6px 15px rgba(108,94,188, 0.3);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

table.order-table {
  border: none;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 20px;

  th {
    font-weight: 600;
    color: #333;
    background-color: #f8f9fa;
    padding: 15px;
    text-align: left;
    border-bottom: 2px solid #eee;
  }

  td {
    padding: 15px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
    font-size: 14px;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
  }

  .button {
    min-height: 36px;
    min-width: inherit;
    padding: 8px 15px;
    display: inline-flex;
    gap: 8px;
    margin-top: 0px !important;
    font-size: 13px;
  }

  tr {
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(108,94,188, 0.03);
    }

    &:nth-child(odd) {
      background-color: #f9f9f9;

      &:hover {
        background-color: rgba(108,94,188, 0.05);
      }
    }
  }
}

.gc-myaccount-content {
  padding: 30px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;

  h4 {
    font-size: 22px;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 12px;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 3px;
      background-color: var(--gc-purple);
      border-radius: 3px;
    }
  }

  a {
    color: var(--gc-purple);
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      color: darken(#6c5ebc, 10%);
      text-decoration: none;

      &:after {
        width: 100%;
      }
    }

    &:after {
      content: '';
      height: 2px;
      width: 0;
      background-color: currentColor;
      position: absolute;
      bottom: 0;
      left: 0;
      transition: width 0.25s;
    }

    &.address-action-btn {
      color: white;

      &:hover {
        color: white;
      }

      &:after {
        display: none;
      }
    }
  }

  a:not(.button):not(.address-action-btn) {
    position: relative;
    display: inline-block;
  }

  .woocommerce-message,
  .woocommerce-info {
    background-color: #f8f9fa;
    border-left: 4px solid var(--gc-purple);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;

    .button {
      margin-top: 10px;
    }
  }

  .form-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    input {
      width: 100%;
    }
  }

  p .button {
    margin-top: 20px;
  }

  .gc-form-title {
    margin-bottom: 20px;
  }

  .edit-account {
    .gc-row input {
      width: 100%;
    }

    fieldset {
      margin-top: 40px;
      border: none;
      padding: 0;

      legend {
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        padding-top: 30px;
        border-top: 1px solid #eee;
        display: inline-flex;
        width: 100%;
        text-transform: uppercase;
        font-size: 16px;
      }
    }
  }
}

.logout-tab {
  color: #666 !important;
  font-weight: 500 !important;

  &:hover {
    background-color: rgba(108,94,188, 0.05) !important;
    color: var(--gc-purple) !important;
  }
}

.gc-popup-item {
  .row {
    &.gc-myaccount-page-content {
      &>div {
        width: 100%;
      }
    }
  }
}

@media(min-width:576px) {
  .gc-myaccount-page-content {
    justify-content: center;

    .col-content {
      position: relative;
    }
  }

  .gc-myaccount-content {
    padding: 30px;
    margin-top: 0;
    border: 0;
  }
}

.contact-form {
  padding-top: 20px;

  h3 {
    color: #333;
    font-size: 22px;
    line-height: 1.3;
    margin-bottom: 24px;
    font-weight: 600;
  }

  input:not([type="checkbox"]):not([type="radio"]):not([type="button"]),
  select,
  textarea {
    padding: 12px 15px;
    min-height: 45px;
    border: 1px solid #e0e0e0;
    max-width: 100%;
    width: 100%;
    margin-bottom: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(108,94,188, 0.2);
      border-color: var(--gc-purple);
    }
  }

  textarea {
    min-height: 120px;
    resize: vertical;
  }

  label {
    display: block;
    font-size: 14px;
    color: #555;
    margin-bottom: 8px;
    line-height: 1.4;
    font-weight: 500;
  }

  .error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
  }

  .form-row {
    margin-bottom: 20px;
  }

  .button,
  button[type="submit"],
  input[type="submit"] {
    background-color: var(--gc-purple);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(108,94,188, 0.2);

    &:hover {
      background-color: darken(#6c5ebc, 10%);
      box-shadow: 0 6px 15px rgba(108,94,188, 0.3);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.anarkali-form-title {
  font-size: 22px;
  font-weight: 600;
  color: #333;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 12px;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--gc-purple);
    border-radius: 3px;
  }
}