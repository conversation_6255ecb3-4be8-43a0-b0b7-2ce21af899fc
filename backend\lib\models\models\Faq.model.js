const mongoose = require('mongoose');

const FaqSchema = new mongoose.Schema({
    question: { 
        type: String,
        required: true 
    }, 
    answer: {
        type: String,
        required: true
    },
    category: {
        type: String,
        default: 'General'
    },
    sortOrder: {
        type: Number,
        default: 0,
    },
    isSuspended: {
        type: Boolean,
        default: false,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
}, {
    timestamps: {
        createdAt: 'created',
        updatedAt: 'updated',
    },
    id: false,
    toJSON: {
        getters: true,
    },
    toObject: {
        getters: true,
    },
});

const Faq = mongoose.model('Faq', FaqSchema);

module.exports = Faq;
