import React, { useState, useEffect, useMemo } from 'react';
import { Modal } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { Box, Rating } from '@mui/material';
import { SETTINGS } from 'globals';
import parse from 'html-react-parser';
import { useProductInfoQuery } from 'api/product.api';
import { useRemoveCartMutation, useUpdateCartMutation } from 'api/cart.api';
import useCartStore, { setCartItems } from 'stores/cart';
import useUserStore from 'stores/user';
import useWishlistStore, { setWishlistItems } from 'stores/wishlist';
import { useRemoveWishlistMutation, useUpdateWishlistMutation } from 'api/wishlist.api';
import { IconWishListEmpty, IconWishListFilled } from 'utils/icons';
import ProductColorListMolecule from 'components/molecules/ProductColorList.molecule';
import ProductSizeListMolecule from 'components/molecules/ProductSizeList.molecule';
import CustomLoadingOverlay from 'components/molecules/CustomLoadingOverlay';
import './styles.scss';

const QuickLookModal = ({ show, handleClose, productId }) => {
  const [quantity, setQuantity] = useState(SETTINGS.PRODUCT_QTY_MIN);
  const [ctaText, setCtaText] = useState(SETTINGS.ADD_TO_CART);
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedColor, setSelectedColor] = useState(null);
  const [isApiCalling, setIsApiCalling] = useState(false);

  // Queries and mutations
  const {
    data: productInfo = {},
    isLoading,
    isFetching,
  } = useProductInfoQuery(productId, {
    skip: !productId,
    refetchOnMountOrArgChange: true,
  });

  const { mutateAsync: updateCart } = useUpdateCartMutation();
  const { mutateAsync: removeCart } = useRemoveCartMutation();
  const { mutateAsync: updateWishlist } = useUpdateWishlistMutation();
  const { mutateAsync: removeWishlist } = useRemoveWishlistMutation();

  // Store data
  const cartItems = useCartStore((state) => state.cartItems);
  const wishlistItems = useWishlistStore((state) => state.wishlistItems);
  const token = useUserStore((state) => state.userInfo.token);

  // Reset state when modal opens with a new product
  useEffect(() => {
    if (productInfo && Object.keys(productInfo)?.length) {
      setSelectedColor(productInfo?.attributes?.color[0]);
      setSelectedSize(productInfo?.stock[0]?.size);
      setQuantity(SETTINGS.PRODUCT_QTY_MIN);
      setCtaText(SETTINGS.ADD_TO_CART);
    }
  }, [productInfo]);

  // Derived values
  const productPrices = useMemo(() => {
    if (productInfo?.stock?.length && selectedSize) {
      return productInfo.stock.find((item) => item.size === selectedSize);
    }
    return null;
  }, [productInfo, selectedSize]);

  const productImages = useMemo(() => {
    if (productInfo?.attributes?.color?.length && selectedColor?.hexCode) {
      return productInfo.attributes.color.find(
        (item) => item.hexCode === selectedColor.hexCode
      )?.images;
    }
    return null;
  }, [productInfo, selectedColor]);

  const availableSizes = useMemo(() => {
    if (productInfo?.stock?.length) {
      return productInfo.stock.map((item) => item.size);
    }
    return [];
  }, [productInfo]);

  // Check if product is in cart or wishlist
  const productCheckQry = (item, product, selectedColor, selectedSize) => {
    return (
      item?._id === product?._id &&
      item?.selectedColor?.hexCode === selectedColor?.hexCode &&
      item?.selectedSize === selectedSize
    );
  };

  const isProductInCart = useMemo(
    () =>
      cartItems.some((item) =>
        productCheckQry(item, productInfo, selectedColor, selectedSize)
      ),
    [cartItems, productInfo, selectedColor, selectedSize]
  );

  const isProductInWishlist = useMemo(
    () => wishlistItems.some((item) => item._id === productInfo._id),
    [wishlistItems, productInfo]
  );

  // Handlers
  const handleUpdateQuantity = (type) => {
    if (isProductInCart) return;
    if (type === "increment" && quantity <= SETTINGS.PRODUCT_QTY_MAX - 1) {
      setQuantity(quantity + 1);
    } else if (type === "decrement" && quantity > SETTINGS.PRODUCT_QTY_MIN) {
      setQuantity(quantity - 1);
    }
  };

  const handleAddToCart = () => {
    setIsApiCalling(true);
    if (isProductInCart) {
      setCartItems(cartItems.filter((item) => item._id !== productInfo._id));
      removeCart(productInfo?._id);
      setIsApiCalling(false);
      return;
    }
    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productImages?.mainImage,
      price: productPrices?.sellingPrice,
      originalPrice: productPrices?.unitPrice,
      discount: productInfo.prices.b2cPrice.discount,
      quantity,
      selectedSize,
      selectedColor,
    };
    if (token) {
      updateCart({
        data: {
          productId: productInfo._id,
          quantity,
          selectedSize,
          selectedColor,
        },
        token,
      });
    }
    setCartItems([...cartItems, itemInfo]);
    setQuantity(1);
    setCtaText(SETTINGS.ADDED_TO_CART);

    setTimeout(() => {
      setCtaText(SETTINGS.ADD_TO_CART);
      setIsApiCalling(false);
    }, 2000);
  };

  const handleAddToWishlist = () => {
    if (isProductInWishlist) {
      setWishlistItems(
        wishlistItems.filter((item) => item._id !== productInfo._id)
      );
      removeWishlist(productInfo?._id);
      return;
    }
    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productImages?.mainImage,
      prices: productInfo?.prices,
    };
    if (token) {
      updateWishlist({
        data: {
          productId: productInfo._id,
        },
        token,
      });
    }
    setWishlistItems([...wishlistItems, itemInfo]);
  };

  const handleSelectColor = (color) => {
    setSelectedColor(color);
  };

  const handleSelectSize = (size) => {
    setSelectedSize(size);
  };

  if (!productId) return null;

  return (
    <Modal
      show={show}
      onHide={handleClose}
      centered
      size="lg"
      className="quick-look-modal"
    >
      <CustomLoadingOverlay active={isLoading || isFetching} />
      <Modal.Header closeButton>
        <Modal.Title style={{color: '#000'}}>Quick Look</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {productInfo && Object.keys(productInfo).length > 0 && (
          <div className="row">
            <div className="col-md-6 product-image">
              <img
                src={productImages?.mainImage?.url || productInfo?.mainImage}
                alt={productInfo.name}
                className="img-fluid"
              />
            </div>
            <div className="col-md-6 product-details">
              <h3 className="product-title">
                {productInfo.name}
              </h3>

              <div className="product-price">
                <span className="original-price">
                  {parse(SETTINGS.CURRENCY)}{productPrices?.unitPrice}
                </span>
                <span className="selling-price">
                  {parse(SETTINGS.CURRENCY)}{productPrices?.sellingPrice}
                </span>
              </div>

              {productInfo?.avgrating > 0 && (
                <div className="product-rating">
                  <Rating
                    value={productInfo?.avgrating || 0}
                    readOnly
                    precision={0.5}
                    size="small"
                  />
                  <span className="review-count">
                    ({productInfo?.reviews?.length || 0})
                  </span>
                </div>
              )}

              <div className="product-description">
                {productInfo.description}
              </div>

              <div className="product-variations">
                <div className="color-selection">
                  <span className="variation-title">Color</span>
                  <div className="color-options">
                    {productInfo?.attributes?.color?.map((color, index) => (
                      <div
                        key={index}
                        className={`color-option ${selectedColor?.hexCode === color.hexCode ? 'selected' : ''}`}
                        style={{ backgroundColor: color.hexCode }}
                        onClick={() => handleSelectColor(color)}
                      />
                    ))}
                  </div>
                </div>

                <div className="size-selection">
                  <span className="variation-title">Size</span>
                  <div className="size-options">
                    {availableSizes.map((size, index) => (
                      <div
                        key={index}
                        className={`size-option ${selectedSize === size ? 'selected' : ''}`}
                        onClick={() => handleSelectSize(size)}
                      >
                        {size}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="product-actions">
                <div className="quantity-actions">
                  <div className="quantity-selector">
                    <button
                      className="quantity-button minus"
                      onClick={() => handleUpdateQuantity("decrement")}
                      disabled={isProductInCart || quantity <= SETTINGS.PRODUCT_QTY_MIN}
                    >
                      −
                    </button>
                    <input
                      type="text"
                      className="quantity-input"
                      value={quantity}
                      readOnly
                    />
                    <button
                      className="quantity-button plus"
                      onClick={() => handleUpdateQuantity("increment")}
                      disabled={isProductInCart || quantity >= SETTINGS.PRODUCT_QTY_MAX}
                    >
                      +
                    </button>
                  </div>

                  <button
                    className="add-to-cart-btn"
                    onClick={handleAddToCart}
                    disabled={isApiCalling}
                  >
                    {isApiCalling
                      ? "Loading..."
                      : isProductInCart
                      ? SETTINGS.REMOVE_FROM_CART
                      : "Add To Cart"}
                  </button>

                  <Box
                    className="wishlist-btn"
                    onClick={handleAddToWishlist}
                  >
                    {isProductInWishlist ? (
                      <IconWishListFilled className="svgwishlist gc-svg-icon" />
                    ) : (
                      <IconWishListEmpty />
                    )}
                  </Box>
                </div>
              </div>

              <div className="view-details">
                <Link to={`/product-details/${productInfo._id}`} className="view-details-link">
                  View Full Details
                </Link>
              </div>
            </div>
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default QuickLookModal;
