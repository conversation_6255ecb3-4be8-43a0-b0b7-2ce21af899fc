import { useAddressQuery } from "api/user.api";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import "./Address.css";

const AddressList = () => {
  const { data: addressItems = [] } = useAddressQuery();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="address-list-container">
      <h4 className="anarkali-form-title">My Addresses</h4>

      {addressItems?.length > 0 ? (
        <div className="row">
          {addressItems.map((item) => (
            <div className="col-md-6 mb-4" key={item._id}>
              <div className="address-card">
                <div className="address-card-header">
                  <div className="address-type">{item?.nickName}</div>
                </div>
                <div className="address-card-body">
                  <div className="address-details">
                    <p>{item?.addressLine1}</p>
                    <p>{item?.city}, {item?.state} {item?.postalCode}</p>
                    <p>Phone: {item?.phone}</p>
                  </div>
                </div>
                <div className="address-card-footer">
                  <Link
                    to={`?addressId=${item._id}`}
                    className="address-action-btn"
                    style={{ color: 'white', textDecoration: 'none' }}
                  >
                    Edit Address
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="address-card">
          <div className="address-card-body text-center py-5">
            <p className="mb-4">You don't have any saved addresses yet.</p>
            <Link
              to="?tab=2"
              className="address-action-btn"
              style={{ color: 'white', textDecoration: 'none' }}
            >
              Add New Address
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddressList;
