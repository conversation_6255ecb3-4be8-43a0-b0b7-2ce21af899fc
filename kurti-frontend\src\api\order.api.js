import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api/api-client";
import { API_ENDPOINTS } from "globals/endpoints";

export const useOrdersQuery = ({ token }) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_ORDERS);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["orders", token],
  });

export const usePlaceOrderMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(API_ENDPOINTS.PLACE_ORDER, payload);
      return response;
    },
  });

export const useCreateOrderMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(
        API_ENDPOINTS.CREATE_ORDER,
        payload
      );
      return response;
    },
  });

export const useOrderInfoQuery = (id) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        `${API_ENDPOINTS.GET_ORDER_INFO}/${id}`
      );
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["order-info"],
    enabled: !!id,
  });

export const useUpdateOrderStatusMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.put(
        API_ENDPOINTS.UPDATE_ORDER_STATUS,
        payload
      );
      return response;
    },
  });

export const useTrackOrderQuery = (awb_code) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        `${API_ENDPOINTS.TRACK_ORDER}?awb_code=${awb_code}`
      );
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["track-info", awb_code],
    enabled: !!awb_code,
  });

export const useRefundOrderMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(
        API_ENDPOINTS.RAZORPAY_REFUND_ORDER,
        payload
      );
      return response;
    },
  });

export const useShiprocketReturnOrderMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(
        API_ENDPOINTS.SHIPROCKET_RETURN_ORDER,
        payload
      );
      return response;
    },
  });

export const useGenerateInvoiceMutation = () =>
  useMutation({
    mutationFn: async (orderId) => {
      const response = await apiClient.get(
        `${API_ENDPOINTS.GENERATE_INVOICE}/${orderId}/generate`
      );
      return response;
    },
  });

export const useDownloadInvoiceMutation = () =>
  useMutation({
    mutationFn: async (orderId) => {
      const response = await apiClient.get(
        `${API_ENDPOINTS.DOWNLOAD_INVOICE}/${orderId}/download`
      );
      return response;
    },
  });

export const useEmailInvoiceMutation = () =>
  useMutation({
    mutationFn: async (orderId) => {
      const response = await apiClient.post(
        `${API_ENDPOINTS.EMAIL_INVOICE}/${orderId}/email`
      );
      return response;
    },
  });

export const useWhatsappInvoiceMutation = () =>
  useMutation({
    mutationFn: async (orderId) => {
      const response = await apiClient.post(
        `${API_ENDPOINTS.WHATSAPP_INVOICE}/${orderId}/whatsapp`
      );
      return response;
    },
  });
