.profile-picture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
  position: relative;
}

.profile-picture-wrapper {
  position: relative;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  border: 4px solid white;
  transition: all 0.3s ease;
}

.profile-picture {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.profile-picture-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(108,94,188, 0.8);
  color: white;
  padding: 10px 0;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  opacity: 0;
  transition: all 0.3s ease;
}

.profile-picture-wrapper:hover {
  box-shadow: 0 10px 25px rgba(108,94,188, 0.2);
  transform: translateY(-5px);
}

.profile-picture-wrapper:hover .profile-picture {
  transform: scale(1.05);
}

.profile-picture-wrapper:hover .profile-picture-overlay {
  opacity: 1;
}

.upload-progress {
  width: 160px;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 15px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, #6c5ebc, #ff7676);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.error {
  color: #dc3545;
  font-size: 13px;
  margin-top: 8px;
  display: block;
  font-weight: 500;
}

/* Form styling */
.edit-account .form-group {
  margin-bottom: 25px;
}

.edit-account .form-control {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 12px 15px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.edit-account .form-control:focus {
  border-color: #6c5ebc;
  box-shadow: 0 0 0 3px rgba(108,94,188, 0.1);
}

.edit-account label {
  font-weight: 500;
  margin-bottom: 8px;
  color: #555;
  font-size: 14px;
}

.edit-account .btn-primary {
  background-color: #6c5ebc;
  border-color: #6c5ebc;
  padding: 12px 25px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(108,94,188, 0.2);
  transition: all 0.3s ease;
}

.edit-account .btn-primary:hover {
  background-color: #6c5ebc;
  border-color: #e04545;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(108,94,188, 0.3);
}

.edit-account .btn-primary:active {
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-picture-wrapper {
    width: 140px;
    height: 140px;
  }

  .upload-progress {
    width: 140px;
  }
}
