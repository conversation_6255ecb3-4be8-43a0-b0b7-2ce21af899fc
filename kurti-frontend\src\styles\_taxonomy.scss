.gc-taxonomy-list {
    //border: 1px solid var(--gc-border);
    display: grid;
    flex-direction: column;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
    margin-bottom: 60px;
    @media (max-width: 1400px) {
        grid-template-columns: repeat(4, 1fr);
    }

    @media (max-width: 1024px) {
        grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 767px) {
        grid-template-columns: repeat(1, 1fr);
    }

    .taxonomy-item {
        display: flex;
        gap: 10px;
        position: relative;
        min-height: 35px;
        padding: 5px 5px;
        border-radius: 50px;
        border: 1px solid var(--gc-border);

    }
    .taxonomy-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 15px;
        flex: 1;
    }
    .taxonomy-thumb {
        border-radius: 5px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 45px;
        min-width: 45px;
        border-radius: 50px;
        img {
            border-radius: 100%;
            max-width: 34px;
            max-height: 34px;
            i {
                font-size: 36px;
            }
            svg {
                width: 35px;
                height: 35px;
                max-width: 100%;
                max-height: 100%
            }

        }
    }
    .taxonomy-title {
        flex: 1 0 auto;
        text-transform: uppercase;
        font-size: 12px;
        font-weight: 500
    }
    .taxonomy-count {
        min-width: 45px;
        min-height: 45px;
        border-radius: 50px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--gc-purple-bg);
        color: var(--gc-purple-dark);
        position: relative;
        right: 0;
        top: 0;
    }
}

.gc-taxonomy-list,
.layout-slider {
    .taxonomy-details,
    .taxonomy-details {
        position: absolute;
        width: 100%;
        max-width: 320px;
        top: 100%;
        right: 0;
        background-color: var(--gc-light);
        border: 1px solid var(--gc-border);
        border-radius: 5px;
        z-index: 99;
        padding: 20px;
        opacity: 0;
        visibility: hidden;
        box-shadow: -4px -2px 30px 2px rgba(0, 0, 0, 0.17);
        transform: translateY(15px) translateZ(0);
        transition: opacity 0.4s cubic-bezier(.19, 1, .22, 1), visibility 0.4s cubic-bezier(.19, 1, .22, 1), transform 0.4s cubic-bezier(.19, 1, .22, 1);
        pointer-events: none
    }
}
.layout-slider {
    .taxonomy-details {
        &.active {
            opacity: 1;
            visibility: visible
        }
    }
}






.taxonomy-item .taxonomy-details:before,
.layout-slider .taxonomy-details:before {
    content: "";
    position: absolute;
    top: -8px;
    right: 13px;
    width: 15px;
    height: 15px;
    transform: rotate(45deg);
    border: 1px solid var(--gc-border);
    background: var(--gc-light);
    border-right: 0;
    border-bottom: 0
}

.gc-taxonomy-list .taxonomy-details.position-top {
    bottom: 100%;
    top: auto
}

.gc-taxonomy-list .taxonomy-details.position-right {
    top: 0;
    left: 100%
}

.gc-taxonomy-list .taxonomy-details.position-left {
    top: 0;
    right: 100%
}

.gc-taxonomy-list .taxonomy-details.position-top:before {
    top: auto;
    bottom: -8px;
    right: 12px;
    transform: rotate(-135deg)
}

.gc-taxonomy-list .taxonomy-details.position-left:before {
    top: 16px;
    right: -8px;
    transform: rotate(135deg)
}

.gc-taxonomy-list .taxonomy-details.position-right:before {
    top: 16px;
    left: -8px;
    transform: rotate(-45deg)
}

.taxonomy-item.active,
.taxonomy-item:hover {
    background-color: var(--gc-purple)
}

.taxonomy-item.active .taxonomy-thumb,
.taxonomy-item:hover .taxonomy-thumb {
    background-color: var(--gc-purple-bg)
}

.taxonomy-item.active .taxonomy-title,
.taxonomy-item:hover .taxonomy-title {
    color: var(--gc-light)
}

.taxonomy-item:hover .taxonomy-details {
    opacity: 1;
    visibility: visible;
    transform: none;
    pointer-events: unset
}

.taxonomy-item.active .taxonomy-count,
.taxonomy-item:hover .taxonomy-count {
    opacity: .8
}

.taxonomy-item a:hover {
    opacity: 1
}

.style-2 .gc-taxonomy-list .taxonomy-item {
    padding: 0
}

.style-2 .gc-taxonomy-list .taxonomy-thumb {
    border-radius: 0;
    height: 100%;
    min-width: 60px
}

.style-3 .gc-taxonomy-list,
.style-4 .gc-taxonomy-list {
    border: 0;
    gap: 3px
}

.style-3 .gc-taxonomy-list .taxonomy-item {
    border: 1px solid var(--gc-border);
    border-radius: 5px
}

.style-3 .gc-taxonomy-list .taxonomy-count {
    height: calc(100% - 4px);
    top: 50%;
    transform: translateY(-50%);
    right: 2px;
    border-radius: 5px
}

.style-3 .taxonomy-item:hover .taxonomy-count,
.style-4 .taxonomy-item:hover .taxonomy-count {
    opacity: 1;
    background-color: var(--gc-light);
    color: var(--gc-dark)
}

.style-4 .gc-taxonomy-list .taxonomy-item {
    border: 1px solid var(--gc-border);
    border-radius: 50px;
    padding: 5px
}

.style-4 .gc-taxonomy-list .taxonomy-count,
.style-4 .gc-taxonomy-list .taxonomy-thumb {
    border-radius: 50px;
    min-width: 45px;
    min-height: 45px
}

.style-4 .gc-taxonomy-list .taxonomy-thumb img {
    padding: 0;
    border-radius: 100%
}

.style-4 .gc-taxonomy-list .taxonomy-count {
    position: relative;
    right: auto;
    left: auto;
    top: auto;
    transform: none;
    font-size: 11px;
    font-weight: 500
}

.style-4 .gc-taxonomy-list .taxonomy-details.position-right:before {
    top: 15px
}

.layout-grid .gc-taxonomy-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px
}

.layout-grid .gc-taxonomy-list .taxonomy-item,
.layout-slider .gc-taxonomy-list .taxonomy-item {
    border: 1px solid var(--gc-border)
}

.gc-nav-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px
}

.gc-nav-bg {
    background-color: var(--gc-primary);
    border-color: var(--gc-primary);
    width: 30px;
    height: 30px
}

.gc-widget-taxonomy-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    --sgap: 20px
}

.taxonomy-col-wrapper {
    width: 300px
}

.products-col-wrapper {
    width: calc(100% - (300px + var(--sgap)))
}

.gc-tab-taxonomy .gc-swiper-container:not(.active),
.gc-tab-taxonomy .gc-woocommerce-pagination:not(.active) {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    z-index: -1
}

.gc-tab-taxonomy.style-border-outside .gc-swiper-container {
    border-left: 1px solid var(--gc-border)
}

.gc-tab-taxonomy.style-border-outside .gc-loop-product {
    border-top: 1px solid var(--gc-border)
}

.gc-tab-taxonomy.style-border-outside .gc-loop-product:hover {
    border: 1px solid var(--gc-primary);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    transition: none;
    transform: none
}

.gc-tab-taxonomy .slider-wrapper {
    position: relative
}

.gc-tab-taxonomy .gc-woocommerce-pagination {
    margin-top: 30px
}

.gc-tab-taxonomy .gc-swiper-prev,
.gc-tab-taxonomy .gc-swiper-next {
    position: absolute;
    width: 40px;
    height: 40px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gc-light);
    background-color: var(--gc-primary);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden
}

.gc-tab-taxonomy .slider-wrapper:hover .gc-swiper-prev,
.gc-tab-taxonomy .slider-wrapper:hover .gc-swiper-next {
    opacity: 1;
    visibility: visible
}

.gc-tab-taxonomy .gc-nav-bg:hover {
    background-color: var(--gc-primary);
    border-color: var(--gc-primary);
    color: var(--gc-light)
}

.gc-tab-taxonomy .gc-swiper-next {
    right: 0;
    left: auto
}

@media(max-width:768px) {
    .gc-taxonomy-list .taxonomy-details.position-right {
        display: none
    }
}

@media(max-width:480px) {
    .taxonomy-col-wrapper {
        width: 100%
    }

    .products-col-wrapper {
        width: 100%
    }
}