stages:
  - build
  - deploy

variables:
  FRONTEND_BUILD_DIR: kurti-frontend/dist
  DASHBOARD_BUILD_DIR: kurti-dashboard/dist

build-react:
  image: node:20  # ✅ Use a Node image that has npm
  stage: build
  script:
    - echo "📦 Installing frontend dependencies..."
    - cd kurti-frontend
    - npm install --legacy-peer-deps
    - npm run build
    - cd ../kurti-dashboard
    - npm install --legacy-peer-deps
    - npm run build
  artifacts:
    paths:
      - $FRONTEND_BUILD_DIR
      - $DASHBOARD_BUILD_DIR

deploy-to-vps:
  stage: deploy
  only:
    - develop
  script:
    - echo "🔐 Setting up SSH"
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa

    - echo "📤 Copying frontend build to VPS..."
    - scp -o StrictHostKeyChecking=no -r kurti-frontend/dist/* root@*************:/var/www/html/gajancreation.com/
    - scp -o StrictHostKeyChecking=no -r kurti-dashboard/dist/* root@*************:/var/www/html/admin.gajancreation.com/

    - echo "🧹 Removing old React code from VPS..."
    - |
      ssh -o StrictHostKeyChecking=no root@************* << 'EOF'
        rm -rf /var/www/html/ecommerce-store/kurti-frontend
        rm -rf /var/www/html/ecommerce-store/kurti-dashboard
      EOF

    - echo "🚀 Deploying backend via SSH..."
    - |
      ssh -o StrictHostKeyChecking=no root@************* << 'EOF'
        cd /var/www/html/ecommerce-store
        git reset --hard
        git clean -fd
        git pull origin develop

        echo "📦 Installing backend deps and restarting API..."
        cd backend
        npm install
        pm2 restart GC-API-SUBDOMAIN || pm2 start src/api/index.js --name GC-API-SUBDOMAIN
      EOF
