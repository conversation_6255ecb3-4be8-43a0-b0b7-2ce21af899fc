import { useState, useEffect } from "react";
import { usePincodeAvailabilityMutation } from "api/util.api";
import { toast } from "react-toastify";

const CheckOutForm = ({ formik }) => {
  const [pincode, setPincode] = useState("");
  const [debouncedPincode, setDebouncedPincode] = useState("");
  const { mutateAsync: checkAvailability } = usePincodeAvailabilityMutation();

  const renderError = (fieldName) => {
    if (formik.touched[fieldName] && formik.errors[fieldName]) {
      return <span className="error">{formik.errors[fieldName]}</span>;
    }
    return null;
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedPincode(pincode);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [pincode]);

 
  useEffect(() => {
    const checkPincode = async () => {
      if (!debouncedPincode) return;

      const result = await checkAvailability({ pincode: debouncedPincode });

      if (!result?.data?.data) {
        toast.error(result?.data?.message);
        return;
      }
      toast.success('Delivery available!')
      if(result?.data?.data?.available_courier_companies?.length){
        const {city, state} = result?.data?.data?.available_courier_companies[0] ?? {};
        formik.setFieldValue('city', city)
        formik.setFieldValue('state', state)
      }
    };

    checkPincode();
  }, [debouncedPincode]);

  const onChangePincode = (e) => {
    setPincode(e.target.value);
    formik.handleChange(e);
  };

  return (
    <form>
      <div className="woocommerce-billing-fields__field-wrapper">
        <p className="form-row form-row-first validate-required">
          <label for="billing_first_name" className="">
            Address Type&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              name="nickName"
              placeholder="Enter address type"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.nickName}
            />
          </span>
          {renderError("nickName")}
        </p>
        <p className="form-row form-row-first validate-required">
          <label for="billing_first_name" className="">
            Full name&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              name="fullName"
              placeholder="Enter your full name"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.fullName}
            />
          </span>
          {renderError("fullName")}
        </p>
        <p className="form-row form-row-wide">
          <label for="billing_company" className="">
            Address&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              placeholder="Enter your address"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              name="addressLine1"
              value={formik.values.addressLine1}
            />
          </span>
          {renderError("addressLine1")}
        </p>
        <p className="form-row form-row-wide">
          <label for="billing_company" className="">
            Phone&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              placeholder="Enter your phone number"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              name="phone"
              value={formik.values.phone}
            />
          </span>
          {renderError("phone")}
        </p>
        <p className="form-row form-row-wide validate-required validate-email">
          <label for="billing_email" className="">
            Email address&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="email"
              className="input-text "
              placeholder="Enter your email address"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              name="email"
              value={formik.values.email}
            />
          </span>
          {renderError("email")}
        </p>
        <p className="form-row form-row-wide">
          <label for="billing_company" className="">
            City&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              placeholder="Enter your city"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              name="city"
              value={formik.values.city}
            />
          </span>
          {renderError("city")}
        </p>
        <p className="form-row form-row-wide">
          <label for="billing_company" className="">
            State&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              placeholder="Enter your state"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              name="state"
              value={formik.values.state}
            />
          </span>
          {renderError("state")}
        </p>
        <p className="form-row form-row-wide">
          <label for="billing_company" className="">
            Country&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              placeholder="Enter your country"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              name="country"
              value={formik.values.country}
            />
          </span>
          {renderError("country")}
        </p>
        <p className="form-row form-row-wide">
          <label for="billing_company" className="">
            Postal Code&nbsp;
            <abbr className="required" title="required">
              *
            </abbr>
          </label>
          <span className="woocommerce-input-wrapper">
            <input
              type="text"
              className="input-text "
              placeholder="Enter your postalCode"
              onChange={onChangePincode}
              onBlur={formik.handleBlur}
              name="postalCode"
              value={formik.values.postalCode}
            />
          </span>
          {renderError("postalCode")}
        </p>
      </div>
    </form>
  );
};

export default CheckOutForm;
