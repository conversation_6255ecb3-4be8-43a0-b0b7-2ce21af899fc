const {
    models: { Product, Wishlist },
} = require('../../../../lib/models');

class WishlistController {
    async addToWishlist(req, res) {
        const userId = req.user._id;
        const { productId } = req.body;

        try {
            let wishlist = await Wishlist.findOne({ userId });

            if (!wishlist) {
                wishlist = new Wishlist({ userId, products: [] });
            }

            if (wishlist.products.some(item => item.productId.toString() === productId)) {
                return res.warn({}, req.__('Product already in wishlist'));
            }

            wishlist.products.push({ productId });

            await wishlist.save();
            const wishlistdata = await Wishlist.findOne({ userId }).populate(
                'products.productId',
                'name prices mainImage'
            );
            return res.success(wishlistdata, req.__('Product added to wishlist'));
        } catch (error) {
            console.log('Error adding product to wishlist:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async removeFromWishlist(req, res) {
        const userId = req.user._id;
        const { productId } = req.params;

        try {
            let wishlist = await Wishlist.findOne({ userId });

            if (!wishlist) {
                return res.notFound({}, req.__('Wishlist not found'));
            }

            if (productId === 'all') {
                // Clear all items from the wishlist
                wishlist.products = [];
                await wishlist.save();
                return res.success(wishlist, req.__('All products removed from wishlist'));
            } else {
                // Find the product index to remove specific product
                const productIndex = wishlist.products.findIndex(
                    item => item.productId.toString() === productId.toString()
                );
                if (productIndex === -1) {
                    return res.notFound({}, req.__('Product not found in wishlist'));
                }

                wishlist.products.splice(productIndex, 1);
                await wishlist.save();
                return res.success(wishlist, req.__('Product removed from wishlist'));
            }
        } catch (error) {
            console.log('Error removing product from wishlist:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getWishlist(req, res) {
        const userId = req.user._id;

        try {
            const wishlist = await Wishlist.findOne({ userId }).populate('products.productId', 'name prices mainImage attributes');
            if (wishlist && wishlist.products.length > 0) {
                wishlist.products = wishlist.products.map(item => {
                    const product = JSON.parse(JSON.stringify(item));
                    product.productId.mainImage = product?.productId?.attributes?.color?.[0]?.images?.mainImage?.url;
                    return product;
                });
            }
            if (!wishlist) {
                return res.success({}, req.__('Wishlist is empty'));
            }
            return res.success(wishlist);
        } catch (error) {
            console.log('Error fetching wishlist:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}
module.exports = new WishlistController();
