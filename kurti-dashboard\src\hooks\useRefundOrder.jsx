import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { useCreateQuickOrderMutation, useRefundOrderMutation } from "services";
import { setConfirmModalConfig } from "store/slices/utilSlice";

const useRefundOrder = ({ refetch }) => {
  const [createQuickOrder] = useCreateQuickOrderMutation();
  const [refundOrder] = useRefundOrderMutation();
  const dispatch = useDispatch();

  const handleRefundOrder = async (orderId) => {
    try {
      const refundResult = await refundOrder({ orderId });
      if (refundResult?.success) {
        toast.success("Refund initiated successfully!");
        refetch();
      } else {
        toast.error("Something went wrong!");
      }
    } catch (err) {
      console.log(err);
    }
  };

  const onClickAccept = (orderId) => {
    dispatch(
      setConfirmModalConfig({
        visible: true,
        data: {
          onSubmit: () => handleRefundOrder(orderId),
          content: {
            heading: "Initiate Refund",
            description: "Are you sure you want to initiate refund?",
          },
        },
      })
    );
  };

  return { onClickAccept };
};

export default useAcceptOrder;
