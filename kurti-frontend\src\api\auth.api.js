import { useMutation } from "@tanstack/react-query";
import { apiClient } from "api/api-client";
import { auth } from "config/firebaseConfig";
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  signOut,
} from "firebase/auth";
import { API_ENDPOINTS } from "globals/endpoints";
import { toast } from "react-toastify";

// Email/Password Authentication
export const useEmailPasswordSignIn = () =>
  useMutation({
    mutationFn: async ({ email, password }) => {
      try {
        // Step 1: Authenticate with Firebase
        const userCredential = await signInWithEmailAndPassword(
          auth,
          email,
          password
        );
        const user = userCredential.user;
        const firebaseToken = await user.getIdToken();

        // Step 2: Register with backend if needed
        try {
          await apiClient.post(API_ENDPOINTS.SIGNUP, {
            email: user.email,
            fcmId: user.uid,
            isSocial: false,
          });
        } catch (error) {
          // If user already exists, continue with login
          if (!error.response || error.response.status !== 409) {
            console.warn("Non-critical signup error:", error);
          }
        }

        // Step 3: Exchange Firebase token for backend token
        try {
          const response = await apiClient.post(API_ENDPOINTS.EXCHANGE_TOKEN, {
            firebaseToken,
          });

          if (response?.success && response?.data?.token) {
            console.log("Token exchange successful");

            // Return user info with backend token
            return {
              token: response.data.token, // Use backend token
              user: {
                _id: user.uid,
                email: user.email,
                displayName: user.displayName || user.email.split("@")[0],
                photoURL: user.photoURL,
                emailVerified: user.emailVerified,
              },
            };
          } else {
            console.error("Token exchange failed:", response);
            throw new Error("Failed to exchange token");
          }
        } catch (error) {
          console.error("Token exchange error:", error);

          // Fallback to Firebase token if exchange fails
          console.warn("Using Firebase token as fallback");
          return {
            token: firebaseToken,
            user: {
              _id: user.uid,
              email: user.email,
              displayName: user.displayName || user.email.split("@")[0],
              photoURL: user.photoURL,
              emailVerified: user.emailVerified,
            },
          };
        }
      } catch (error) {
        console.error("Email/Password Sign-In Error:", error);
        if (error?.code === "auth/invalid-credential") {
          throw new Error("Invalid email or password");
        }
        throw new Error(
          error.message || "Failed to sign in with email/password"
        );
      }
    },
  });

export const useEmailPasswordSignUp = () =>
  useMutation({
    mutationFn: async ({ email, password }) => {
      try {
        // Step 1: Create user with Firebase
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          email,
          password
        );
        const user = userCredential.user;
        const firebaseToken = await user.getIdToken();

        // Step 2: Register with backend
        try {
          await apiClient.post(API_ENDPOINTS.SIGNUP, {
            email: user.email,
            fcmId: user.uid,
            isSocial: false,
          });
        } catch (error) {
          // If user already exists, continue with login
          if (!error.response || error.response.status !== 409) {
            console.warn("Non-critical signup error:", error);
          }
        }

        // Step 3: Exchange Firebase token for backend token
        try {
          const response = await apiClient.post(API_ENDPOINTS.EXCHANGE_TOKEN, {
            firebaseToken,
          });

          if (response?.success && response?.data?.token) {
            console.log("Token exchange successful");

            // Return user info with backend token
            return {
              token: response.data.token, // Use backend token
              user: {
                _id: user.uid,
                email: user.email,
                displayName: user.displayName || user.email.split("@")[0],
                photoURL: user.photoURL,
                emailVerified: user.emailVerified,
              },
            };
          } else {
            console.error("Token exchange failed:", response);
            throw new Error("Failed to exchange token");
          }
        } catch (error) {
          console.error("Token exchange error:", error);

          // Fallback to Firebase token if exchange fails
          console.warn("Using Firebase token as fallback");
          return {
            token: firebaseToken,
            user: {
              _id: user.uid,
              email: user.email,
              displayName: user.displayName || user.email.split("@")[0],
              photoURL: user.photoURL,
              emailVerified: user.emailVerified,
            },
          };
        }
      } catch (error) {
        console.error("Email/Password Sign-Up Error:", error);
        throw new Error(
          error.message || "Failed to sign up with email/password"
        );
      }
    },
  });

export const useResetPassword = () =>
  useMutation({
    mutationFn: async (email) => {
      try {
        await sendPasswordResetEmail(auth, email);
        return { success: true };
      } catch (error) {
        console.error("Reset Password Error:", error);
        throw new Error(error.message || "Failed to send password reset email");
      }
    },
  });

export const useSignOut = () =>
  useMutation({
    mutationFn: async () => {
      try {
        await signOut(auth);
        return { success: true };
      } catch (error) {
        console.error("Sign Out Error:", error);
        throw new Error(error.message || "Failed to sign out");
      }
    },
  });

// Token Exchange
export const useExchangeTokenMutation = () =>
  useMutation({
    mutationFn: async (firebaseToken) => {
      try {
        console.log("Exchanging Firebase token for backend token");
        const response = await apiClient.post(API_ENDPOINTS.EXCHANGE_TOKEN, {
          firebaseToken,
        });

        if (response?.success && response?.data?.token) {
          console.log("Token exchange successful");
          return response.data.token;
        } else {
          console.error("Token exchange failed:", response);
          throw new Error("Failed to exchange token");
        }
      } catch (error) {
        console.error("Token exchange error:", error);
        throw error;
      }
    },
  });

// Backend API calls
export const useSignupMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(API_ENDPOINTS.SIGNUP, payload);
      return response;
    },
  });

export const useCheckUserDisabledMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(API_ENDPOINTS.CHECK_USER_DISABLED, payload);
      return response;
    },
  });
