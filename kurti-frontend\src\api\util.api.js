import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./api-client";

export const usePincodeAvailabilityMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.get(
        API_ENDPOINTS.PINCODE_SERVICE_ABILITY,
        {
          params: payload,
        }
      );
      return response;
    },
  });

export const useContactUsMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(API_ENDPOINTS.CONTACT_US, payload);
      return response;
    },
  });

export const useGeneralSettingsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GENERAL_SETTINGS);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["general-setting"],
  });

  export const useStaticPageQuery = ({slug}) =>
   useQuery({
    queryFn: async () => {
      const response = await apiClient.get(`${API_ENDPOINTS.STATIC_PAGE}${slug}`);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["static-page"],
  });
