const { models: { Order, User } } = require('../../../../lib/models');
const { generateInvoice } = require('../../../../lib/invoice');
const { sendInvoice } = require('../../../../lib/whatsapp');
const { sendMail } = require('../../../../lib/mailer');
const fs = require('fs');
const path = require('path');

class InvoiceController {
    /**
     * Generate an invoice for an order
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @returns {Object} - Response with invoice URL
     */
    async generateInvoice(req, res) {
        const { orderId } = req.params;
        const userId = req.user._id;

        try {
            // Find the order
            const order = await Order.findOne({
                _id: orderId,
                user: userId
            }).populate('user');

            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            // Create temp directory if it doesn't exist
            const tempDir = path.join(__dirname, '../../../../temp');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Generate the invoice
            const invoicePath = await generateInvoice(order, order.user);

            // Return the invoice URL
            return res.success({
                invoiceUrl: `/api/orders/invoice/${orderId}/download`,
                invoicePath
            }, req.__('INVOICE_GENERATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error generating invoice:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /**
     * Download an invoice
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async downloadInvoice(req, res) {
        const { orderId } = req.params;
        const userId = req.user._id;

        try {
            // Find the order
            const order = await Order.findOne({
                _id: orderId,
                user: userId
            }).populate('user');

            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            // Generate the invoice
            const invoicePath = await generateInvoice(order, order.user);

            // Set headers for file download (force download, not display)
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="invoice-${order.orderId}.pdf"`);
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');

            // Stream the file to the response
            const fileStream = fs.createReadStream(invoicePath);
            fileStream.pipe(res);
        } catch (error) {
            console.error('Error downloading invoice:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /**
     * Public download an invoice (for email/WhatsApp links)
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     */
    async publicDownloadInvoice(req, res) {
        const { orderId } = req.params;
        const { token } = req.query;

        try {
            // Verify the token if provided
            let userId = null;
            if (token) {
                try {
                    const jwt = require('jsonwebtoken');
                    // Remove 'Bearer ' prefix if present
                    const cleanToken = token.startsWith('Bearer ') ? token.slice(7) : token;
                    const decoded = jwt.verify(cleanToken, process.env.JWT_SECRET);

                    // The auth system uses 'sub' for user ID, not '_id'
                    userId = decoded.sub || decoded._id || decoded.id;
                    console.log('Token verified successfully for user:', userId);
                    console.log('Token payload:', { sub: decoded.sub, _id: decoded._id, id: decoded.id, aud: decoded.aud });
                } catch (tokenError) {
                    console.error('Token verification failed:', tokenError.message);
                    return res.status(401).json({
                        success: false,
                        message: 'Invalid or expired token',
                        debug: process.env.NODE_ENV === 'development' ? tokenError.message : undefined
                    });
                }
            }

            // Find the order (with or without user verification)
            const orderQuery = { _id: orderId };
            if (userId) {
                orderQuery.user = userId;
                console.log('Searching for order with user verification:', { orderId, userId });
            } else {
                console.log('Searching for order without user verification:', { orderId });
            }

            const order = await Order.findOne(orderQuery).populate('user');

            if (!order) {
                console.log('Order not found:', { orderId, userId });
                return res.status(404).json({
                    success: false,
                    message: 'Order not found'
                });
            }

            console.log('Order found successfully:', { orderId: order._id, orderUserId: order.user?._id });

            // Generate the invoice
            const invoicePath = await generateInvoice(order, order.user);

            // Set headers for file download (force download, not display)
            res.setHeader('Content-Type', 'application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename="invoice-${order.orderId}.pdf"`);
            res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            res.setHeader('Pragma', 'no-cache');
            res.setHeader('Expires', '0');

            // Stream the file to the response
            const fileStream = fs.createReadStream(invoicePath);
            fileStream.pipe(res);
        } catch (error) {
            console.error('Error downloading invoice:', error);
            return res.status(500).json({
                success: false,
                message: 'Internal server error'
            });
        }
    }

    /**
     * Send an invoice via email
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @returns {Object} - Response with success message
     */
    async emailInvoice(req, res) {
        const { orderId } = req.params;
        const userId = req.user._id;

        try {
            // Find the order
            const order = await Order.findOne({
                _id: orderId,
                user: userId
            }).populate('user');

            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            // Generate the invoice
            const invoicePath = await generateInvoice(order, order.user);

            // Send the invoice via email
            await sendMail(
                'invoice-email',
                `Your Invoice for Order #${order.orderId}`,
                order.user.email,
                {
                    order,
                    invoicePath
                }
            );

            return res.success({}, req.__('INVOICE_SENT_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error sending invoice via email:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    /**
     * Send an invoice via WhatsApp
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @returns {Object} - Response with success message
     */
    async whatsappInvoice(req, res) {
        const { orderId } = req.params;
        const userId = req.user._id;

        try {
            // Find the order
            const order = await Order.findOne({
                _id: orderId,
                user: userId
            }).populate('user');

            if (!order) {
                return res.notFound({}, req.__('ORDER_NOT_FOUND'));
            }

            // Check if phone number is available
            if (!order.shippingAddress.phone) {
                return res.badRequest({}, req.__('PHONE_NUMBER_REQUIRED'));
            }

            // Generate the invoice
            const invoicePath = await generateInvoice(order, order.user);

            // Format the phone number (ensure it has country code)
            let phoneNumber = order.shippingAddress.phone;
            if (!phoneNumber.startsWith('+')) {
                // Add India country code by default if not present
                phoneNumber = `+91${phoneNumber}`;
            }

            // Send the invoice via WhatsApp
            await sendInvoice(phoneNumber, invoicePath, order);

            return res.success({}, req.__('INVOICE_SENT_SUCCESSFULLY_VIA_WHATSAPP'));
        } catch (error) {
            console.error('Error sending invoice via WhatsApp:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new InvoiceController();
