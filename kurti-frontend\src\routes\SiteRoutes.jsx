import {
  AboutUs,
  Cart,
  Checkout,
  Home,
  MyOrders,
  NotFound,
  ProductCategory,
  ProductDetail,
  Signin,
  Signup,
  ContactUs,
  ViewOrder,
  Wishlist,
  PrivacyPolicy,
  TermsAndConditions,
  ReturnAndExchangePolicy,
  Dashboard,
  Faq,
} from "pages";
import Products from "pages/Products";
import { Route, Routes } from "react-router-dom";
import useUserStore from "stores/user";
import ProtectedRoute from "./ProtectedRoute";

const SiteRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/category" element={<ProductCategory />} />
      <Route path="/products" element={<Products />} />
      <Route path="/product-details/:id" element={<ProductDetail />} />
      <Route path="/about-us" element={<AboutUs />} />
      <Route path="/sign-in" element={<Signin />} />
      <Route path="/sign-up" element={<Signup />} />
      <Route path="/cart" element={<Cart />} />
      <Route element={<ProtectedRoute />}>
        <Route path="/checkout" element={<Checkout />} />
        <Route path="/my-orders" element={<MyOrders />} />
        <Route path="/view-order/:id" element={<ViewOrder />} />
        <Route path="/dashboard" element={<Dashboard />} />
      </Route>
      <Route path="/wishlist" element={<Wishlist />} />
      <Route path="*" element={<NotFound />} />
      <Route path="/about" element={<AboutUs />} />
      <Route path="/contact" element={<ContactUs />} />
      <Route path="/privacy-policy" element={<PrivacyPolicy />} />
      <Route path="/terms-conditions" element={<TermsAndConditions />} />
      <Route path="/return-policy" element={<ReturnAndExchangePolicy />} />
      <Route path="/faq" element={<Faq />} />
    </Routes>
  );
};

export default SiteRoutes;
