const express = require('express');
const router = express.Router();
const CouponController = require('./CouponController');
const validations = require('./CouponValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin, verifyTokenAdmin } = require('../../util/auth');

router.get('/:id', CouponController.detail);
router.put('/:id/:status', verifyTokenAdmin, CouponController.activeToggle);
router.get('/', CouponController.list);
router.post('/', verifyTokenAdmin, validate(validations.addCoupon), CouponController.add);
router.post('/validate', verifyTokenUserOrAdmin, CouponController.couponValidate);

module.exports = router;
