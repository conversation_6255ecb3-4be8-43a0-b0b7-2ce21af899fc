const express = require('express');
const router = express.Router();
const UtilController = require('./UtilController');
const validations = require('./UtilValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');

router.get('/upload-file', verifyToken, validate(validations.uploadFile, 'query'), UtilController.uploadFile);
router.get('/delhivery-pincode-serviceability', UtilController.pincodeServiceability);
router.get('/delhivery-track-shipment', UtilController.delhiveryTrackShipment);
router.post('/contact-us', UtilController.contactUs);
router.get('/dashboard',verifyTokenUserOrAdmin, UtilController.dashboard);

module.exports = router;
