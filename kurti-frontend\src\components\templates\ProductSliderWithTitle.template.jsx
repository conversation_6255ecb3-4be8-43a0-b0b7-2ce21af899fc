import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import ProductSliderTemplate from "./ProductSlider.template";
import { IconRightArrow } from "utils/icons";
import { Link } from "react-router-dom";

const ProductSliderWithTitleTemplate = ({
  title,
  products = [],
  sideLink = "",
  sideLinkText = "",
}) => {
  return (
    <div className="container">
      <div className="gc-tab-header">
        <h3 className="gc-tab-title text-capitalize">{title}</h3>
        <Link
          className="gc-tab-button gc-btn gc-btn-secondary gc-btn-text gc-square has-icon icon-after order-lg-3"
          to="/products"
        >
          <IconRightArrow />
          <span className="btn-text" data-hover="All Products"></span>
        </Link>
      </div>
      {sideLink && (
        <Link
          className="gc-tab-button gc-btn gc-btn-secondary gc-btn-text gc-square has-icon icon-after"
          to="/category"
        >
          <IconRightArrow />
          <span className="btn-text" data-hover={sideLinkText}></span>
        </Link>
      )}
      <div className="tab-content">
        <ProductSliderTemplate products={products} />
      </div>
    </div>
  );
};

export default ProductSliderWithTitleTemplate;
