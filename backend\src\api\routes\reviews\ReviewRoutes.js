const express = require('express');
const router = express.Router();
const ReviewController = require('./ReviewController');
const validations = require('./ReviewValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenAdmin, verifyTokenUserOrAdmin } = require('../../util/auth');

// User routes
router.post('/', verifyTokenUserOrAdmin, validate(validations.addReview), ReviewController.addReview);
router.put('/', verifyTokenUserOrAdmin, validate(validations.updateReview), ReviewController.updateReview);
router.delete('/:productId/:reviewId', verifyTokenUserOrAdmin, ReviewController.deleteReview);
router.get('/product/:productId', ReviewController.getProductReviews);

// Admin routes
router.get('/all', verifyTokenAdmin, ReviewController.getAllReviews);

module.exports = router;
