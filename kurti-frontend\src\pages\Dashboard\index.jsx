import React, { useEffect } from "react";
import { Box, Tab, Tabs, Typography, useMediaQuery } from "@mui/material";
import { useOrdersQuery } from "api/order.api";
import ResponsiveOrderList from "components/ResponsiveOrderList";
import { useNavigate, useSearchParams } from "react-router-dom";
import { setCartItems } from "stores/cart";
import { resetState } from "stores/user";
import { setWishlistItems } from "stores/wishlist";
import "../../styles/dashboard.scss";
import AddAddress from "./AddAddress";
import AddressList from "./AddressList";
import MyProfile from "./MyProfile";

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vertical-tabpanel-${index}`}
      aria-labelledby={`vertical-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={2}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `vertical-tab-${index}`,
    "aria-controls": `vertical-tabpanel-${index}`,
  };
}

const Dashboard = () => {
  const [value, setValue] = React.useState(0);
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const addressId = searchParams.get("addressId");
  const isMobile = useMediaQuery("(max-width: 768px)"); // Detect mobile screens

  const handleChange = (_, newValue) => {
    setValue(newValue);
    setSearchParams({});
  };

  const handleLogout = () => {
    resetState();
    setCartItems([]);
    setWishlistItems([]);
    navigate("/");
  };

  useEffect(() => {
    if (addressId) {
      setValue(1);
    }
  }, [addressId]);

  return (
    <div className="dashboard-page gc-myaccount-page-content pt-30">
      <div className="container">
        <div className="gc-myaccount-page-content-inner">
          <div className="row">
            {/* Tabs Section */}
            <div className={isMobile ? "col-12" : "col-12 col-sm-4 col-md-3"}>
              <Tabs
                orientation={isMobile ? "horizontal" : "vertical"}
                variant="scrollable"
                value={value}
                onChange={handleChange}
                aria-label="Dashboard navigation"
                sx={{
                  borderRight: isMobile ? "none" : 1,
                  borderBottom: isMobile ? 1 : "none",
                  borderColor: "divider",
                }}
                className="gc-myaccount-navigation"
              >
                <Tab label="Orders" {...a11yProps(0)} />
                <Tab label="Addresses" {...a11yProps(1)} onClick={() => handleChange(null, 1)} />
                <Tab label="Add Address" {...a11yProps(2)} />
                <Tab label="My Profile" {...a11yProps(3)} />
                <Tab label="Logout" onClick={handleLogout} className="logout-tab" />
              </Tabs>
            </div>

            {/* Content Section */}
            <div className={isMobile ? "col-12" : "col-12 col-sm-8 col-md-9 col-content"}>
              <div className="gc-myaccount-content">
                <TabPanel value={value} index={0}>
                  <h4>My Orders</h4>
                  <ResponsiveOrderList />
                </TabPanel>
                <TabPanel value={value} index={1}>
                  {addressId ? <AddAddress setValue={setValue} /> : <AddressList />}
                </TabPanel>
                <TabPanel value={value} index={2}>
                  <AddAddress setValue={setValue} />
                </TabPanel>
                <TabPanel value={value} index={3}>
                  <MyProfile />
                </TabPanel>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
