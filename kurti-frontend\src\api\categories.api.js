import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./api-client";

export const useCategoriesQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_CATEGORIES);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["categories"],
  });

export const useGetSubCategoriesQuery = (catId) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(`${API_ENDPOINTS.GET_CATEGORIES}?catId=${catId}`);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["sub-categories"],
    enabled: !!catId
  });
