import { useProductsQuery } from "api/product.api";
import ProductSliderWithTitleTemplate from "./ProductSliderWithTitle.template";

const ProductSectionTemplate = ({ section }) => {
  const { data: { items: products = [] } = {} } = useProductsQuery({
    tags: section?.tag ?? "Trending",
  });
  return (
    <div className="nt-gc-inner-container pt-10">
      <ProductSliderWithTitleTemplate
        title={section?.name}
        products={products}
        sideLink=""
        sideLinkText=""
      />
    </div>
  );
};

export default ProductSectionTemplate;
