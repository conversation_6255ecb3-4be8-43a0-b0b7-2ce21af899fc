import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import ToggleOnIcon from "@mui/icons-material/ToggleOn";
import ToggleOffIcon from "@mui/icons-material/ToggleOff";
import { Box, IconButton, Tooltip, useTheme, Button, TextField, MenuItem, Select, FormControl, InputLabel, Chip } from "@mui/material";
import Header from "components/Header";
import StyledDataGrid from "components/datagrids/StyledDataGrid";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useGetFaqsQuery, useDeleteFaqMutation, useToggleFaqStatusMutation, useGetFaqCategoriesQuery } from "services";
import { tokens } from "theme";
import dataGridStyles from "../../styles/dataGridStyles";
import { useDispatch } from "react-redux";
import { setConfirmModalConfig } from "store/slices/utilSlice";
import { toast } from "react-toastify";

const List = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const styles = dataGridStyles(theme.palette.mode);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // State
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [params, setParams] = useState({
    page: 1,
    limit: 10
  });

  // Queries
  const { data: faqsData, isLoading, refetch } = useGetFaqsQuery({ params: {...params, isAllTrue: true} });
  const { data: categoriesData = [] } = useGetFaqCategoriesQuery();

  // Mutations
  const [deleteFaq] = useDeleteFaqMutation();
  const [toggleFaqStatus] = useToggleFaqStatusMutation();

  // Effects
  useEffect(() => {
    const newParams = {
      page: page + 1,
      limit: pageSize,
    };

    if (searchTerm) {
      newParams.search = searchTerm;
    }

    if (selectedCategory) {
      newParams.category = selectedCategory;
    }

    setParams(newParams);
  }, [page, pageSize, searchTerm, selectedCategory]);

  // Handlers
  const handleEdit = (id) => {
    navigate(`/faqs/edit/${id}`);
  };

  const handleDelete = (id) => {
    dispatch(
      setConfirmModalConfig({
        visible: true,
        data: {
          onSubmit: async () => {
            try {
              await deleteFaq(id).unwrap();
              toast.success("FAQ deleted successfully");
              refetch();
            } catch (error) {
              toast.error("Failed to delete FAQ");
            }
          },
          content: {
            heading: "Delete FAQ?",
            description: "Are you sure you want to delete this FAQ?",
          },
        },
      })
    );
  };

  const handleToggleStatus = async (id) => {
    try {
      await toggleFaqStatus(id).unwrap();
      toast.success("FAQ status updated successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to update FAQ status");
    }
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setPage(0); // Reset to first page on search
  };

  const handleCategoryChange = (e) => {
    setSelectedCategory(e.target.value);
    setPage(0); // Reset to first page on category change
  };

  // Column definitions
  const columns = [
    {
      field: "question",
      headerName: "Question",
      flex: 2,
      renderCell: ({ row }) => {
        return (
          <Box>
            <Tooltip title={row.question} arrow>
              <span>{row.question}</span>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: "answer",
      headerName: "Answer",
      flex: 2,
      renderCell: ({ row }) => {
        return (
          <Box>
            <Tooltip title={row.answer} arrow>
              <span>{row.answer.substring(0, 50)}...</span>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: "category",
      headerName: "Category",
      flex: 1,
    },
    {
      field: "sortOrder",
      headerName: "Sort Order",
      flex: 0.5,
    },
    {
      field: "isSuspended",
      headerName: "Status",
      flex: 0.8,
      renderCell: ({ row }) => {
        return (
          <Chip
            label={row.isSuspended ? "Inactive" : "Active"}
            color={row.isSuspended ? "error" : "success"}
            size="small"
            variant="outlined"
          />
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 1,
      renderCell: ({ row }) => {
        return (
          <Box>
            <Tooltip title="Edit">
              <IconButton onClick={() => handleEdit(row._id)}>
                <EditIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton onClick={() => handleDelete(row._id)}>
                <DeleteIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title={row.isSuspended ? "Activate" : "Deactivate"}>
              <IconButton onClick={() => handleToggleStatus(row._id)}>
                {row.isSuspended ? <ToggleOffIcon /> : <ToggleOnIcon />}
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  return (
    <Box m="20px">
      <Box
        sx={{
          ...styles.headingsContainer,
          ...styles.dFlex,
          ...styles.alignItemsStart,
          ...styles.justifyContentBetween,
        }}
      >
        <Box sx={styles.mainHeadings}>
          <Header title="FAQS" subtitle="Managing Frequently Asked Questions" />
        </Box>
        <Box>
          <Button
            sx={styles.buttonMd}
            onClick={() => navigate("/faqs/add")}
          >
            <AddOutlinedIcon sx={{ mr: "10px" }} />
            Add FAQ
          </Button>
        </Box>
      </Box>

      <Box
        sx={{
          ...styles.formContainer,
          mb: 2,
          display: "flex",
          gap: 2,
        }}
      >
        <TextField
          label="Search"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          sx={{ flex: 1 }}
        />

        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="category-select-label">Category</InputLabel>
          <Select
            labelId="category-select-label"
            value={selectedCategory}
            onChange={handleCategoryChange}
            label="Category"
          >
            <MenuItem value="">All Categories</MenuItem>
            {categoriesData.map((category) => (
              <MenuItem key={category} value={category}>
                {category}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <Box
        sx={{
          ...styles.formContainer,
        }}
      >
        <StyledDataGrid
          rows={faqsData?.items || []}
          columns={columns}
          getRowId={(row) => row._id}
          loading={isLoading}
          pagination
          page={page}
          pageSize={pageSize}
          rowCount={faqsData?.count || 0}
          paginationMode="server"
          onPageChange={(newPage) => setPage(newPage)}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          className="no-shaddow"
          noResultText="No FAQs found."
        />
      </Box>
    </Box>
  );
};

export default List;
