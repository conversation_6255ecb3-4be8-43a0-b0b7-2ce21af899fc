import { useSectionsQuery } from "api/master.api";
import ProductSectionTemplate from "components/templates/ProductSection.template";
import Banner from "../components/Banner";
import WooBanner from "../components/WooBanner";
import CustomLoadingOverlay from "components/molecules/CustomLoadingOverlay";
import { isMobileOnly } from "react-device-detect";
import MobileCategories from "components/MobileCategories";
import useUserStore, { setUserInfo} from "stores/user";
import { useEffect } from "react";
import { useGetProfileMutation } from "api/user.api";

const Home = () => {
  const { data: sectionsData, isLoading, isFetching } = useSectionsQuery();
  const userInfo = useUserStore((state) => state.userInfo);
  const { token, user } = userInfo || {};
  const { mutateAsync: getProfile } = useGetProfileMutation();

  const fetchUserProfile = async () => {
    try {
      const response = await getProfile(token);
      if (response?.success) {
        setUserInfo({
          ...userInfo,
          user: {
            ...user,
            ...response.data
          }
        });
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  useEffect(() => {
    if (token) {
      fetchUserProfile();
    }
  }, [token]);

  return (
    <>
      <CustomLoadingOverlay active={isLoading || isFetching} />
      <Banner />
      {isMobileOnly ? <MobileCategories /> : <WooBanner />}
      {/* <Taxonomy /> */}
      {/* <TrendingPid /> */}
      {sectionsData?.length > 0 &&
        sectionsData.map((section, idx) => {
          return <ProductSectionTemplate key={idx} section={section} />;
        })}
    </>
  );
};

export default Home;
