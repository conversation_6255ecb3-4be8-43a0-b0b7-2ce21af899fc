import * as Yup from 'yup';

// Signin validation schema
export const signinValidationSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

// Signup validation schema
export const signupValidationSchema = Yup.object().shape({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  password: Yup.string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Confirm Password is required'),
});

export const addressValidationSchema = Yup.object().shape({
  addressLine1: Yup.string()
    .required("Address is required")
    .max(100, "Address cannot exceed 100 characters"),
  city: Yup.string()
    .required("City is required")
    .matches(/^[A-Za-z]+$/, "City name must contain only letters")
    .max(50, "City name cannot exceed 50 characters"),
  state: Yup.string()
    .required("State is required")
    .matches(/^[A-Za-z]+$/, "State name must contain only letters")
    .max(50, "State name cannot exceed 50 characters"),
  postalCode: Yup.string()
    .required("Postal Code is required")
    .matches(/^[0-9]{5,10}$/, "Postal Code must be between 5 and 10 digits"),
  country: Yup.string()
    .required("Country is required")
    .matches(/^[A-Za-z]+$/, "Country name must contain only letters")
    .max(50, "Country name cannot exceed 50 characters"),
  phone: Yup.string()
    .required("Phone number is required")
    .matches(
      /^\+?[0-9]{10,15}$/,
      "Phone number must be between 10 and 15 digits and can optionally start with a country code"
    ),
  email: Yup.string()
    .required("Email is required")
    .email("Email is not valid")
    .max(100, "Email cannot exceed 100 characters"),
  fullName: Yup.string()
    .required("Full name is required")
    .matches(/^[A-Z a-z]+$/, "Full name must contain only letters")
    .max(50, "Full name cannot exceed 50 characters"),
  nickName: Yup.string()
    .required("Address Type is required")
    .max(50, "Address Type cannot exceed 50 characters"),
});

export const myProfileValidationSchema = Yup.object({
  fullName: Yup.string().required("Full name is required"),
  phone: Yup.string()
    .required("Phone is required")
    .matches(/^[0-9]/, "Phone contain only numbers")
    .max(10, "Phone must be at least 10 characters")
    .min(10, "Phone must be at least 10 characters"),
  upiId: Yup.string()
    .required()
    .matches(
      /^[a-zA-Z0-9.\-_]{2,256}@[a-zA-Z][a-zA-Z]{2,64}$/,
      "Please enter a valid UPI ID (e.g., username@upi)"
    ),

});



// Initial values
export const signinInitialValues = {
  email: '',
  password: '',
};

export const signupInitialValues = {
  email: '',
  password: '',
  confirmPassword: '',
};

export const addressInitialValues = {
  nickName: "",
  addressLine1: "",
  city: "",
  state: "",
  postalCode: "",
  country: "",
  phone: "",
  email: "",
  fullName: "",
};


