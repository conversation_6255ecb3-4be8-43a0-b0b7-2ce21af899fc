import { InputAdornment, TextField } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { useCouponsQuery, useValidateCouponMutation } from "api/coupon.api";
import { useState } from "react";
import { toast } from "react-toastify";

const CouponModal = ({ show, handleClose, handleSubmit, cartStoredData }) => {
  const { data: { items: couponsData = [] } = {} } = useCouponsQuery({
    params: { page: 1, limit: 50, status: "Active" },
  });

  const { mutateAsync: checkCoupon } = useValidateCouponMutation();

  const [selectedCoupon, setSelectedCoupon] = useState({});
  const [resultMsg, setResultMsg] = useState({
    type: "",
    msg: "",
  });

  const styles = {
    couponTile: {
      border: "1px solid #ddd",
      borderRadius: "8px",
      padding: "16px",
      marginBottom: "16px",
      boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)",
    },
  };

  const calculateCouponSaving = (couponItem) => {
    if(couponItem?.discountType === 'flat') {
      return couponItem?.discountValue
    }
    return 0;
  };

  const handleCheckCoupon = async () => {
    try {
      const productsInCart = cartStoredData?.products?.map((product) => {
        return {
          productId: product?.productId?._id,
          category:
            product?.productId?.categoryId ?? "665a8de4e247c9d5473738f7",
          quantity: product?.quantity,
        };
      });
      const payload = {
        couponCode: selectedCoupon?.code,
        cartTotal: cartStoredData?.totalPrice ?? 0,
        productsInCart,
      };
      const result = await checkCoupon(payload);
      if (result?.success) {
        setResultMsg({
          type: "success",
          msg: result?.data?.message,
        });
        setSelectedCoupon({
          ...selectedCoupon,
          savingAmount: result?.data?.discountAmount
        })
      }
    } catch (err) {
      setResultMsg({
        type: "error",
        msg: err?.response?.data?.message,
      });
      console.log(err);
    }
  };

  const onClose = () => {
    handleClose();
    setSelectedCoupon({});
    setResultMsg({ type: "", msg: "" });
  };

  console.log(selectedCoupon)
  return (
    <Dialog
      open={show}
      onClose={onClose}
      scroll="paper"
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <DialogTitle id="scroll-dialog-title">
        Apply Coupon
        <hr />
        <TextField
          fullWidth
          value={selectedCoupon?.code ?? ""}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <Button
                  sx={{
                    color: "#6C5EBC",
                  }}
                  onClick={handleCheckCoupon}
                  disabled={!selectedCoupon?.code}
                >
                  Check
                </Button>
              </InputAdornment>
            ),
          }}
        />
        {resultMsg?.msg && (
          <div
            style={{
              color: resultMsg?.type === "error" ? "red" : "green",
              fontSize: "14px",
            }}
          >
            {resultMsg?.msg}
          </div>
        )}
      </DialogTitle>
      <DialogContent dividers={scroll === "paper"} sx={{ minWidth: "40vw" }}>
        {couponsData?.length > 0 &&
          couponsData.map((couponItem, idx) => {
            return (
              <div
                key={idx}
                style={{
                  ...styles.couponTile,
                  border:
                    couponItem?.code === selectedCoupon?.code
                      ? "1px solid green"
                      : "",
                  cursor: "pointer",
                }}
                onClick={() => {
                  setSelectedCoupon(couponItem);
                  setResultMsg({ type: "", msg: "" });
                }}
              >
                <h6>{couponItem.code}</h6>
                <div>
                  <strong>Discount:</strong> {couponItem.discountValue}{" "}
                  {couponItem.discountType === "flat" ? "INR" : "%"}
                </div>
                <div>
                  <strong>Minimum Cart Value:</strong> {couponItem.minCartValue}{" "}
                  INR
                </div>
                <div>
                  <strong>Expiry Date:</strong>{" "}
                  {new Date(couponItem.expiry).toLocaleDateString()}
                </div>
                <span>
                  <strong>Description:</strong> {couponItem.description}
                </span>
              </div>
            );
          })}
      </DialogContent>
      <DialogActions
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mx: 3,
          color: "#6C5EBC",
        }}
      >
        <p>Maximum Savings: {selectedCoupon?.savingAmount ?? 0}</p>
        <div>
          <Button
            sx={{
              color: "#6C5EBC",
            }}
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button
            sx={{
              color: "#6C5EBC",
            }}
            onClick={() => {
              handleSubmit({
                ...selectedCoupon,
                calculateDiscount: selectedCoupon?.savingAmount
              });
              onClose();
            }}
          >
            Apply
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
};

export default CouponModal;
