import { API_ENDPOINTS } from "../constants/endpoints";
import { apiSlice, handleResponse } from "./base.service";

export const faqApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getFaqs: builder.query({
      query: ({ params }) => ({
        url: API_ENDPOINTS.GET_FAQS,
        method: "GET",
        params,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["faqs-list"],
    }),
    
    getFaqCategories: builder.query({
      query: () => ({
        url: API_ENDPOINTS.GET_FAQ_CATEGORIES,
        method: "GET",
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    
    getFaq: builder.query({
      query: (id) => ({
        url: `${API_ENDPOINTS.GET_FAQS}/${id}`,
        method: "GET",
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    
    createFaq: builder.mutation({
      query: (body) => ({
        url: API_ENDPOINTS.GET_FAQS,
        method: "POST",
        body,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: ["faqs-list"],
    }),
    
    updateFaq: builder.mutation({
      query: ({ id, body }) => ({
        url: `${API_ENDPOINTS.GET_FAQS}/${id}`,
        method: "PUT",
        body,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: ["faqs-list"],
    }),
    
    deleteFaq: builder.mutation({
      query: (id) => ({
        url: `${API_ENDPOINTS.GET_FAQS}/${id}`,
        method: "DELETE",
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: ["faqs-list"],
    }),
    
    toggleFaqStatus: builder.mutation({
      query: (id) => ({
        url: `${API_ENDPOINTS.GET_FAQS}/${id}/toggle-status`,
        method: "PUT",
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: ["faqs-list"],
    }),
  }),
});

export const {
  useGetFaqsQuery,
  useGetFaqCategoriesQuery,
  useGetFaqQuery,
  useCreateFaqMutation,
  useUpdateFaqMutation,
  useDeleteFaqMutation,
  useToggleFaqStatusMutation,
} = faqApiSlice;
