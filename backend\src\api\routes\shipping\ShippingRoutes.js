const express = require('express');
const router = express.Router();
const ShippingController = require('./ShippingController');
const validations = require('./ShippingValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin, verifyTokenAdmin } = require('../../util/auth');

const {getShiprocketToken} = require('../../util/shipping/shiprocket')

router.post('/shiprocket-create-shipment', getShiprocketToken, ShippingController.shiprocketCreateShipment);
router.post('/shiprocket-create-quick-order', getShiprocketToken, ShippingController.wrapperApiToCreateOrder);
router.post('/shiprocket-return-order', getShiprocketToken, ShippingController.wrapperApiToReturnOrder);
router.post('/shiprocket-generate-awb', getShiprocketToken, ShippingController.shiprocketGenerateAWB);
router.post('/shiprocket-generate-label', getShiprocketToken, ShippingController.shiprocketGenerateLabel);
router.post('/shiprocket-generate-manifest', getShiprocketToken, ShippingController.shiprocketGenerateManifest);
router.post('/shiprocket-generate-pickup', getShiprocketToken, ShippingController.shiprocketGeneratePickup);
router.get('/shiprocket-check-area-availability', ShippingController.checkAreaAvailability);
router.get('/shiprocket-get-tracking-by-awb', ShippingController.getTrackingByAWB);
router.post('/shiprocket-cancel-shipment', getShiprocketToken, ShippingController.shiprocketCancelShipment);


module.exports = router;
