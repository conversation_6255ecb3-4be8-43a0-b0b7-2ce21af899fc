const { Joi } = require('../../util/validations');

const addReview = Joi.object().keys({
    productId: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .required(),
    rating: Joi.number()
        .min(1)
        .max(5)
        .required(),
    comment: Joi.string()
        .min(3)
        .max(1000)
        .required(),
});

const updateReview = Joi.object().keys({
    productId: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .required(),
    reviewId: Joi.string()
        .regex(/^[0-9a-fA-F]{24}$/)
        .required(),
    rating: Joi.number()
        .min(1)
        .max(5)
        .required(),
    comment: Joi.string()
        .min(3)
        .max(1000)
        .required(),
});

module.exports = {
    addReview,
    updateReview,
};
