const express = require('express');
const router = express.Router();
const CategoryController = require('./CategoryController');
const validations = require('./CategoryValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin, verifyTokenAdmin } = require('../../util/auth');

router.get('/:catId', CategoryController.detail);
router.put('/:catId', verifyTokenAdmin, validate(validations.addCategory), CategoryController.update);
router.get('/', CategoryController.list);
router.post('/', verifyTokenAdmin, validate(validations.addCategory), CategoryController.add);

module.exports = router;
