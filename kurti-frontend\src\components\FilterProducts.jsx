import { Box, Grid, Input, Slider, Typography } from "@mui/material";
import { useGetSubCategoriesQuery } from "api/categories.api";
import {
  useBrandsQuery,
  useColorsQuery,
  useFabricQuery,
  useLengthQuery,
  useOccasionsQuery,
} from "api/master.api";
import { SETTINGS } from "globals";
import { debounce } from "lodash";
import { memo, useCallback } from "react";
import Accordion from "react-bootstrap/Accordion";
import { useSearchParams } from "react-router-dom";
import { getAllSearchParams } from "../utils";

const FilterProducts = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const params = getAllSearchParams(searchParams);
  const { data: subCategories = [] } = useGetSubCategoriesQuery(params?.catId);

  //   catId: searchParams.get('catId') ?? '',
  //     search: searchParams.get('search') ?? '',

  const { data: colorsData } = useColorsQuery();
  const { data: brandsData } = useBrandsQuery();
  const { data: occasionsData } = useOccasionsQuery();
  const { data: lengthData } = useLengthQuery();
  const { data: fabricData } = useFabricQuery();

  const debouncedHandleFilterChange = useCallback(
    debounce((value, type) => handleFilterChange(value, type), 300),
    []
  );

  const handleFilterChange = (value, type) => {
    if (type === "price") {
      setSearchParams({
        ...params,
        minPrice: value.minPrice,
        maxPrice: value.maxPrice,
      });
    } else {
      const currentValues = params?.[type] && params?.[type]?.split(",");
      let updatedValues = [];
      if (currentValues?.includes(value)) {
        updatedValues = currentValues.filter((item) => item !== value);
      } else {
        updatedValues = currentValues?.length
          ? [...currentValues, value]
          : [value];
      }
      setSearchParams({
        ...params,
        [type]: updatedValues.join(","),
      });
    }
  };

  const ClearFilter = ({ paramsObject, type }) => {
    return (
      <>
        {params?.[type] ? (
          <div className="gc-clear-filters">
            <button
              className="btn btn-primary"
              onClick={() => {
                setSearchParams(paramsObject);
              }}
            >
              Clear
            </button>
          </div>
        ) : null}
      </>
    );
  };

  const MasterAccordionItem = ({
    title,
    eventKey,
    data,
    queryKey,
    nameKey = "name",
    valueKey = "name",
  }) => {
    return (
      <Accordion.Item eventKey={eventKey}>
        <Accordion.Header>
          {title}
          <i class="fa-solid fa-plus"></i>
          <i class="fa-solid fa-minus"></i>
        </Accordion.Header>
        <Accordion.Body>
          <ul className="woocommerce-widget-layered-nav-list">
            {data?.length > 0 &&
              data?.map((item) => (
                <li
                  className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term"
                  key={item?.[valueKey]}
                >
                  <input
                    className="cursor-pointer"
                    type="checkbox"
                    id={item?.[valueKey]}
                    value={item?.[valueKey]}
                    checked={params?.[queryKey]?.includes(item?.[valueKey])}
                    onChange={() =>
                      handleFilterChange(item?.[valueKey], queryKey)
                    }
                  />
                  <label htmlFor={item?.[valueKey]}>
                    {item?.hexCode && (
                      <span
                        className="gc-swatches-widget-color-item"
                        style={{
                          backgroundColor: item?.hexCode,
                        }}
                      />
                    )}

                    {item?.[nameKey]}
                  </label>
                </li>
              ))}
          </ul>
          <ClearFilter
            type={queryKey}
            paramsObject={{ ...params, [queryKey]: "" }}
          />
        </Accordion.Body>
      </Accordion.Item>
    );
  };

  return (
    <Box>
      <Typography variant="h6" className="mb-2">
        Filter Products
      </Typography>
      <Accordion defaultActiveKey="0">
        <Accordion.Item eventKey="0">
          <Accordion.Header>
            By Price
            <i class="fa-solid fa-plus"></i>
            <i class="fa-solid fa-minus"></i>
          </Accordion.Header>
          <Accordion.Body>
            <Box
              sx={{
                width: 300,
              }}
            >
              <Slider
                value={[params.minPrice, params.maxPrice]}
                onChange={(event, value) =>
                  debouncedHandleFilterChange(
                    {
                      minPrice: value[0],
                      maxPrice: value[1],
                    },
                    "price"
                  )
                }
                valueLabelDisplay="auto"
                min={SETTINGS.MIN_PRICE_RANGE}
                max={SETTINGS.MAX_PRICE_RANGE}
                step={100}
              />
              {params?.minPrice || params?.maxPrice ? (
                <Grid
                  item
                  sx={{
                    display: "flex",
                    gap: "2rem",
                  }}
                >
                  <Input
                    value={params?.minPrice}
                    size="small"
                    onChange={(event) => {
                      debouncedHandleFilterChange(
                        {
                          minPrice: event.target.value,
                          maxPrice: params?.maxPrice,
                        },
                        "price"
                      );
                    }}
                    inputProps={{
                      step: 100,
                      min: SETTINGS.MIN_PRICE_RANGE,
                      max: SETTINGS.MAX_PRICE_RANGE,
                      type: "number",
                      "aria-labelledby": "input-slider",
                    }}
                    placeholder="Min"
                  />
                  <Input
                    value={params?.maxPrice}
                    size="small"
                    onChange={(event) => {
                      debouncedHandleFilterChange(
                        {
                          minPrice: params?.minPrice,
                          maxPrice: event.target.value,
                        },
                        "price"
                      );
                    }}
                    inputProps={{
                      step: 100,
                      min: SETTINGS.MIN_PRICE_RANGE,
                      max: SETTINGS.MAX_PRICE_RANGE,
                      type: "number",
                      "aria-labelledby": "input-slider",
                    }}
                    placeholder="Max"
                  />
                </Grid>
              ) : null}
            </Box>
          </Accordion.Body>
          <ClearFilter
            paramsObject={{ ...params, minPrice: null, maxPrice: null }}
            type="price"
          />
        </Accordion.Item>
        <MasterAccordionItem
          eventKey="1"
          title="By Color"
          data={colorsData}
          queryKey="color"
        />

        <MasterAccordionItem
          eventKey="2"
          title="By Brands"
          data={brandsData}
          queryKey="brand"
        />
        <MasterAccordionItem
          eventKey="3"
          title="By Occasions"
          data={occasionsData}
          queryKey="occasion"
        />
        <MasterAccordionItem
          eventKey="4"
          title="By Cloth Length"
          data={lengthData}
          queryKey="length"
        />
        <MasterAccordionItem
          eventKey="5"
          title="By Fabric"
          data={fabricData}
          queryKey="fabric"
        />
        {subCategories?.length > 0 && (
          <MasterAccordionItem
            eventKey="6"
            title="By Sub Category"
            data={subCategories}
            queryKey="subCatId"
            valueKey="_id"
          />
        )}
      </Accordion>
    </Box>
  );
};

export default memo(FilterProducts);
