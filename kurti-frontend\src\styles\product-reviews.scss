// Product Reviews Styles
.product-reviews-section {
  margin-top: 3rem;
  padding: 1.5rem;
  background-color: var(--gc-light);
  border-radius: 8px;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--gc-purple-dark);
  }

  .reviews-summary {
    background-color: var(--gc-white);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);

    .average-rating {
      h2 {
        font-size: 3rem;
        font-weight: 700;
        color: var(--gc-purple);
        margin-bottom: 0.5rem;
      }

      .text-muted {
        margin-top: 0.5rem;
        font-size: 0.9rem;
      }
    }

    .write-review-container {
      h5 {
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--gc-text);
      }
    }
  }

  .no-reviews {
    background-color: var(--gc-white);
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    text-align: center;

    p {
      margin-bottom: 1.5rem;
      color: var(--gc-text-soft);
    }

    a {
      color: var(--gc-purple);
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .reviews-list {
    margin-top: 1.5rem;

    .review-card {
      border: 1px solid var(--gc-border);
      border-radius: 8px;
      overflow: hidden;

      .reviewer-name {
        font-weight: 600;
        font-size: 1rem;
        margin-bottom: 0.5rem;
        color: var(--gc-text);
      }

      .review-meta {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;

        .review-date {
          margin-left: 1rem;
          font-size: 0.85rem;
          color: var(--gc-text-soft);
        }
      }

      .review-actions {
        button {
          svg {
            width: 18px;
            height: 18px;
          }
        }
      }

      .review-comment {
        color: var(--gc-text-soft);
        font-size: 0.95rem;
        line-height: 1.6;
      }
    }
  }

  .load-more-btn {
    padding: 0.5rem 1.5rem;
    border-color: var(--gc-purple);
    color: var(--gc-purple);

    &:hover {
      background-color: var(--gc-purple);
      color: var(--gc-white);
    }
  }

  .submit-review-btn {
    background-color: var(--gc-purple);
    border-color: var(--gc-purple);

    &:hover {
      background-color: var(--gc-purple-dark);
      border-color: var(--gc-purple-dark);
    }
  }
}

// Star Rating Component
.star-rating {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;

  .star {
    color: #ffc107;
    margin-right: 0.25rem;
    cursor: pointer;

    &.readonly {
      cursor: default;
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

// Loading Skeleton
.loading-skeleton {
  .skeleton-line {
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 10px;
    animation: pulse 1.5s infinite;

    &:nth-child(2) {
      width: 90%;
    }

    &:nth-child(3) {
      width: 80%;
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }
}

// Review Form
.review-form {
  textarea {
    resize: vertical;
    min-height: 100px;
    border-color: var(--gc-border);

    &:focus {
      border-color: var(--gc-purple);
      box-shadow: 0 0 0 0.25rem rgba(108, 94, 188, 0.25);
    }
  }
}
