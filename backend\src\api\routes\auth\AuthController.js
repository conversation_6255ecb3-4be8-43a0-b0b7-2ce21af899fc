const {
    models: { Otp, User },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');
const { signToken } = require('../../util/auth');
const { getPlatform } = require('../../util/common');
const axios = require('axios');

const MSG91_AUTH_KEY = '439586AGv8DmwMdzSO67b8880aP1';
const TEMPLATE_ID = '67b86e08d6fc05732e6296d3';
const OTP_EXPIRY = 15;

class AuthController {
    async sendOtp(req, res) {
        const { countryCode, phone } = req.body;

        if (!countryCode || !phone) {
            return res.warn('', req.__('MOBILE_NUMBER_REQUIRED'));
        }

        try {
            const options = {
                method: 'POST',
                url: 'https://control.msg91.com/api/v5/otp',
                params: {
                    otp_expiry: OTP_EXPIRY,
                    template_id: TEMPLATE_ID,
                    mobile: `${countryCode}${phone}`,
                    authkey: MSG91_AUTH_KEY,
                    realTimeResponse: 1,
                },
                headers: { 'Content-Type': 'application/json' },
            };

            const { data } = await axios.request(options);
            return res.success('', data);
        } catch (error) {
            console.error(error);
            return res.warn('', req.__('FAILED_TO_SEND_OTP'));
        }
    }

    async validateOtp(req, res) {
        const { countryCode, phone, otp } = req.body;

        if (!countryCode || !phone || !otp) {
            return res.warn('', req.__('OTP_VALIDATION_FAILED'));
        }

        try {
            const options = {
                method: 'GET',
                url: 'https://control.msg91.com/api/v5/otp/verify',
                params: {
                    otp,
                    mobile: `${countryCode}${phone}`,
                },
                headers: { authkey: MSG91_AUTH_KEY },
            };

            const { data } = await axios.request(options);
            return res.success('', data);
        } catch (error) {
            console.error(error);
            return res.warn('', req.__('INVALID_OTP'));
        }
    }

    async resendOtp(req, res) {
        const { countryCode, phone, retryType } = req.body;

        if (!countryCode || !phone || !retryType) {
            return res.warn('', req.__('OTP_RESEND_FAILED'));
        }

        try {
            const options = {
                method: 'GET',
                url: 'https://control.msg91.com/api/v5/otp/retry',
                params: {
                    authkey: MSG91_AUTH_KEY,
                    retrytype: retryType,
                    mobile: `${countryCode}${phone}`,
                },
            };

            const { data } = await axios.request(options);
            return res.success('', data);
        } catch (error) {
            console.error(error);
            return res.warn('', req.__('FAILED_TO_RESEND_OTP'));
        }
    }

    async requestOtp(req, res) {
        const { type, countryCode, phone } = req.body;

        if (['SIGN_UP', 'CHANGE_PHONE'].indexOf(type) !== -1) {
            const user = await User.findOne({
                countryCode,
                phone,
                isDeleted: false,
            });

            // if (user) {
            //     return res.warn('', req.__('PHONE_ALREADY_FOUND'));
            // }
        }

        if (type === 'FORGOT_PASSWORD') {
            const user = await User.findOne({
                countryCode,
                phone,
                isDeleted: false,
            });

            if (!user) {
                return res.warn('', req.__('USER_NOT_FOUND'));
            }

            if (user.isSuspended) {
                return res.warn('', req.__('YOUR_ACCOUNT_SUSPENDED'));
            }
        }

        let token = Math.floor(100000 + Math.random() * 900000).toString();

        const otp =
            (await Otp.findOne({
                countryCode,
                phone,
            })) || new Otp({});

        otp.type = type;
        otp.countryCode = countryCode;
        otp.phone = phone;
        otp.token = token;
        otp.validTill = utcDateTime(utcDateTime().valueOf() + parseInt(process.env.otpValidMinutes || 5) * 60000);
        otp.isVerified = false;
        await otp.save();

        // if (process.env.NODE_ENV === 'production') {
        try {
            const options = {
                method: 'POST',
                url: 'https://control.msg91.com/api/v5/otp',
                params: {
                    otp_expiry: '15',
                    template_id: '67b86e08d6fc05732e6296d3',
                    mobile: `${countryCode}${phone}`,
                    authkey: '439586AGv8DmwMdzSO67b8880aP1',
                    realTimeResponse: '1',
                },
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            const { data } = await axios.request(options);
            if (!data.type === 'success') {
                return res.warn('', req.__('FAILED_TO_SEND_OTP'));
            }
        } catch (error) {
            console.error('MSG91 OTP Error:', error);
            return res.warn('', req.__('FAILED_TO_SEND_OTP'));
        }
        // }

        await otp.save();
        return res.success('', req.__('OTP_SENT'));
    }

    async verifyOtp(req, res) {
        const { type, countryCode, phone, token } = req.body;

        if (['SIGN_UP', 'CHANGE_PHONE'].indexOf(type) !== -1) {
            const user = await User.findOne({
                countryCode,
                phone,
                isDeleted: false,
            });

            // if (user) {
            //     return res.warn('', req.__('PHONE_ALREADY_FOUND'));
            // }
        }

        if (type === 'FORGOT_PASSWORD') {
            const user = await User.findOne({
                countryCode,
                phone,
                isDeleted: false,
            });

            if (!user) {
                return res.warn('', req.__('USER_NOT_FOUND'));
            }

            if (user.isSuspended) {
                return res.warn('', req.__('YOUR_ACCOUNT_SUSPENDED'));
            }
        }

        const otp = await Otp.findOne({
            type,
            countryCode,
            phone,
            validTill: {
                $gte: utcDateTime(),
            },
            isVerified: false,
        });
        console.log(otp);

        if (!otp || otp.token !== token) {
            return res.warn('', req.__('INVALID_OTP'));
        }

        otp.isVerified = true;
        await otp.save();

        return res.success('', req.__('OTP_VERIFIED'));
    }

    async signUp(req, res) {
        const { fullName, age, email, countryCode, phone, password, deviceToken } = req.body;

        const otp = await Otp.findOne({
            type: 'SIGN_UP',
            countryCode,
            phone,
            validTill: {
                $gte: utcDateTime(),
            },
            isVerified: true,
        });

        if (!otp) {
            return res.warn('', req.__('PHONE_NOT_VERIFIED'));
        }

        const userMatchCond = {
            $or: [{ $and: [{ countryCode }, { phone }] }],
            isDeleted: false,
        };
        email && userMatchCond.$or.push({ email });

        let user = await User.findOne(userMatchCond);

        if (user) {
            return res.warn('', req.__('USER_ALREADY_FOUND'));
        }

        const userData = {
            fullName,
            age,
            countryCode,
            phone,
            password,
        };
        email && (userData.email = email);
        deviceToken && (userData.deviceToken = deviceToken);
        user = new User(userData);
        user.authTokenIssuedAt = utcDateTime().valueOf();
        await user.save();

        otp.validTill = null;
        await otp.save();

        const platform = getPlatform(req);
        const token = signToken(user, platform);
        const userJson = user.toJSON();
        ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
            key => delete userJson[key]
        );

        return res.success(
            {
                token,
                user: userJson,
            },
            req.__('LOGIN_SUCCESS')
        );
    }

    // async fcmSignUp(req, res) {
    //     const { email, fcmId, deviceToken, isSocial, phone, countryCode } = req.body;

    //     const userMatchCond = {
    //         isDeleted: false,
    //     };
    //     if (email) {
    //         userMatchCond.email = email;
    //     } else {
    //         userMatchCond.$or = [{ $and: [{ countryCode }, { phone }] }];
    //     }

    //     // Find a user by email and ensure they are not deleted
    //     // let user = await User.findOne({ email, isDeleted: false });
    //     let user = await User.findOne(userMatchCond);
    //     console.log(user)
    //     if (user) {
    //         // Check if the user is a social user
    //         if (isSocial == true) {
    //             // Update the fcmId and deviceToken for the existing social user
    //             user.fcmId = fcmId;
    //             user.phone = phone;
    //             user.deviceToken = deviceToken;
    //             await user.save();

    //             // Return success response for updated user
    //             const userJson = user.toJSON();
    //             ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
    //                 key => delete userJson[key]
    //             );

    //             return res.success(
    //                 {
    //                     user: userJson,
    //                 },
    //                 req.__('UPDATE_SUCCESS') // You might want to change the message to reflect update success
    //             );
    //         }
    //         // else {
    //         //     // Return warning if user already exists but is not social

    //         // }
    //     }
    //     // else {

    //     // }

    //     // If the user does not exist, create a new user
    //     const userData = {
    //         email,
    //         fcmId,
    //         phone,
    //         deviceToken,
    //         countryCode,
    //     };
    //     email && (userData.email = email);
    //     phone && (userData.phone = phone);
    //     deviceToken && (userData.deviceToken = deviceToken);
    //     user = new User(userData);
    //     user.authTokenIssuedAt = utcDateTime().valueOf();
    //     await user.save();
    //     const platform = getPlatform(req);
    //     const token = signToken(user, platform);
    //     const userJson = user.toJSON();
    //     ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
    //         key => delete userJson[key]
    //     );

    //     return res.success(
    //         {
    //             token,
    //             user: userJson,
    //         },
    //         req.__('LOGIN_SUCCESS')
    //     );
    // }

    async fcmSignUp(req, res) {
        try {
            const { email, fcmId, deviceToken, isSocial, phone, countryCode } = req.body;

            if (!phone && !email) {
                return res.error(req.__('PHONE_OR_EMAIL_REQUIRED'));
            }

            let userMatchCond = { isDeleted: false };
            if (email) {
                userMatchCond.email = email;
            } else {
                userMatchCond.countryCode = countryCode;
                userMatchCond.phone = phone;
            }

            let user = await User.findOne(userMatchCond);

            if (user) {
                if (!isSocial) {
                    // If the user is found and not a social user, login with the same user
                    const token = signToken(user, getPlatform(req));
                    return res.success({ token, user: sanitizeUser(user) }, req.__('LOGIN_SUCCESS'));
                } else {
                    // If the user is social, update their details and login
                    user.fcmId = fcmId;
                    user.deviceToken = deviceToken;
                    await user.save();
                    return res.success({ user: sanitizeUser(user) }, req.__('UPDATE_SUCCESS'));
                }
            }

            // If user is not found, create a new one
            const newUser = new User({
                email,
                phone,
                countryCode,
                fcmId,
                deviceToken,
                authTokenIssuedAt: utcDateTime().valueOf(),
            });
            await newUser.save();

            const token = signToken(newUser, getPlatform(req));
            return res.success({ token, user: sanitizeUser(newUser) }, req.__('LOGIN_SUCCESS'));
        } catch (error) {
            console.error('Error in fcmSignUp:', error);
            return res.error(req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async logIn(req, res) {
        const { countryCode, phone, password, deviceToken } = req.body;
        let user = await User.findOne({
            countryCode,
            phone,
            isDeleted: false,
        });

        if (!user) {
            return res.warn('', req.__('USER_NOT_FOUND'));
        }

        if (user.isSuspended) {
            return res.warn('', req.__('YOUR_ACCOUNT_SUSPENDED'));
        }

        if (user.failedLoginAttempts >= parseInt(process.env.allowedFailedLoginAttempts || 3)) {
            let difference = user.preventLoginTill - utcDateTime().valueOf();
            if (difference > 0) {
                let differenceInSec = Math.abs(difference) / 1000;
                differenceInSec -= Math.floor(differenceInSec / 86400) * 86400;
                differenceInSec -= (Math.floor(differenceInSec / 3600) % 24) * 3600;
                const minutes = Math.floor(differenceInSec / 60) % 60;
                differenceInSec -= minutes * 60;
                const seconds = differenceInSec % 60;
                return res.warn(
                    '',
                    req.__(
                        'LOGIN_DISABLED',
                        `${minutes ? `${minutes} ${req.__('KEY_MINUTES')} ` : ''}${seconds} ${req.__('KEY_SECONDS')}`
                    )
                );
            }
        }

        const passwordMatched = await user.comparePassword(password);
        if (!passwordMatched) {
            user.failedLoginAttempts = user.failedLoginAttempts + 1;
            user.preventLoginTill = utcDateTime(
                utcDateTime().valueOf() + parseInt(process.env.preventLoginOnFailedAttemptsTill || 5) * 60000
            ).valueOf();
            await user.save();
            const chanceLeft = parseInt(process.env.allowedFailedLoginAttempts || 3) - user.failedLoginAttempts;
            return res.warn(
                '',
                req.__(
                    'INVALID_CREDENTIALS_LIMIT',
                    `${
                        chanceLeft <= 0
                            ? `${req.__('KEY_LOGIN_DISABLED')}`
                            : `${req.__('KEY_YOU_HAVE_ONLY')} ${chanceLeft} ${req.__('KEY_CHANCE_LEFT')}`
                    }`
                )
            );
        }

        deviceToken && (user.deviceToken = deviceToken);
        user.authTokenIssuedAt = utcDateTime().valueOf();
        user.failedLoginAttempts = 0;
        user.preventLoginTill = 0;
        await user.save();

        const platform = getPlatform(req);
        const token = signToken(user, platform);
        const userJson = user.toJSON();
        ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
            key => delete userJson[key]
        );

        return res.success(
            {
                token,
                user: userJson,
            },
            req.__('LOGIN_SUCCESS')
        );
    }

    async logout(req, res) {
        const { user } = req;
        user.authTokenIssuedAt = null;
        user.deviceToken = null;
        await user.save();
        return res.success('', req.__('LOGOUT_SUCCESS'));
    }

    async resetPassword(req, res) {
        const { countryCode, phone, password } = req.body;
        const otp = await Otp.findOne({
            type: 'FORGOT_PASSWORD',
            countryCode,
            phone,
            validTill: {
                $gte: utcDateTime(),
            },
            isVerified: true,
        });

        if (!otp) {
            return res.warn('', req.__('PHONE_NOT_VERIFIED'));
        }

        let user = await User.findOne({
            countryCode,
            phone,
            isDeleted: false,
        });

        if (!user) {
            return res.warn('', req.__('USER_NOT_FOUND'));
        }

        if (user.isSuspended) {
            return res.warn('', req.__('YOUR_ACCOUNT_SUSPENDED'));
        }

        user.password = password;
        await user.save();

        otp.validTill = null;
        await otp.save();

        return res.success('', req.__('PASSWORD_CHANGED'));
    }
}

function sanitizeUser(user) {
    const userJson = user.toJSON();
    ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
        key => delete userJson[key]
    );
    return userJson;
}

module.exports = new AuthController();
