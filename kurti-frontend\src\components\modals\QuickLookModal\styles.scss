.quick-look-modal {
  .modal-dialog {
    max-width: 900px;
  }

  .modal-content {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    border-bottom: 1px solid #eee;
    padding: 15px 20px;

    .modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .btn-close {
      font-size: 12px;
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-body {
    padding: 25px;
  }

  .product-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 20px;

    img {
      max-height: 400px;
      object-fit: contain;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .product-details {
    padding: 0 15px;

    .product-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #222;
      line-height: 1.3;
    }

    .product-price {
      margin-bottom: 15px;
      display: flex;
      align-items: center;

      .original-price {
        text-decoration: line-through;
        color: #999;
        margin-right: 10px;
        font-size: 16px;
      }

      .selling-price {
        font-size: 22px;
        font-weight: 600;
        color: #6c5ebc;
      }
    }

    .product-rating {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .MuiRating-root {
        color: #ffc107;
        margin-right: 5px;
      }

      .review-count {
        color: #666;
        font-size: 14px;
      }
    }

    .product-description {
      margin-bottom: 20px;
      font-size: 14px;
      color: #666;
      line-height: 1.6;
    }

    .product-variations {
      margin-bottom: 25px;

      .variation-title {
        display: block;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
      }

      .color-selection {
        margin-bottom: 20px;

        .color-options {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;

          .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            border: 1px solid #ddd;
            transition: all 0.2s ease;

            &.selected {
              box-shadow: 0 0 0 2px #fff, 0 0 0 4px #6c5ebc;
            }

            &:hover {
              transform: scale(1.1);
            }
          }
        }
      }

      .size-selection {
        margin-bottom: 20px;

        .size-options {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;

          .size-option {
            min-width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            padding: 0 12px;

            &.selected {
              background-color: #333;
              color: #fff;
              border-color: #333;
            }

            &:hover:not(.selected) {
              border-color: #999;
            }
          }
        }
      }
    }

    .product-actions {
      margin-bottom: 20px;

      .quantity-actions {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .quantity-selector {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 4px;

        .quantity-button {
          width: 40px;
          height: 45px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          user-select: none;
          background: none;
          border: none;
          font-size: 18px;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &:hover:not(:disabled) {
            background-color: #f5f5f5;
          }
        }

        .quantity-input {
          width: 50px;
          height: 45px;
          border: none;
          border-left: 1px solid #ddd;
          border-right: 1px solid #ddd;
          text-align: center;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .add-to-cart-btn {
        height: 45px;
        padding: 0 25px;
        background-color: #6c5ebc;
        color: white;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        font-size: 15px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: darken(#6c5ebc, 10%);
        }

        &:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
      }

      .wishlist-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          border-color: #6c5ebc;
          background-color: rgba(255, 71, 71, 0.05);
        }

        svg {
          width: 22px;
          height: 22px;
          color: #6c5ebc;
        }
      }
    }

    .view-details {
      margin-top: 25px;
      text-align: center;

      .view-details-link {
        color: #333;
        text-decoration: none;
        font-weight: 500;
        font-size: 15px;
        padding-bottom: 2px;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;

        &:hover {
          color: #6c5ebc;
          border-bottom-color: #6c5ebc;
        }
      }
    }
  }

  @media (max-width: 767px) {
    .modal-dialog {
      margin: 10px;
    }

    .modal-body {
      padding: 15px;
    }

    .product-image {
      margin-bottom: 20px;
      padding: 10px;

      img {
        max-height: 300px;
      }
    }

    .product-details {
      padding: 0;

      .product-title {
        font-size: 20px;
      }

      .product-price {
        .selling-price {
          font-size: 20px;
        }
      }
    }

    .product-actions {
      .quantity-actions {
        flex-direction: column;
        align-items: stretch;

        .quantity-selector {
          margin-bottom: 10px;
        }

        .add-to-cart-btn {
          margin-bottom: 10px;
        }

        .wishlist-btn {
          align-self: flex-start;
        }
      }
    }
  }
}
