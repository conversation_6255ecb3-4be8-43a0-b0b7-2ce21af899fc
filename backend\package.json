{"name": "GAJAN CREATION", "version": "0.0.1", "description": "A Kurti Store", "private": true, "repository": "https://gitlab.com/RonakSethi/ecommerce-store.git", "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://gajancreation.com", "bugs": {"url": "https://gitlab.com/RonakSethi/ecommerce-store/issues"}, "keywords": ["kurti", "store", "ecommerce", "gajan", "creation"], "readme": "https://gitlab.com/RonakSethi/ecommerce-store/-/blob/main/README.md", "readmeFilename": "README.md", "engines": {"node": "18", "yarn": ">= 1.12.3"}, "main": "src/api/index.js", "scripts": {"start:api": "nodemon src/api/index.js", "deploy": "firebase deploy --only functions"}, "lint-staged": {"src/**/*.{js,json}": ["eslint --fix", "prettier --write", "git add"]}, "dependencies": {"@sendgrid/mail": "^6.5.0", "aws-sdk": "^2.618.0", "axios": "^0.21.4", "bcrypt": "^4.0.1", "body-parser": "^1.19.0", "compare-versions": "^3.6.0", "compression": "^1.7.4", "connect-flash": "^0.1.1", "connect-mongo": "^3.0.0", "cookie-parser": "^1.4.4", "cors": "^2.8.5", "custom-env": "^1.0.2", "dotenv": "^8.1.0", "ejs-locals": "^1.0.2", "email-templates": "^7.0.2", "express": "~4.16.1", "express-async-errors": "^3.1.1", "express-fileupload": "^1.1.7-alpha.3", "express-mung": "^0.5.1", "express-session": "^1.17.0", "fcm-node": "^1.5.2", "firebase-admin": "^12.2.0", "firebase-functions": "^5.0.1", "joi": "^14.3.1", "joi-i18n": "^13.1.3", "jsonwebtoken": "^8.5.1", "moment": "^2.24.0", "mongoose": "^5.11.7", "nodemon": "^3.1.0", "pdfkit": "^0.17.1", "puppeteer": "^24.8.2", "qrcode-terminal": "^0.12.0", "randomstring": "^1.1.5", "razorpay": "^2.9.4", "redis": "^4.7.0", "saslprep": "^1.0.3", "short-unique-id": "^5.2.0", "shortid": "^2.2.16", "slugify": "^1.6.6", "socket.io": "2.2.0", "stripe": "^8.68.0", "swagger-ui-express": "^4.1.4", "uuid": "^8.3.2", "whatsapp-web.js": "^1.28.0", "yamljs": "^0.3.0"}, "devDependencies": {"@types/node": "^12.7.5", "eslint": "^5.16.0", "lint-staged": "^9.3.0", "morgan": "^1.9.1", "prettier": "^1.18.2"}}