import { useState } from "react";
import { Link } from "react-router-dom";
import useUserStore from "stores/user";
import { IconLogin, IconLogout } from "utils/icons";
import userImg from "../assets/images/14.jpg";
import logo from "../assets/images/logo.png";
import CartIconWithCount from "./atoms/CartIconWithCount.atom";
import WishlistIconWithCount from "./atoms/WishlistIconWithCount.atom";
import defaultAvatar from "../assets/images/14.jpg";

const MobileHeader = ({ handleLogout }) => {
  const [activeLeftMenu, setActiveLeftMenu] = useState("");

  const { token, user } = useUserStore((state) => state.userInfo);

  const handleLeftMenu = () => {
    setActiveLeftMenu(!activeLeftMenu);
  };

  return (
    <>
      <div className="gc-header-mobile-top gc-container-xl mobile-header-bg-type-default">
        <div className="mobile-toggle" onClick={handleLeftMenu}>
          <svg
            className="svgBars gc-svg-icon"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="0" y1="12" x2="21" y2="12"></line>
            <line x1="0" y1="6" x2="21" y2="6"></line>
            <line x1="0" y1="18" x2="21" y2="18"></line>
          </svg>
        </div>
        <div className="gc-header-mobile-logo">
          <div className="nav-logo logo-type-img">
            <Link
              to="/"
              aria-label="logo image"
              className="nt-logo header-logo logo-type-img"
            >
              <img
                className="main-logo entered lazyloaded"
                src={logo}
                alt="GC"
              />
            </Link>
          </div>
        </div>
        <div className="gc-header-mobile-top-actions">
          <div className="top-action-btn">
            <CartIconWithCount onClick={handleLeftMenu}/>
            {/* <Link to="/cart">
              <div className="cart-with-counter">
                <span className="gc-cart-count gc-wc-count">{cartCount}</span>
                <svg
                  className="shopBag gc-svg-icon"
                  width="512"
                  height="512"
                  fill="currentColor"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m26 8.9a1 1 0 0 0 -1-.9h-3a6 6 0 0 0 -12 0h-3a1 1 0 0 0 -1 .9l-1.78 17.8a3 3 0 0 0 .78 2.3 3 3 0 0 0 2.22 1h17.57a3 3 0 0 0 2.21-1 3 3 0 0 0 .77-2.31zm-10-4.9a4 4 0 0 1 4 4h-8a4 4 0 0 1 4-4zm9.53 23.67a1 1 0 0 1 -.74.33h-17.58a1 1 0 0 1 -.74-.33 1 1 0 0 1 -.26-.77l1.7-16.9h2.09v3a1 1 0 0 0 2 0v-3h8v3a1 1 0 0 0 2 0v-3h2.09l1.7 16.9a1 1 0 0 1 -.26.77z"></path>
                </svg>
              </div>
            </Link> */}
            {/* {token && (
              <Link to="/dashboard">
                <img
                  src={userImg}
                  alt=""
                  className="avatar avatar-sm avatar-rounded"
                />
              </Link>
            )} */}
          </div>
        </div>
      </div>

      <nav
        className={`gc-header-mobile has-bar sidebar-header-bg-type-default has-buttons has-socials has-logo ${
          activeLeftMenu ? "active" : ""
        }`}
      >
        <div className="gc-panel-close no-bar"></div>
        <div className="gc-header-mobile-sidebar">
          <div
            className="gc-panel-close gc-panel-close-button"
            onClick={handleLeftMenu}
          ></div>
          <div className="gc-header-mobile-sidebar-inner">
            <div className="sidebar-top-action justify-content-end gap-3">
              {/* <CartIconWithCount onClick={handleLeftMenu} activeLeftMenu={activeLeftMenu} /> */}
              <WishlistIconWithCount onClick={handleLeftMenu}/>
              {token ? (
                <>
                  {/* <div
                        className="top-action-btn header-top-account"
                        data-account-action="account"
                      >
                        <Link className="account-page-link" to="/my-orders">
                          <IconUserProfile />
                        </Link>
                      </div> */}
                  <div className="top-action-btn header-top-account">
                    <Link className="account-page-link" to="/dashboard" onClick={() => handleLeftMenu()}>
                      <img
                        src={user?.avatar || defaultAvatar}
                        alt="Profile"      
                        className="avatar avatar-sm avatar-rounded"
                      />
                    </Link>
                  </div>

                  <div
                    className="top-action-btn cursor-pointer"
                    data-name="cart"
                    onClick={handleLogout}
                  >
                    <IconLogout className={"gc-svg-icon"} />
                  </div>
                </>
              ) : (
                <Link
                  to="/sign-in"
                  className="top-action-btn"
                  data-name="cart"
                  title="login"
                  onClick={() => handleLeftMenu()}
                >
                  <IconLogin className={"gc-svg-icon"} />
                </Link>
              )}
              {/* <div className="top-action-btn">
                <span className="gc-compare-count gc-wc-count">0</span>
                <svg
                  className="svgCompare gc-svg-icon"
                  width="512"
                  height="512"
                  fill="currentColor"
                  viewBox="0 0 30 30"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m26 9a1 1 0 0 0 0-2h-4a1 1 0 0 0 -1 1v4a1 1 0 0 0 2 0v-1.66a9 9 0 0 1 -7 14.66c-.3 0-.6 0-.9 0a1 1 0 1 0 -.2 2c.36 0 .73.05 1.1.05a11 11 0 0 0 8.48-18.05z"></path>
                  <path d="m10 19a1 1 0 0 0 -1 1v1.66a9 9 0 0 1 8.8-14.48 1 1 0 0 0 .4-2 10.8 10.8 0 0 0 -2.2-.18 11 11 0 0 0 -8.48 18h-1.52a1 1 0 0 0 0 2h4a1 1 0 0 0 1-1v-4a1 1 0 0 0 -1-1z"></path>
                </svg>
              </div> */}
              {/* <div className="top-action-btn">
                <span className="gc-category-count gc-wc-count">12</span>
                <IconPaperSearchMagnifyGlass className="svgPaperSearch gc-svg-icon" />
              </div> */}
            </div>
          </div>
        </div>
        <div className="gc-header-mobile-content">
          <div className="gc-header-slide-menu menu-area active">
            <div
              className="gc-header-mobile-slide-menu sliding-menu"
              style={{ height: "168px" }}
            >
              <ul className="sliding-menu__panel sliding-menu__panel-root">
                <li className="sliding-menu-inner">
                  <ul>
                    <li className="menu-item menu-item-type-custom menu-item-object-custom menu-item-home menu-item-has-children has-dropdown menu-item-2602">
                      <Link to="/" className="sliding-menu__nav" type="button" onClick={() => handleLeftMenu()}>
                        Home
                      </Link>
                    </li>
                    <li className="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children has-dropdown menu-item-5935">
                      <Link
                        to="/about"
                        className="sliding-menu__nav"
                        type="button"
                        onClick={() => handleLeftMenu()}
                      >
                        About
                      </Link>
                    </li>
                    <li className="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children has-dropdown menu-item-5935">
                      <Link
                        to="/category"
                        className="sliding-menu__nav"
                        type="button"
                        onClick={() => handleLeftMenu()}
                      >
                        Shop
                      </Link>
                    </li>
                    <li className="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children has-dropdown menu-item-1191">
                      <Link
                        to="/contact"
                        className="sliding-menu__nav"
                        type="button"
                        onClick={() => handleLeftMenu()}
                      >
                        Contact Us
                      </Link>
                    </li>
                  </ul>
                </li>
              </ul>
            </div>
            {/* <div className="gc-sidemenu-copyright">
              <p>
                © 2024,{" "}
                <a className="theme" href="https://none.com/themes/anarkali/">
                  Anarkali
                </a>{" "}
                Theme. Made with passion by{" "}
                <a className="dev" href="https://none.com/contact/">
                  Ninetheme.
                </a>
              </p>
            </div>{" "} */}
          </div>

          <div
            className="category-area action-content"
            data-target-name="search-cats"
          >
            <span className="panel-top-title">All Products Categories</span>
            <div className="category-area-inner gc-perfect-scrollbar">
              <div className="row row-cols-3">
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/men/ethnic-wear/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Ethnic Wear</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/women/festive-wear/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Festive Wear</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/kids/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Kids</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/kids/kurta-pant-set/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Kurta Pant Set</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/women/lehangas/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Lehangas</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/kids/lehenga-set/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Lehenga Set</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/men/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Men</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/men/men-t-shirts/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Men t-shirts</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/kids/sharara-set/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Sharara Set</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/men/sherwanis/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Sherwanis</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/women/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Women</span>
                    </a>
                  </div>
                </div>
                <div className="col">
                  <div className="product-category">
                    <a href="https://none.com/themes/anarkali/product-category/women/women-t-shirts/">
                      <img
                        width="540"
                        height="720"
                        src="data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20540%20720'%3E%3C/svg%3E"
                        className="attachment-gc-panel size-gc-panel"
                        alt=""
                      />

                      <span className="category-title">Women t-shirts</span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="cart-area action-content" data-target-name="cart">
            <span className="panel-top-title">Your Cart</span>
            <div className="minicart-panel">
              <div className="gc-header-cart-details gc-minicart row row-cols-1">
                <div className="cart-empty-content">
                  <svg
                    className="shopBag"
                    width="512"
                    height="512"
                    fill="currentColor"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="m26 8.9a1 1 0 0 0 -1-.9h-3a6 6 0 0 0 -12 0h-3a1 1 0 0 0 -1 .9l-1.78 17.8a3 3 0 0 0 .78 2.3 3 3 0 0 0 2.22 1h17.57a3 3 0 0 0 2.21-1 3 3 0 0 0 .77-2.31zm-10-4.9a4 4 0 0 1 4 4h-8a4 4 0 0 1 4-4zm9.53 23.67a1 1 0 0 1 -.74.33h-17.58a1 1 0 0 1 -.74-.33 1 1 0 0 1 -.26-.77l1.7-16.9h2.09v3a1 1 0 0 0 2 0v-3h8v3a1 1 0 0 0 2 0v-3h2.09l1.7 16.9a1 1 0 0 1 -.26.77z"></path>
                  </svg>{" "}
                  <span className="minicart-title">Your Cart</span>
                  <p className="empty-title gc-small-title">
                    No products in the cart.
                  </p>
                  <div className="cart-empty-actions cart-bottom-btn">
                    <a
                      className="gc-btn-medium gc-btn gc-btn-black"
                      href="https://none.com/themes/anarkali/shop/"
                    >
                      Start Shopping
                    </a>
                    <a className="gc-btn-medium gc-btn gc-btn-black" href="">
                      Return Policy
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            className="contact-area action-content"
            data-target-name="contact"
          >
            <span className="panel-top-title">Contact Us</span>

            <div className="wpcf7 js" id="wpcf7-f135-o1" lang="en-US" dir="ltr">
              <div className="screen-reader-response">
                <p role="status" aria-live="polite" aria-atomic="true"></p>{" "}
                <ul></ul>
              </div>
              <form
                action="/themes/anarkali/product/amzira/#wpcf7-f135-o1"
                method="post"
                className="wpcf7-form demo init"
                aria-label="Contact form"
                novalidate="novalidate"
                data-status="init"
              >
                <div className="header-left-side-menu-form-container">
                  <div className="header-left-side-menu-form-container-title">
                    <span className="gc-meta-title">Call to Us</span>
                    <address>
                      We’re available from 7 am – 8 pm EST,
                      <br />
                    </address>
                    <address>
                      7 days a week.
                      <br />
                    </address>
                    <span className="gc-meta-title">Customer Services:</span>
                    <div>+91 989 00 00</div>
                    <h6>Send a Message</h6>
                  </div>

                  <label>
                    <span
                      className="wpcf7-form-control-wrap"
                      data-name="text-204"
                    >
                      <input
                        size="40"
                        className="wpcf7-form-control wpcf7-text wpcf7-validates-as-required"
                        aria-required="true"
                        aria-invalid="false"
                        placeholder="Your name"
                        value=""
                        type="text"
                        name="text-204"
                      />
                    </span>
                  </label>
                  <label>
                    <span
                      className="wpcf7-form-control-wrap"
                      data-name="email-623"
                    >
                      <input
                        size="40"
                        className="wpcf7-form-control wpcf7-email wpcf7-validates-as-required wpcf7-text wpcf7-validates-as-email"
                        aria-required="true"
                        aria-invalid="false"
                        placeholder="Your E-mail"
                        value=""
                        type="email"
                        name="email-623"
                      />
                    </span>
                  </label>
                  <label>
                    <span
                      className="wpcf7-form-control-wrap"
                      data-name="menu-91"
                    >
                      <select
                        className="wpcf7-form-control wpcf7-select wpcf7-validates-as-required"
                        aria-required="true"
                        aria-invalid="false"
                        name="menu-91"
                      >
                        <option value="Technical Help">Technical Help</option>
                        <option value="Order">Order</option>
                        <option value="Refund">Refund</option>
                      </select>
                    </span>
                  </label>
                  <span
                    className="wpcf7-form-control-wrap"
                    data-name="textarea-999"
                  >
                    <textarea
                      cols="40"
                      rows="10"
                      className="wpcf7-form-control wpcf7-textarea wpcf7-validates-as-required"
                      aria-required="true"
                      aria-invalid="false"
                      placeholder="Message"
                      name="textarea-999"
                    ></textarea>
                  </span>
                  <button className="gc-btn-medium gc-btn gc-btn-black gc-fullwidth wpcf7-submit">
                    Submit
                    <span className="loading-wrapper">
                      <span className="ajax-loading"></span>
                    </span>
                  </button>
                </div>
                <div className="wpcf7-response-output" aria-hidden="true"></div>
              </form>
            </div>
          </div>

          <div
            className="compare-area action-content"
            data-target-name="compare"
            data-compare-count="0"
          >
            <div className="compare-content">
              <span className="panel-top-title">Your compared products</span>

              <div className="gc-panel-content-items gc-compare-content-items gc-perfect-scrollbar"></div>
              <div className="gc-panel-content-notice gc-compare-content-notice">
                <div className="gc-empty-content">
                  <svg
                    className="svgCompare gc-big-svg-icon"
                    width="512"
                    height="512"
                    fill="currentColor"
                    viewBox="0 0 30 30"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="m26 9a1 1 0 0 0 0-2h-4a1 1 0 0 0 -1 1v4a1 1 0 0 0 2 0v-1.66a9 9 0 0 1 -7 14.66c-.3 0-.6 0-.9 0a1 1 0 1 0 -.2 2c.36 0 .73.05 1.1.05a11 11 0 0 0 8.48-18.05z"></path>
                    <path d="m10 19a1 1 0 0 0 -1 1v1.66a9 9 0 0 1 8.8-14.48 1 1 0 0 0 .4-2 10.8 10.8 0 0 0 -2.2-.18 11 11 0 0 0 -8.48 18h-1.52a1 1 0 0 0 0 2h4a1 1 0 0 0 1-1v-4a1 1 0 0 0 -1-1z"></path>
                  </svg>{" "}
                  <div className="gc-small-title">
                    No product is added to the compare list!
                  </div>
                  <a
                    className="gc-btn-small mt-10"
                    href="https://none.com/themes/anarkali/shop/"
                  >
                    Start Shopping
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div
            className="wishlist-area action-content"
            data-target-name="wishlist"
            data-wishlist-count="0"
          >
            <div className="wishlist-content">
              <span className="panel-top-title has-clear-btn">
                Your Wishlist
                <span className="clear-all-wishlist">Clear All</span>
              </span>
              <div className="gc-panel-content-items gc-wishlist-content-items gc-perfect-scrollbar"></div>
              <div className="gc-panel-content-notice gc-wishlist-content-notice gc-empty-content">
                <svg
                  className="svgLove gc-big-svg-icon"
                  width="512"
                  height="512"
                  fill="currentColor"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m29.55 6.509c-1.73-2.302-3.759-3.483-6.031-3.509h-.076c-3.29 0-6.124 2.469-7.443 3.84-1.32-1.371-4.153-3.84-7.444-3.84h-.075c-2.273.026-4.3 1.207-6.059 3.549a8.265 8.265 0 0 0 1.057 10.522l11.821 11.641a1 1 0 0 0 1.4 0l11.82-11.641a8.278 8.278 0 0 0 1.03-10.562zm-2.432 9.137-11.118 10.954-11.118-10.954a6.254 6.254 0 0 1 -.832-7.936c1.335-1.777 2.831-2.689 4.45-2.71h.058c3.48 0 6.627 3.924 6.658 3.964a1.037 1.037 0 0 0 1.57 0c.032-.04 3.2-4.052 6.716-3.964a5.723 5.723 0 0 1 4.421 2.67 6.265 6.265 0 0 1 -.805 7.976z"></path>
                </svg>{" "}
                <div className="gc-small-title">
                  There are no products on the wishlist!
                </div>
                <a
                  className="gc-btn-small mt-10"
                  href="https://none.com/themes/anarkali/shop/"
                >
                  Start Shopping
                </a>
              </div>
              <a
                className="gc-btn-small wishlist-page-link"
                href="https://none.com/themes/anarkali/wishlist/"
              >
                Open Wishlist Page
              </a>
            </div>
          </div>
          <div
            className="account-area action-content"
            data-target-name="account"
          >
            <div className="panel-top-title">
              <span
                className="form-action-btn signin-title"
                data-target-form="login"
              >
                <span>Sign in&nbsp;</span>
                <svg
                  className="svgRight"
                  width="512"
                  height="512"
                  fill="currentColor"
                  viewBox="0 0 128 128"
                  enable-background="new 0 0 128 128"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="m64 0c-35.289 0-64 28.711-64 64s28.711 64 64 64 64-28.711 64-64-28.711-64-64-64zm0 120c-30.879 0-56-25.121-56-56s25.121-56 56-56 56 25.121 56 56-25.121 56-56 56zm26.828-58.828c1.563 1.563 1.563 4.094 0 5.656l-20 20c-.781.781-1.805 1.172-2.828 1.172s-2.047-.391-2.828-1.172c-1.563-1.563-1.563-4.094 0-5.656l13.172-13.172h-38.344c-2.209 0-4-1.789-4-4s1.791-4 4-4h38.344l-13.172-13.172c-1.563-1.563-1.563-4.094 0-5.656s4.094-1.563 5.656 0z"></path>
                </svg>{" "}
              </span>
              <span
                className="form-action-btn register-title"
                data-target-form="register"
              >
                <svg
                  className="svgUser2"
                  width="512"
                  height="512"
                  fill="currentColor"
                  viewBox="0 0 512 512"
                  enable-background="new 0 0 512 512"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <g>
                      <path d="m256 253.7c-62 0-112.4-50.4-112.4-112.4s50.4-112.4 112.4-112.4 112.4 50.4 112.4 112.4-50.4 112.4-112.4 112.4zm0-195.8c-46 0-83.4 37.4-83.4 83.4s37.4 83.4 83.4 83.4 83.4-37.4 83.4-83.4-37.4-83.4-83.4-83.4z"></path>
                    </g>
                    <g>
                      <path d="m452.1 483.2h-392.2c-8 0-14.5-6.5-14.5-14.5 0-106.9 94.5-193.9 210.6-193.9s210.6 87 210.6 193.9c0 8-6.5 14.5-14.5 14.5zm-377-29.1h361.7c-8.1-84.1-86.1-150.3-180.8-150.3s-172.7 66.2-180.9 150.3z"></path>
                    </g>
                  </g>
                </svg>{" "}
                <span>&nbsp;Register</span>
              </span>
            </div>

            <div className="account-area-form-wrapper">
              <div className="login-form-content active">
                <form
                  className="woocommerce-form woocommerce-form-login login gc-ajax-login"
                  method="post"
                >
                  <p className="form-row form-row-first gc-is-required">
                    <label for="username">
                      Username or email&nbsp;<span className="required">*</span>
                    </label>
                    <input
                      type="text"
                      className="input-text"
                      name="username"
                      id="username"
                      autocomplete="username"
                    />
                    <span className="gc-form-message"></span>
                  </p>
                  <p className="form-row form-row-last gc-is-required">
                    <label for="password">
                      Password&nbsp;<span className="required">*</span>
                    </label>
                    <input
                      className="input-text"
                      type="password"
                      name="password"
                      id="password"
                      autocomplete="current-password"
                    />
                    <span className="gc-form-message"></span>
                  </p>
                  <div className="clear"></div>

                  <p className="form-row">
                    <label className="woocommerce-form__label woocommerce-form__label-for-checkbox woocommerce-form-login__rememberme">
                      <input
                        className="woocommerce-form__input woocommerce-form__input-checkbox"
                        name="rememberme"
                        type="checkbox"
                        value="forever"
                      />{" "}
                      <span>Remember me</span>
                    </label>

                    <button
                      type="submit"
                      className="woocommerce-button button woocommerce-form-login__submit gc-btn-medium gc-btn gc-btn-black"
                      name="login"
                      value="Sign In"
                    >
                      Sign In
                      <span className="loading-wrapper">
                        <span className="ajax-loading"></span>
                      </span>
                    </button>
                  </p>
                  <p className="lost_password">
                    <a href="https://none.com/themes/anarkali/my-account/lost-password/">
                      Lost your password?
                    </a>
                  </p>

                  <div className="clear"></div>
                </form>
              </div>
              <div className="register-form-content">
                <form
                  method="post"
                  className="woocommerce-form woocommerce-form-register register"
                >
                  <p className="form-row gc-is-required">
                    <label for="reg_email">
                      Email address&nbsp;<span className="required">*</span>
                    </label>
                    <input
                      type="email"
                      className="woocommerce-Input woocommerce-Input--text input-text"
                      name="email"
                      id="reg_email"
                      autocomplete="email"
                      value=""
                    />
                    <span className="gc-form-message"></span>
                  </p>

                  <p>A password will be sent to your email address.</p>

                  <div className="woocommerce-privacy-policy-text">
                    <p>
                      Your personal data will be used to support your experience
                      throughout this website, to manage access to your account,
                      and for other purposes described in our{" "}
                      <a
                        href=""
                        className="woocommerce-privacy-policy-link"
                        target="_blank"
                      >
                        privacy policy
                      </a>
                      .
                    </p>
                  </div>
                  <p className="form-row">
                    <button
                      type="submit"
                      className="woocommerce-Button woocommerce-button button woocommerce-form-register__submit gc-btn-medium gc-btn gc-btn-black"
                      name="register"
                      value="Register"
                    >
                      Register
                      <span className="loading-wrapper">
                        <span className="ajax-loading"></span>
                      </span>
                    </button>
                  </p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default MobileHeader;
