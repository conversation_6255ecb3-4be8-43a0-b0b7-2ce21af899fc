const axios = require('axios');
const { models: { Order, Shipment, AdminSettings, Product } } = require('../../../../lib/models');
const { makeApiRequest } = require('../../util/shipping/shiprocket');
const { ShipRocketStatusCodes } = require('../../../../lib/models/enums');

class ShippingController {
    async shiprocketCreateShipment(req, res) {
        const { order_id } = req.body;

        try {
            const order = await Order.findOne({ _id: order_id })
                .populate('user', 'fullName formattedPhone email');

            if (!order) {
                return res.warn("INVALID ORDER ID");
            }

            // Prepare order items
            const order_items = (order.products || []).map((product, index) => ({
                name: product.name,
                sku: product.sku + index,
                units: product.quantity,
                selling_price: product.price,
            }));


            const settings  = await AdminSettings.findOne({}).select('packageWeight');


            // Prepare order details for Shiprocket API
            const orderDetails = {
                order_id: order?.orderId || "",
                order_date: order?.createdAt || new Date().toISOString(),
                pickup_location: "Primary",
                billing_customer_name: order?.shippingAddress?.fullName || "",
                billing_last_name: order?.shippingAddress?.lName || "",
                billing_address: order?.shippingAddress?.addressLine1 || "",
                billing_address_2: order?.shippingAddress?.addressLine2 || "",
                billing_city: order?.shippingAddress?.city || "",
                billing_pincode: order?.shippingAddress?.postalCode || "000000",
                billing_state: order?.shippingAddress?.state || "",
                billing_country: order?.shippingAddress?.country || "",
                billing_email: order?.user?.email || "",
                billing_phone: order?.shippingAddress?.phone || "0000000000",
                billing_alternate_phone: 9782459299 || 9887223537,
                shipping_is_billing: true,
                shipping_customer_name: order?.shippingAddress?.fullName || "",
                shipping_last_name: order?.shippingAddress?.lName || "",
                shipping_address: order?.shippingAddress?.addressLine1 || "",
                shipping_address_2: order?.shippingAddress?.addressLine2 || "",
                shipping_city: order?.shippingAddress?.city || "",
                shipping_pincode: order?.shippingAddress?.postalCode || "000000",
                shipping_country: order?.shippingAddress?.country || "",
                shipping_state: order?.shippingAddress?.state || "",
                shipping_email: order?.shippingAddress?.email || "",
                shipping_phone: order?.shippingAddress?.phone || "0000000000",
                order_items,
                payment_method: (order?.paymentMethod == 'Cash on Delivery') ? 'COD' : 'Prepaid',
                shipping_charges: 0,
                giftwrap_charges: 0,
                transaction_charges: 0,
                total_discount: order?.totalDiscount || 0.0,
                sub_total: order?.totalPrice || 0.0,
                length: 10.0,
                breadth: 10.0,
                height: 10.0,
                weight: parseInt((settings?.packageWeight)/1000) || 1.0,
                ewaybill_no: order?.waybill || ""
            };

            // Shiprocket API endpoint
            const endpoint = `/orders/create/adhoc`;
          
            // Make the API request
            const data = await makeApiRequest(endpoint, 'POST', orderDetails, req.shiprocketToken);

            // Check if API request was successful
            if (!data || !data.shipment_id) {
                return res.warn('Failed to create shipment with Shiprocket.');
            }

            // Create a new Shipment document
            const newShipment = new Shipment({
                shipment_id: data.shipment_id,
                order_id: order_id,
                tracking_number: data.awb_code || '',
                courier_company_id: data.courier_company_id || null,
                courier_name: data.courier_name || '',
                applied_weight: data.weight || 0.0,
                freight_charges: data.freight_charges || 0.0,
                invoice_no: data.invoice_no || '',
                transporter_id: data.transporter_id || '',
                transporter_name: data.transporter_name || '',
                awb_code: data.awb_code || '',
                label_url: data.label_url || '',
                manifest_status: data.manifest_status || 'Not Generated',
                pickup_status: data.pickup_status || 0,
                pickup_token_number: data.pickup_token_number || null,
                pickup_scheduled_date: data.pickup_scheduled_date || null,
                pickup_generated_date: data.pickup_generated_date || null,
            });

            // Save the new Shipment document
            const savedShipment = await newShipment.save();

            // Update the Order document to include the new Shipment
            order.shiprocket_order_id = data.payload.order_id
            order.shipments.push(savedShipment._id);
            await order.save();

            return res.success(data, req.__('CREATED_ORDER_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while creating order:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }


    async wrapperApiToCreateOrder(req, res) {
        const { order_id } = req.body;

        try {
            const order = await Order.findOne({ _id: order_id })
                .populate('user', 'fullName formattedPhone email');

            if (!order) {
                return res.warn("INVALID ORDER ID");
            }

            // Prepare order items
            const order_items = (order.products || []).map((product, index) => ({
                name: product.name,
                sku: product.sku + index,
                units: product.quantity,
                selling_price: product.price,
            }));

            const settings  = await AdminSettings.findOne({}).select('packageWeight');
            

            // Prepare order details for Shiprocket API
            const orderDetails = {
                order_id: order?.orderId || "",
                order_date: order?.createdAt || new Date().toISOString(),
                pickup_location: "Primary",
                billing_customer_name: order?.shippingAddress?.fullName || "",
                billing_last_name: order?.shippingAddress?.lName || "",
                billing_address: order?.shippingAddress?.addressLine1 || "",
                billing_address_2: order?.shippingAddress?.addressLine2 || "",
                billing_city: order?.shippingAddress?.city || "",
                billing_pincode: order?.shippingAddress?.postalCode || "000000",
                billing_state: order?.shippingAddress?.state || "",
                billing_country: order?.shippingAddress?.country || "",
                billing_email: order?.user?.email || "",
                billing_phone: order?.shippingAddress?.phone || "0000000000",
                billing_alternate_phone: order?.user?.phone || "0000000000",
                shipping_is_billing: true,
                shipping_customer_name: order?.shippingAddress?.fullName || "",
                shipping_last_name: order?.shippingAddress?.lName || "",
                shipping_address: order?.shippingAddress?.addressLine1 || "",
                shipping_address_2: order?.shippingAddress?.addressLine2 || "",
                shipping_city: order?.shippingAddress?.city || "",
                shipping_pincode: order?.shippingAddress?.postalCode || "000000",
                shipping_country: order?.shippingAddress?.country || "",
                shipping_state: order?.shippingAddress?.state || "",
                shipping_email: order?.shippingAddress?.email || "",
                shipping_phone: order?.shippingAddress?.phone || "0000000000",
                order_items,
                payment_method: (order?.paymentMethod == 'Cash on Delivery') ? 'COD' : 'Prepaid',
                shipping_charges: 0,
                giftwrap_charges: 0,
                transaction_charges: 0,
                total_discount: order?.totalDiscount || 0.0,
                sub_total: order?.totalPrice || 0.0,
                length: 10.0,
                breadth: 10.0,
                height: 10.0,
                weight: parseFloat((settings?.packageWeight)/1000) || 1.0,
                ewaybill_no: order?.waybill || ""
            };

            // console.log("order  >>>",order)
            // console.log("orderDetails  >>>",orderDetails)
            
            // Shiprocket API endpoint
            const endpoint = `/shipments/create/forward-shipment`;

            // Make the API request
            const data = await makeApiRequest(endpoint, 'POST', orderDetails, req.shiprocketToken);
            // console.log("check>>>>", data)
            // Check if API request was successful
            if (!data || !data.payload.shipment_id) {
                return res.warn('Failed to create shipment with Shiprocket.');
            }

            let shipment_data = {}
            if (data.status && data.status == 1){
                shipment_data = data.payload
            }
            else{
                return res.warn('Failed to create shipment with Shiprocket.');
            }
            // Create a new Shipment document
            const newShipment = new Shipment({
                shipment_id: shipment_data?.shipment_id,
                order_id: shipment_data?.order_id,
                tracking_number: shipment_data?.awb_code || '',
                courier_company_id: shipment_data?.courier_company_id || null,
                courier_name: shipment_data?.courier_name || '',
                applied_weight: shipment_data?.weight || 0.0,
                freight_charges: shipment_data?.freight_charges || 0.0,
                invoice_no: shipment_data?.invoice_no || '',
                transporter_id: shipment_data?.transporter_id || '',
                transporter_name: shipment_data?.transporter_name || '',
                awb_code: shipment_data?.awb_code || '',
                label_url: shipment_data?.label_url || '',
                manifest_url: shipment_data?.manifest_url || '',
                manifest_status: shipment_data?.manifest_generated || 0,
                pickup_status: shipment_data?.pickup_generated || 0,
                pickup_token_number: shipment_data?.pickup_token_number || null,
                pickup_scheduled_date: shipment_data?.pickup_scheduled_date || null,
                pickup_generated_date: shipment_data?.pickup_booked_date || null,
            });

            // Save the new Shipment document
            const savedShipment = await newShipment.save();

            // Update the Order document to include the new Shipment
            order.shipments.push(savedShipment._id);
            order.status = ShipRocketStatusCodes.SHIPPED;
            await order.save();

            return res.success(data, req.__('CREATED_ORDER_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while creating order:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }

    async wrapperApiToReturnOrder(req, res) {
        const { order_id } = req.body;

        try {
            const order = await Order.findOne({ _id: order_id })
                .populate('user', 'fullName formattedPhone email');

            if (!order) {
                return res.warn("INVALID ORDER ID");
            }
            const shipment = await Shipment.findOne(order.shipments[0])
            // Prepare order items
            const order_items = (order.products || []).map((product, index) => ({
                name: product.name,
                sku: product.sku + index,
                units: product.quantity,
                selling_price: product.price,
            }));

            const settings  = await AdminSettings.findOne({}).select('packageWeight');

            // Prepare order details for Shiprocket API
            console.log("order>>>>>", order)
            const returnDetails = {
                order_id: order?.orderId || "",
                order_date: order?.createdAt || new Date().toISOString(),
                pickup_customer_name: order?.shippingAddress?.fullName || "",
                pickup_last_name: order?.shippingAddress?.lName || "",
                pickup_address: order?.shippingAddress?.addressLine1 || "",
                pickup_address_2: order?.shippingAddress?.addressLine2 || "",
                pickup_city: order?.shippingAddress?.city || "",
                pickup_pincode: order?.shippingAddress?.postalCode || "000000",
                pickup_state: order?.shippingAddress?.state || "",
                pickup_country: order?.shippingAddress?.country || "",
                pickup_email: order?.user?.email || "",
                pickup_phone: order?.shippingAddress?.phone?.replace("+91", "") || "0000000000",
                pickup_alternate_phone: order?.user?.formattedPhone || "0000000000",
                shipping_is_billing: true,
                shipping_customer_name: order?.shippingAddress?.fullName || "",
                shipping_last_name: order?.shippingAddress?.lName || "",
                shipping_address: order?.shippingAddress?.addressLine1 || "",
                shipping_address_2: order?.shippingAddress?.addressLine2 || "",
                shipping_city: order?.shippingAddress?.city || "",
                shipping_pincode: order?.shippingAddress?.postalCode || "000000",
                shipping_country: order?.shippingAddress?.country || "",
                shipping_state: order?.shippingAddress?.state || "",
                shipping_email: order?.shippingAddress?.email || "",
                shipping_phone: order?.shippingAddress?.phone?.replace("+91", "") || "0000000000",
                order_items,
                payment_method: order?.paymentMethod === "Cash on Delivery" ? "COD" : "Prepaid",
                total_discount: order?.totalDiscount || 0.0,
                sub_total: order?.totalPrice || 0.0,
                length: 10.0,
                breadth: 10.0,
                height: 10.0,
                weight: parseFloat((settings?.packageWeight)/1000) || 1.0
            };

            console.log("returnDetails  >>>",returnDetails)


            // Shiprocket API endpoint
            const endpoint = `/shipments/create/return-shipment`;

            // Make the API request
            const data = await makeApiRequest(endpoint, 'POST', returnDetails, req.shiprocketToken);
            if (!data || !data.payload.shipment_id) {
                return res.warn('Failed to create shipment with Shiprocket.');
            }

            let shipment_data = {}
            if (data.status && data.status == 1){
                shipment_data = data.payload
            }
            else{
                return res.warn('Failed to create shipment with Shiprocket.');
            }
            // Create a new Shipment document
            const newShipment = new Shipment({
                shipment_id: shipment_data?.shipment_id,
                order_id: shipment_data?.order_id,
                tracking_number: shipment_data?.awb_code || '',
                courier_company_id: shipment_data?.courier_company_id || null,
                courier_name: shipment_data?.courier_name || '',
                applied_weight: shipment_data?.weight || 0.0,
                freight_charges: shipment_data?.freight_charges || 0.0,
                invoice_no: shipment_data?.invoice_no || '',
                transporter_id: shipment_data?.transporter_id || '',
                transporter_name: shipment_data?.transporter_name || '',
                awb_code: shipment_data?.awb_code || '',
                label_url: shipment_data?.label_url || '',
                manifest_url: shipment_data?.manifest_url || '',
                manifest_status: shipment_data?.manifest_generated || 0,
                pickup_status: shipment_data?.pickup_generated || 0,
                pickup_token_number: shipment_data?.pickup_token_number || null,
                pickup_scheduled_date: shipment_data?.pickup_scheduled_date || null,
                pickup_generated_date: shipment_data?.pickup_booked_date || null,
            });

            // Save the new Shipment document
            const savedShipment = await newShipment.save();

            // Update the Order document to include the new Shipment
            order.shiprocket_order_id = data.payload.order_id
            order.refund_shipment.push(savedShipment._id);
            order.status = ShipRocketStatusCodes.RTO_INITIATED;
            await order.save();

            return res.success(data, req.__('RETURNED_ORDER_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while returning order:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }

    async shiprocketGenerateLabel(req, res) {
        try {
            const { shipment_id, courier_id } = req.body; // Extract shipment ID from request body
            const endpoint = `/courier/generate/label`;


            const data = await makeApiRequest(endpoint, 'POST', { shipment_id, courier_id }, req.shiprocketToken);

            if (!data || !data.label_url) {
                return res.warn('Failed to generate label.');
            }


            const shipment = await Shipment.findOne({ shipment_id });

            if (!shipment) {
                return res.warn('Shipment not found.');
            }


            shipment.label_url = data.label_url;
            shipment.manifest_status = 'Generated';
            shipment.label_generated_date = new Date();

            // Save the updated Shipment document
            await shipment.save();

            return res.success(data, req.__('GENERATED_LABEL_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while generating label:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }

    async shiprocketGenerateManifest(req, res) {
        try {
            const { shipment_id } = req.body;
            const endpoint = `/manifests/generate`;


            const data = await makeApiRequest(endpoint, 'POST', { shipment_id }, req.shiprocketToken);

            if (!data || !data.manifest_url) {
                return res.warn('Failed to generate manifest.');
            }


            const shipment = await Shipment.findOne({ shipment_id });

            if (!shipment) {
                return res.warn('Shipment not found.');
            }


            shipment.manifest_url = data.manifest_url;
            shipment.manifest_status = 'Generated';
            shipment.manifest_generated_date = new Date();

            // Save the updated Shipment document
            await shipment.save();

            return res.success(data, req.__('GENERATED_MANIFEST_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while generating manifest:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }

    async shiprocketGeneratePickup(req, res) {
        try {
            const { shipment_id } = req.body;
            const endpoint = `/courier/generate/pickup`;


            const data = await makeApiRequest(endpoint, 'POST', { shipment_id }, req.shiprocketToken);

            if (!data || !data.pickup_reference_number) {
                return res.warn('Failed to generate pickup.');
            }


            const shipment = await Shipment.findOne({ shipment_id });

            if (!shipment) {
                return res.warn('Shipment not found.');
            }


            shipment.pickup_status = 'Scheduled';
            shipment.pickup_reference_number = data.pickup_reference_number;
            shipment.pickup_scheduled_date = new Date();


            await shipment.save();

            return res.success(data, req.__('GENERATED_PICKUP_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while generating pickup:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }

    async shiprocketGenerateAWB(req, res) {
        try {
            const { shipment_id, courier_id } = req.body;
            console.log(shipment_id,"PPPPP")
            console.log(req.body,"PPPPP")
            const endpoint = `/courier/assign/awb`;


            const data = await makeApiRequest(endpoint, 'POST', { shipment_id, courier_id }, req.shiprocketToken);

            if (!data || !data.awb_code) {
                return res.warn('Failed to generate AWB.');
            }


            const shipment = await Shipment.findOne({ shipment_id });

            if (!shipment) {
                return res.warn('Shipment not found.');
            }


            shipment.awb_code = data.awb_code;
            shipment.awb_status = 'Generated';
            shipment.awb_generated_date = new Date();


            await shipment.save();

            return res.success(data, req.__('GENERATED_AWB_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while generating AWB for shipment:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }


    async checkAreaAvailability(req, res) {
        const { pincode } = req.query;

        try {
            // Endpoint to check serviceability based on provided pincode
            const endpoint = `/courier/serviceability?pickup_postcode=${process.env.PICKUP_PINCODE}&delivery_postcode=${pincode}&weight=1&cod=1`;
            // Make the API request using the Shiprocket token from middleware
            const data = await makeApiRequest(endpoint, 'GET', null, req.shiprocketToken);

            return res.success(data, req.__('AREA_AVAILABILITY_CHECKED'));
        } catch (error) {
            console.error('Error while checking area availability:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    };

    async getTrackingByAWB(req, res) {
        const { awb_code } = req.query;

        try {
            // Endpoint to check serviceability based on provided pincode
            const endpoint = `/courier/track/awb/${awb_code}`;

            // Make the API request using the Shiprocket token from middleware
            const data = await makeApiRequest(endpoint, 'GET', null, req.shiprocketToken);

            return res.success(data, req.__('GOT_TRACKING_DETAILS_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while getting tracking:', error);
            return res.serverError(error.response.data.message, req.__('INTERNAL_SERVER_ERROR'));
        }
    };

    async shiprocketCancelShipment(req, res) {
        const { order_id, awbsIds } = req.body;
 
        try {
            const order = await Order.findOne({ _id: order_id }).populate('user', 'fullName formattedPhone email');
 
            if (!order) {
                return res.warn('INVALID ORDER ID');
            }
            if (awbsIds.length <= 0) {
                return res.warn('Please provide atleast one AWB id');
            }
 
            // Shiprocket API endpoint
            const endpoint = `/orders/cancel/shipment/awbs`;
            const awbs = {
                "awbs": awbsIds
            };
 
            // Make the API request
            const data = await makeApiRequest(endpoint, 'POST', awbs, req.shiprocketToken);
 
            // Check if API request was successful
            if (!data || !data.shipment_id) {
                return res.warn('Failed to cancel shipment with Shiprocket.');
            }
 
 
            console.log("data.response >>>",data.response);
 
            return res.success(data, req.__('CANCEL_ORDER_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while creating order:', error);
            return res.serverError(
                error?.response?.data?.message || 'Unknown error occurred',
                req.__('INTERNAL_SERVER_ERROR')
            );
        }
    }

}


/**
 * Polling function to periodically check the shipment status
 * and update the shipment details once available.
 */
const pollShipmentStatus = async (shipmentId, token, shipmentDbId) => {
    try {
        const pollInterval = 60000; // Poll every 60 seconds

        const checkShipmentStatus = async () => {
            const response = await axios.get(
                `https://apiv2.shiprocket.in/v1/external/courier/track/shipment/${shipmentId}`,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                }
            );

            const { awb_code, courier_name } = response.data;

            if (awb_code && courier_name) {
                // Update the shipment once the details are available
                await Shipment.findByIdAndUpdate(shipmentDbId, {
                    tracking_number: awb_code,
                    courier_name: courier_name
                });

                console.log(`Shipment ${shipmentId} details updated: AWB ${awb_code}, Courier ${courier_name}`);
                return true;
            } else {
                console.log(`Shipment ${shipmentId} details are still pending, polling again...`);
                return false;
            }
        };

        // Poll every 60 seconds until the AWB and courier details are available
        const pollShipmentDetails = async () => {
            let completed = false;
            while (!completed) {
                completed = await checkShipmentStatus();
                if (!completed) {
                    await new Promise(resolve => setTimeout(resolve, pollInterval));
                }
            }
        };

        pollShipmentDetails();
    } catch (error) {
        console.log("JSON.stringify(error) >>", JSON.stringify(error.data))
        console.error('Error polling shipment status:', JSON.stringify(error));
    }




};
module.exports = new ShippingController();