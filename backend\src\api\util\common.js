const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const { v4: uuidv4 } = require('uuid');
const ShortUniqueId = require('short-unique-id');

const getPlatform = req => req.headers['x-eshop-platform'];

const getLanguage = req => req.headers['accept-language'];

const generateToken = payload => jwt.sign(payload, process.env.JWT_SECRET);

const getObjectId = id => ObjectId(id);

const generateUniqueOrderId = () => {
    // You can customize the prefix and format as needed
    const prefix = 'GC-';
    const timestamp = Date.now();
    const uniqueId = uuidv4();

    return `${prefix}${timestamp}-${uniqueId}`;
};

const generateShortUniqueOrderId = () => {
    // Initialize the ShortUniqueId instance
    const uid = new ShortUniqueId();
    // You can customize the prefix and format as needed
    const prefix = 'GC-';
    const uniqueId = uid.stamp(32);

    return `${prefix}${uniqueId}`;
};

const getFormattedDate = date => {
    const d = new Date(date);
    const month = d.toLocaleString('default', { month: 'long' });
    const day = d.getDate();
    const year = d.getFullYear();
    return `${month} ${day}, ${year}`;
};

const generateShortUniqueOrderIdRandom = () => {
    const dt = String(new Date().getTime());
    const randomDigits = Math.floor(100 + Math.random() * 900); 
    const orderId = `GC-${dt}${randomDigits}`;
    return orderId;
};

module.exports = {
    getPlatform,
    getLanguage,
    generateToken,
    getObjectId,
    generateUniqueOrderId,
    generateShortUniqueOrderId,
    getFormattedDate,
    generateShortUniqueOrderIdRandom,
};