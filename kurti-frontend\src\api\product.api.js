import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./api-client";

export const useProductsQuery = (
  params = {
    page: 1,
    limit: 10,
  }
) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_PRODUCTS, {
        params,
      });
      if (response?.success) {
        return response.data;
      }
      return [];
    },
    queryKey: ["products", params],
  });

export const useProductInfoQuery = (id) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(`${API_ENDPOINTS.GET_PRODUCT_INFO}/${id}`);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["product-info", id],
    enabled: !!id,
  });
