export const ageOptions = Array.from({ length: 83 }, (_, i) => i + 18);

export const getEstimatedDeliveryDate = () => {
  const date = new Date();
  date.setDate(date.getDate() + 6);

  const options = { month: "long", day: "numeric" };
  const formattedDate = date.toLocaleDateString("en-US", options);

  return formattedDate;
};

export const generateRandomNumber = (start = 10, end = 100) => {
  return Math.floor(Math.random() * (end - start + 1) + start);
};

export const getFormattedDate = (date) => {
  if(!date) return;
  const d = new Date(date);
  const month = d.toLocaleString("default", { month: "long", timeZone: "UTC" });
  const day = d.getUTCDate();
  const year = d.getUTCFullYear();
  return `${day} ${month}, ${year}`;
};

export const preFillValues = (sectionInitialValues, savedValues) => {
  const initialValues = {};
  Object.keys(sectionInitialValues).forEach((key) => {
    initialValues[key] = savedValues[key] || sectionInitialValues[key];
  });
  return initialValues;
};

export const getAllSearchParams = (searchParams) => {
  const params = {};
  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }
  return params;
};

export const decodeHtml = (html) => {
  const txt = document.createElement("textarea");
  txt.innerHTML = html;
  return txt.value;
};

export const formatDate = (dateString) => {
  if (!dateString) return '';

  const date = new Date(dateString);
  const options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };

  return date.toLocaleDateString('en-US', options);
};