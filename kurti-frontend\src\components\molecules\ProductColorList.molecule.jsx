import React from "react";

const ProductColorListMolecule = ({
  colors = [],
  selectedColor,
  onSelectColor,
}) => {
  const handleColorClick = (color) => {
    if (onSelectColor) onSelectColor(color);
  };

  return (
    <div className="gc-terms gc-type-color terms-shape-default outline-0">
      {colors.length > 0 &&
        colors.map((color, index) => {
          return (
            <span
              key={index}
              className={`gc-term ${
                selectedColor?._id === color._id ? "gc-selected" : ""
              } ${color.hexCode === "#ffffff" ? "gc-white" : ""}`}
              style={{
                backgroundColor: `${color.hexCode}`,
                marginRight: "5px",
              }}
              onClick={() => handleColorClick(color)}
            >
              {color.name}
            </span>
          );
        })}
    </div>
  );
};

export default ProductColorListMolecule;
