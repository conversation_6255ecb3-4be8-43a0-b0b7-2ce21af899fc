    /orders/invoice/{orderId}/generate:
        get:
            summary: Generate an invoice for an order
            operationId: generateInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to generate the invoice
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice generated successfully
                    content:
                        application/json:
                            schema:
                                allOf:
                                    - $ref: '#/components/schemas/ApiResponse'
                                    - type: object
                                      properties:
                                          data:
                                              type: object
                                              properties:
                                                  invoiceUrl:
                                                      type: string
                                                      description: URL to download the invoice
                                                      example: "/api/orders/invoice/60c72b2f9b1d8c1b4a4b1f8b/download"
                                                  invoicePath:
                                                      type: string
                                                      description: Path to the invoice file on the server
                                                      example: "/tmp/invoice-SNK-123456789.pdf"
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/download:
        get:
            summary: Download an invoice for an order
            operationId: downloadInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to download the invoice
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice PDF file
                    content:
                        application/pdf:
                            schema:
                                type: string
                                format: binary
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/email:
        post:
            summary: Send an invoice via email
            operationId: emailInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to email the invoice
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice sent via email successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'

    /orders/invoice/{orderId}/whatsapp:
        post:
            summary: Send an invoice via WhatsApp
            operationId: whatsappInvoice
            tags:
                - Orders
            security:
                - ApiKeyAuth: []
            parameters:
                - $ref: '#/components/parameters/headerLanguage'
                - $ref: '#/components/parameters/headerPlatform'
                - $ref: '#/components/parameters/headerVersion'
                - name: orderId
                  in: path
                  description: Order ID for which to send the invoice via WhatsApp
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Invoice sent via WhatsApp successfully
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ApiResponse'
                '400':
                    $ref: '#/components/responses/BadRequestError'
                '401':
                    $ref: '#/components/responses/UnauthorizedError'
                '404':
                    $ref: '#/components/responses/NotFoundError'
                '500':
                    $ref: '#/components/responses/ServerError'
