// src/components/ProductDetail.js
import React, { useState } from "react";
import Zoom from "react-medium-image-zoom";
import "react-medium-image-zoom/dist/styles.css";
import { Box, Grid, ImageList, ImageListItem } from "@mui/material";

import Banner1 from "../assets/images/product1-30.jpg";
import Banner2 from "../assets/images/product2-29.jpg";
import Banner3 from "../assets/images/product3-24.jpg";
import Banner4 from "../assets/images/product5-18.jpg";
import Banner5 from "../assets/images/product4-24.jpg";

const productImages = [
  "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product1-23.jpg",
  "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product4-20.jpg",
  "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product5-11.jpg",
  "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product2-21.jpg",
  "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product1-22.jpg",
];

const ProductDetail = () => {
  const [selectedImage, setSelectedImage] = useState(productImages[0]);

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Zoom>
        <img
          src={selectedImage}
          alt="Product"
          style={{ width: "100%", maxWidth: "600px", height: "auto" }}
        />
      </Zoom>
      <ImageList sx={{ width: "100%", maxWidth: "600px", mt: 2 }} cols={5}>
        {productImages.map((image, index) => (
          <ImageListItem key={index}>
            <img
              src={image}
              alt={`Thumbnail ${index + 1}`}
              style={{ cursor: "pointer", width: "100%", height: "auto" }}
              onClick={() => setSelectedImage(image)}
            />
          </ImageListItem>
        ))}
      </ImageList>
    </Box>
  );
};

export default ProductDetail;
