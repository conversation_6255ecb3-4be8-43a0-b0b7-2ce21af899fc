import { API_ENDPOINTS } from "../constants/endpoints";
import { apiSlice, handleResponse } from "./base.service";

export const reviewApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAllReviews: builder.query({
      query: ({ params }) => ({
        url: API_ENDPOINTS.GET_ALL_REVIEWS,
        method: "GET",
        params,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      providesTags: ["reviews-list"],
    }),
    
    getProductReviews: builder.query({
      query: ({ productId, params }) => ({
        url: `${API_ENDPOINTS.GET_PRODUCT_REVIEWS}/${productId}`,
        method: "GET",
        params,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
    }),
    
    updateReview: builder.mutation({
      query: (payload) => ({
        url: API_ENDPOINTS.UPDATE_REVIEW,
        method: "PUT",
        body: payload,
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: ["reviews-list"],
    }),
    
    deleteReview: builder.mutation({
      query: ({ productId, reviewId }) => ({
        url: `${API_ENDPOINTS.DELETE_REVIEW}/${productId}/${reviewId}`,
        method: "DELETE",
      }),
      transformResponse: handleResponse,
      transformErrorResponse: handleResponse,
      invalidatesTags: ["reviews-list"],
    }),
  }),
});

export const {
  useGetAllReviewsQuery,
  useGetProductReviewsQuery,
  useUpdateReviewMutation,
  useDeleteReviewMutation,
} = reviewApiSlice;
