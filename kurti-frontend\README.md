# Gajan Creation - E-commerce Frontend

A modern React-based frontend for Gajan Creation, an e-commerce platform specializing in kurtis and ethnic wear.

## 🚀 Quick Start

### Prerequisites
- Node.js 18 or higher
- npm or yarn

### Installation
```bash
# Install dependencies
npm install --legacy-peer-deps
# or
yarn install --legacy-peer-deps
```

### Development
```bash
# Start development server
npm start
# or
yarn start
```

### Production Build
```bash
# Build for production
npm run build
# or
yarn build
```

## 🛠️ Tech Stack

- **Framework**: React
- **Build Tool**: Vite
- **Styling**: [Add your styling library here]
- **State Management**: [Add your state management solution here]
- **API Integration**: [Add details about API integration]

## 📁 Project Structure

```
kurti-frontend/
├── public/          # Static files
├── src/             # Source files
│   ├── assets/      # Images, fonts, etc.
│   ├── components/  # Reusable components
│   ├── pages/       # Page components
│   ├── services/    # API services
│   ├── utils/       # Utility functions
│   ├── App.jsx      # Main App component
│   └── main.jsx     # Entry point
└── ...
```

## 🔄 CI/CD

This project uses GitLab CI/CD for automated deployment:
- Builds are triggered on commits to the `develop` branch
- Frontend is deployed to the production server at gajancreation.com

## 🔗 Related Projects

- [Admin Dashboard](https://admin.gajancreation.com) - Admin interface for managing products, orders, etc.
- [Backend API](https://api.gajancreation.com) - Backend services powering the e-commerce platform

## 📝 License

MIT © [Ronak Sethi](https://gitlab.com/RonakSethi)

## 📧 Contact

- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh
