import { Typography, Box, useTheme } from "@mui/material";
import { tokens } from "../theme";

const Header = ({ title, subtitle }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Box mb="30px">
      <Typography
        variant="h2"
        color={colors.blackAccent[800]}
        fontWeight={400}
        sx={{ m: "0 0 5px 0", textTransform:"capitalize" }}
      >
        {title}
      </Typography>
      <Typography variant="h5" color={colors.blueAccent[600]}>
        {subtitle}
      </Typography>
    </Box>
  );
};

export default Header;
