import React from 'react'
const dummyData = [
  {
    "title": "Women t-shirts",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product4-1-100x100.jpg",
    "count": 18,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in women's clothing, it completes the elegance of women in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Men t-shirts",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product1-30-100x100.jpg",
    "count": 15,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in men's clothing, it completes the elegance of men in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Kids",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product5-25-100x100.jpg",
    "count": 15,
    "description": "A brand that stands out with its colorful and fun designs that combine style and comfort in the world of little ones, it gains the trust of parents by prioritizing the comfort and elegance of children with its quality fabrics and carefully selected details."
  },
  {
    "title": "Festive Wear",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product3-1-100x100.jpg",
    "count": 19,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in women's clothing, it completes the elegance of women in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Ethnic Wear",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product5-16-100x100.jpg",
    "count": 14,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in men's clothing, it completes the elegance of men in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Men",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product2-29-100x100.jpg",
    "count": 16,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in men's clothing, it completes the elegance of men in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Women",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product1-22-100x100.jpg",
    "count": 20,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in women's clothing, it completes the elegance of women in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Kurta Pant Set",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product5-23-100x100.jpg",
    "count": 10,
    "description": "A brand that stands out with its colorful and fun designs that combine style and comfort in the world of little ones, it gains the trust of parents by prioritizing the comfort and elegance of children with its quality fabrics and carefully selected details."
  },
  {
    "title": "Lehenga Set",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product4-32-100x100.jpg",
    "count": 12,
    "description": "A brand that stands out with its colorful and fun designs that combine style and comfort in the world of little ones, it gains the trust of parents by prioritizing the comfort and elegance of children with its quality fabrics and carefully selected details."
  },
  {
    "title": "Sharara Set",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product3-35-100x100.jpg",
    "count": 9,
    "description": "A brand that stands out with its colorful and fun designs that combine style and comfort in the world of little ones, it gains the trust of parents by prioritizing the comfort and elegance of children with its quality fabrics and carefully selected details."
  },
  {
    "title": "Sherwanis",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product5-15-100x100.jpg",
    "count": 7,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in men's clothing, it completes the elegance of men in daily life and special moments with its unique style and attention to details."
  },
  {
    "title": "Lehangas",
    "href": "#",
    "imageUrl": "https://none.com/themes/anarkali/wp-content/uploads/2023/09/product4-12-100x100.jpg",
    "count": 12,
    "description": "A brand that stands out with its modern designs and quality materials that combine elegance and comfort in women's clothing, it completes the elegance of women in daily life and special moments with its unique style and attention to details."
  }
]



const Taxonomy = () => {
  return (
    <>

      <div className="container">
        <div className="gc-taxonomy-list">
          {
            dummyData.map((item) => {
              return (
                <div className="taxonomy-item" key={item.title}>
                  <a className="taxonomy-link" href="#" title={item.title}>
                    <div className="taxonomy-thumb">
                      <img decoding="async" width="100" height="100" className="attachment-woocommerce_gallery_thumbnail size-woocommerce_gallery_thumbnail entered lazyloaded" alt="" src={item.imageUrl} />
                    </div>
                    <span className="taxonomy-title">{item.title}</span>
                    <span className="taxonomy-count">{item.count}</span>
                  </a>
                  <div className="taxonomy-details position-right">
                    <p className="taxonomy-desc">
                      {item.description}
                    </p>
                  </div>
                </div>
              )
            })
          }
        </div>
      </div>


    </>
  )
}

export default Taxonomy