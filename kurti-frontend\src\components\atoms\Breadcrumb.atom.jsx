import React from 'react';
import { Link } from 'react-router-dom';

const BreadcrumbAtom = ({ title, links = [] }) => {
  return (
    <div className="gc-shop-hero gc-page-hero page-hero-mini">
      <div className="container-xl gc-container-xl">
        <div className="row">
          <div className="col-12">
            <div className="gc-page-hero-content gc-flex gc-align-center gc-justify-center">
              <h2>{title}</h2>
              <nav className="gc-breadcrumbs">
                <ul className="gc-breadcrumb">
                  <li className="breadcrumb-item">
                    <Link to="/" rel="home">
                      <span>Home</span>
                    </Link>
                  </li>
                  {links.map((link, index) => (
                    <li key={index} className="breadcrumb-item">
                      {link.url ? (
                        <Link to={link.url}>
                          <span>{link.label}</span>
                        </Link>
                      ) : (
                        <span>{link.label}</span>
                      )}
                    </li>
                  ))}
                  <li className="breadcrumb-item active">
                    <span>{title}</span>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BreadcrumbAtom;
