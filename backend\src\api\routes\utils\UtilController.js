const axios = require('axios');
const { sendMail } = require('../../../../lib/mailer');
const {
    models: { ContactUs, User, Product, Order },
} = require('../../../../lib/models');
class UtilController {
    async uploadFile(req, res) {
        const { location, type, count = 1 } = req.query;
        const extensions = { IMAGE: 'jpg', 'DOCUMENT.PDF': 'pdf' };
        const extension = extensions[type] || '';
        if (!extension) return res.warn('', req.__('INVALID_FILE_TYPE'));

        const uploader = require('../../../../lib/uploader');
        const promises = [];
        for (let i = 1; i <= count; i++) {
            promises.push(uploader.getSignedUrl(location.endsWith('/') ? location : `${location}/`, extension));
        }

        const urls = await Promise.all(promises);
        return res.success(urls);
    }

    async pincodeServiceability(req, res) {
        const { pincode } = req.query;
        try {
            const response = await axios.get(
                `https://track.delhivery.com/c/api/pin-codes/json/?token=${process.env.DELHIVERY_TOKEN}&filter_codes=${pincode}`
            );

            if (response.data && response.data.delivery_codes.length > 0) {
                const deliveryInfo = response.data.delivery_codes[0].postal_code;

                return res.success({
                    city: deliveryInfo.city,
                    district: deliveryInfo.district,
                    state_code: deliveryInfo.state_code,
                    max_weight: deliveryInfo.max_weight,
                    cod_available: deliveryInfo.cod === 'Y',
                    pre_paid_available: deliveryInfo.pre_paid === 'Y',
                    cash_available: deliveryInfo.cash === 'Y',
                    pickup_available: deliveryInfo.pickup === 'Y',
                    replacement_available: deliveryInfo.repl === 'Y',
                    covid_zone: deliveryInfo.covid_zone,
                    is_oda: deliveryInfo.is_oda === 'N',
                    remarks: deliveryInfo.remarks,
                    sort_code: deliveryInfo.sort_code,
                    sun_tat: deliveryInfo.sun_tat,
                    centers: deliveryInfo.center.map(center => ({
                        code: center.code,
                        center_name: center.cn,
                        sort_code: center.sort_code,
                    })),
                });
            } else {
                return res.warn({ error: 'Pincode not serviceable' });
            }
        } catch (error) {
            console.error('Error retrieving pincode serviceability:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async delhiveryTrackShipment(req, res) {
        const { waybill } = req.query;
        try {
            const response = await axios.get(
                // `https://track.delhivery.com/api/v1/packages/json?waybill=${waybill}&token=${process.env.DELHIVERY_TOKEN}`
                `https://staging-express.delhivery.com/api/v1/packages/json/?waybill=83694710000081&ref_ids=&token=${process.env.DELHIVERY_TOKEN}`
            );

            if (response.data && response.data.success) {
                return res.success(response.data);
            } else {
                console.log('response >>>', response);
                return res.warn({ error: 'Shipment not found or tracking failed' });
            }
        } catch (error) {
            console.error('Error tracking shipment:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async contactUs(req, res) {
        const { name, email, message, type } = req.body;

        try {
            // Save contact form data to MongoDB
            const newContact = new ContactUs({
                name,
                email,
                message,
                type,
            });

            await newContact.save();
            // Prepare email data
            const emailData = {
                name,
                email,
                message,
                type,
            };
            // Send email using the sendMail function
            await sendMail('contact-template', 'New Contact Us Message', email, emailData);

            return res.success({ message: 'Our team will reach you soon, your message sent successfully' });
        } catch (error) {
            console.error('Error sending contact us email:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async dashboard(req, res) {
        try {
            // Initialize the dashboard data with default values
            const dashboardData = {
                ordersCount: 0,
                usersCount: 0,
                productsCount: 0,
                todaysCollection: 0,
                pendingOrders: [],
                cancelOrders: [],
            };

            // Get users count from MongoDB
            dashboardData.usersCount = await User.countDocuments({ isDeleted: false });

            // Get products count from MongoDB
            dashboardData.productsCount = await Product.countDocuments({ isDeleted: false });

            // Get orders count from MongoDB
            dashboardData.ordersCount = await Order.countDocuments({});

            // Get today's collection (sum of all paid orders created today)
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            dashboardData.todaysCollection = await Order.aggregate([
                {
                    $match: {
                        createdAt: { $gte: today },
                        status: 'Delivered',
                    },
                },
                {
                    $group: {
                        _id: null,
                        total: { $sum: '$totalPrice' },
                    },
                },
                {
                    $project: { _id: 0, total: 1 },
                },
            ]).then(res => res[0]?.total || 0);

            // Get pending orders (status is 'Pending')
            dashboardData.pendingOrders = await Order.find({ status: 'Pending' })
                .select('orderId products totalPrice createdAt')
                .populate('user', 'email fullName fName lName')
                .populate('products.productId', 'name mainImage')
                .sort({ createdAt: -1 })
                .limit(5);

            // Get canceled orders (status is 'Cancelled')
            dashboardData.cancelOrders = await Order.find({ status: 'Cancelled' })
                .select('orderId products totalPrice createdAt')
                .populate('user', 'email fullName fName lName')
                .populate('products.productId', 'name mainImage')
                .sort({ createdAt: -1 })
                .limit(5);

            // Respond with the dashboard data
            return res.json(dashboardData);
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    }

   
}

module.exports = new UtilController();
