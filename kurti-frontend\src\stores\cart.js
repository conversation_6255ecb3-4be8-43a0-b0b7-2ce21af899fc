import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

const initialState = {
  cartItems: [],
};

const cartStore = (set) => ({
  ...initialState,
  setCartItems: (data) => set((state) => ({ ...state, cartItems: data })),
});

const useCartStore = create(
  devtools(
    persist(cartStore, {
      name: "cart",
    })
  )
);

export const { setCartItems } = useCartStore.getState();

export default useCartStore;
