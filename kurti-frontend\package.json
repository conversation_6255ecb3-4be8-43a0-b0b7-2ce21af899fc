{"name": "GC Creations", "private": true, "version": "0.0.0", "type": "module", "repository": "https://gitlab.com/RonakSethi/ecommerce-store.git", "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://gajancreation.com", "bugs": {"url": "https://gitlab.com/RonakSethi/ecommerce-store/issues"}, "keywords": ["kurti", "store", "ecommerce", "gajan", "creation"], "readme": "https://gitlab.com/RonakSethi/ecommerce-store/-/blob/main/README.md", "readmeFilename": "README.md", "engines": {"node": "18", "yarn": ">= 1.12.3"}, "main": "src/index.js", "files": ["dist"], "lint-staged": {"src/**/*.{js,json}": ["eslint --fix", "prettier --write", "git add"]}, "scripts": {"start": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fortawesome/fontawesome-free": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^5.16.5", "@mui/material": "^5.16.5", "@tanstack/react-query": "^5.40.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.2", "bootstrap": "^5.0.1", "firebase": "^10.12.3", "formik": "^2.4.6", "html-react-parser": "^5.1.10", "lodash": "^4.17.21", "node-sass": "^9.0.0", "photoswipe": "^5.4.3", "react": "^18.2.0", "react-bootstrap": "^2.10.2", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-infinite-scroll-component": "^6.1.0", "react-inner-image-zoom": "^3.0.2", "react-lazyload": "^3.2.1", "react-loading-overlay": "^1.0.1", "react-medium-image-zoom": "^5.2.4", "react-otp-input": "^3.1.1", "react-photoswipe-gallery": "^3.0.1", "react-router-dom": "^6.23.1", "react-toastify": "^10.0.5", "simplebar-react": "^3.2.6", "swiper": "^11.1.3", "yup": "^1.4.0", "zustand": "^4.5.2", "date-fns": "^4.1.0", "react-icons": "^5.5.0"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "sass": "^1.77.4", "vite": "^5.2.0"}}