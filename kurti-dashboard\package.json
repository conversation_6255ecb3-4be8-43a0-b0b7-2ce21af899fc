{"name": "Gajan Creation Admin", "version": "1.0.0", "type": "module", "private": true, "repository": "https://gitlab.com/RonakSethi/ecommerce-store.git", "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://admin.gajancreation.com", "bugs": {"url": "https://gitlab.com/RonakSethi/ecommerce-store/issues"}, "keywords": ["kurti", "store", "ecommerce", "gajan", "creation", "admin", "dashboard"], "readme": "https://gitlab.com/RonakSethi/ecommerce-store/-/blob/main/README.md", "readmeFilename": "README.md", "engines": {"node": "18", "yarn": ">= 1.12.3"}, "scripts": {"start": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@fullcalendar/core": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@mui/icons-material": "^5.10.3", "@mui/material": "^5.10.5", "@mui/x-data-grid": "^5.17.2", "@mui/x-date-pickers": "^7.13.0", "@nivo/bar": "^0.80.0", "@nivo/core": "^0.79.0", "@nivo/geo": "^0.80.0", "@nivo/line": "^0.79.1", "@nivo/pie": "^0.80.0", "@reduxjs/toolkit": "^1.9.7", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "chart.js": "^3.9.1", "dayjs": "^1.11.13", "firebase": "^10.12.0", "formik": "^2.4.6", "html-react-parser": "^5.1.10", "react": "^18.2.0", "react-chartjs-2": "^4.3.1", "react-dom": "^18.2.0", "react-pro-sidebar": "^0.7.1", "react-quill": "^2.0.0", "react-redux": "^8.1.3", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-toastify": "^10.0.5", "simplebar-react": "^3.2.6", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "sass": "^1.77.2", "vite": "^5.2.0"}}