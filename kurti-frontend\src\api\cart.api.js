import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api/api-client";
import { API_ENDPOINTS } from "globals/endpoints";

export const useCartQuery = (token) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_CART, {
        headers: {
          Authorization: token,
        },
      });
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["cart"],
    enabled: !!token,
  });

export const useUpdateCartMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(
        API_ENDPOINTS.UPDATE_CART,
        payload?.data,
        {
          headers: {
            Authorization: payload?.token,
          },
        }
      );
      return response;
    },
  });

export const useRemoveCartMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.delete(
        `${API_ENDPOINTS.REMOVE_CART}/${payload}`
      );
      return response;
    },
  });
