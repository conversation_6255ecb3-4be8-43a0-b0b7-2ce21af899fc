const {
    models: { Product, User, AdminSettings },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');
const mongoose = require('mongoose');
const { ObjectId } = mongoose.Types;

class ReviewController {
    async addReview(req, res) {
        try {
            const { productId, rating, comment } = req.body;
            const userId = req.user._id;
            const fcmId = req.user.fcmId;

            // Check if reviews are enabled
            const settings = await AdminSettings.findOne({});
            if (!settings.enableReviews) {
                return res.warn('', req.__('REVIEWS_DISABLED'));
            }

            // Validate product exists
            const product = await Product.findById(productId);
            if (!product) {
                return res.warn('', req.__('PRODUCT_NOT_FOUND'));
            }

            // Check if user has already reviewed this product
            const existingReview = product.reviews.find(review =>
                review.userId.toString() === userId.toString()
            );

            if (existingReview) {
                return res.warn('', req.__('ALREADY_REVIEWED'));
            }

            // Add the review
            const newReview = {
                userId,
                fcmId,
                rating: Number(rating),
                comment,
                productId,
                createdAt: utcDateTime(),
            };

            // Use findByIdAndUpdate instead of modifying the product directly
            // This avoids issues with Mongoose validation on nested fields
            const updatedProduct = await Product.findByIdAndUpdate(
                productId,
                {
                    $push: { reviews: newReview },
                    $set: {
                        avgrating: (product.reviews.reduce((sum, review) => sum + review.rating, 0) + Number(rating)) / (product.reviews.length + 1)
                    }
                },
                { new: true, runValidators: false }
            );

            return res.success(newReview, req.__('REVIEW_ADDED'));
        } catch (error) {
            console.error('Error adding review:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateReview(req, res) {
        try {
            const { productId, reviewId, rating, comment } = req.body;
            const userId = req.user._id;
               const fcmId = req.user.fcmId;
            const isAdmin = req.user.role === 'ADMIN' || req.user.role === 'MASTER';
            // Check if reviews are enabled (only for non-admin users)
            if (!isAdmin) {
                const settings = await AdminSettings.findOne({});
                if (!settings.enableReviews) {
                    return res.warn('', req.__('REVIEWS_DISABLED'));
                }
            }

            // Validate product exists
            const product = await Product.findById(productId);
            if (!product) {
                return res.warn('', req.__('PRODUCT_NOT_FOUND'));
            }

            // Find the review
            const reviewIndex = product.reviews.findIndex(review =>
                review._id.toString() === reviewId
            );

            if (reviewIndex === -1) {
                return res.warn('', req.__('REVIEW_NOT_FOUND'));
            }

            // Check if user is authorized to update this review
            if (!isAdmin && product.reviews[reviewIndex].userId.toString() !== userId.toString()) {
                return res.warn('', req.__('UNAUTHORIZED'));
            }

            // Calculate the new average rating
            // First, get the total without the current review
            const oldRating = product.reviews[reviewIndex].rating;
            const totalWithoutCurrent = product.reviews.reduce((sum, review) => sum + review.rating, 0) - oldRating;
            // Then add the new rating and calculate the average
            const newAvgRating = (totalWithoutCurrent + Number(rating)) / product.reviews.length;

            // Use findOneAndUpdate to update a specific review in the array
            const updatedProduct = await Product.findOneAndUpdate(
                {
                    _id: productId,
                    "reviews._id": reviewId
                },
                {
                    $set: {
                        "reviews.$.rating": Number(rating),
                        "reviews.$.comment": comment,
                        avgrating: newAvgRating
                    }
                },
                { new: true, runValidators: false }
            );

            return res.success(product.reviews[reviewIndex], req.__('REVIEW_UPDATED'));
        } catch (error) {
            console.error('Error updating review:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async deleteReview(req, res) {
        try {
            const { productId, reviewId } = req.params;
            const userId = req.user._id;
            const isAdmin = req.user.role === 'ADMIN' || req.user.role === 'MASTER';

            // Validate product exists
            const product = await Product.findById(productId);
            if (!product) {
                return res.warn('', req.__('PRODUCT_NOT_FOUND'));
            }

            // Find the review
            const reviewIndex = product.reviews.findIndex(review =>
                review._id.toString() === reviewId
            );

            if (reviewIndex === -1) {
                return res.warn('', req.__('REVIEW_NOT_FOUND'));
            }

            // Check if user is authorized to delete this review
            if (!isAdmin && product.reviews[reviewIndex].userId.toString() !== userId.toString()) {
                return res.warn('', req.__('UNAUTHORIZED'));
            }

            // Get the review to be deleted
            const reviewToDelete = product.reviews[reviewIndex];

            // Calculate new average rating
            let newAvgRating = 0;
            if (product.reviews.length > 1) {
                // Calculate total without the review to be deleted
                const totalWithoutDeleted = product.reviews.reduce((sum, review) => sum + review.rating, 0) - reviewToDelete.rating;
                newAvgRating = totalWithoutDeleted / (product.reviews.length - 1);
            }

            // Use findByIdAndUpdate to remove the review
            const updatedProduct = await Product.findByIdAndUpdate(
                productId,
                {
                    $pull: { reviews: { _id: reviewId } },
                    $set: { avgrating: newAvgRating }
                },
                { new: true, runValidators: false }
            );

            return res.success({}, req.__('REVIEW_DELETED'));
        } catch (error) {
            console.error('Error deleting review:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getProductReviews(req, res) {
        try {
            const { productId } = req.params;
            const { page = 1, limit = 10 } = req.query;

            // Check if reviews are enabled
            const settings = await AdminSettings.findOne({});
            if (!settings.enableReviews) {
                return res.warn('', req.__('REVIEWS_DISABLED'));
            }

            // Validate product exists
            const product = await Product.findById(productId);
            if (!product) {
                return res.warn('', req.__('PRODUCT_NOT_FOUND'));
            }

            // Sort reviews by date (newest first)
            const sortedReviews = product.reviews.sort((a, b) =>
                new Date(b.createdAt) - new Date(a.createdAt)
            );

            // Paginate reviews
            const startIndex = (page - 1) * limit;
            const endIndex = page * limit;
            const paginatedReviews = sortedReviews.slice(startIndex, endIndex);

            // Get user details for each review
            const reviewsWithUserDetails = await Promise.all(
                paginatedReviews.map(async (review) => {
                    const user = await User.findById(review.userId);
                    return {
                        ...review.toObject(),
                        user: user ? {
                            _id: user._id,
                            fullName: user.fullName || 'Anonymous',
                            avatar: user.avatar || '',
                        } : {
                            fullName: 'Anonymous',
                            avatar: '',
                        },
                    };
                })
            );

            const response = {
                reviews: reviewsWithUserDetails,
                totalReviews: product.reviews.length,
                avgRating: product.avgrating || 0,
            };

            return res.success(response);
        } catch (error) {
            console.error('Error getting product reviews:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getAllReviews(req, res) {
        try {
            const { page = 1, limit = 10, search = '' } = req.query;

            // Check if reviews are enabled (only for non-admin users)
            // Admin users can always see reviews in the admin panel
            if (req.user.role !== 'ADMIN') {
                const settings = await AdminSettings.findOne({});
                if (!settings.enableReviews) {
                    return res.warn('', req.__('REVIEWS_DISABLED'));
                }
            }

            // Aggregate to get all reviews across products
            const aggregationPipeline = [
                { $match: { isDeleted: false } },
                { $unwind: '$reviews' },
                {
                    $lookup: {
                        from: 'users',
                        localField: 'reviews.userId',
                        foreignField: '_id',
                        as: 'userDetails'
                    }
                },
                {
                    $addFields: {
                        'reviews.productName': '$name',
                        'reviews.productId': '$_id',
                        'reviews.user': { $arrayElemAt: ['$userDetails', 0] }
                    }
                },
                {
                    $project: {
                        _id: '$reviews._id',
                        rating: '$reviews.rating',
                        comment: '$reviews.comment',
                        createdAt: '$reviews.createdAt',
                        productId: '$reviews.productId',
                        productName: '$reviews.productName',
                        userId: '$reviews.userId',
                        user: {
                            _id: '$reviews.user._id',
                            fullName: '$reviews.user.fullName',
                            email: '$reviews.user.email',
                            avatar: '$reviews.user.avatar'
                        }
                    }
                }
            ];

            // Add search filter if provided
            if (search) {
                aggregationPipeline.push({
                    $match: {
                        $or: [
                            { 'productName': { $regex: search, $options: 'i' } },
                            { 'comment': { $regex: search, $options: 'i' } },
                            { 'user.fullName': { $regex: search, $options: 'i' } },
                            { 'user.email': { $regex: search, $options: 'i' } }
                        ]
                    }
                });
            }

            // Add sorting and pagination
            aggregationPipeline.push(
                { $sort: { createdAt: -1 } },
                { $skip: (page - 1) * limit },
                { $limit: parseInt(limit) }
            );

            const reviews = await Product.aggregate(aggregationPipeline);

            // Count total reviews for pagination
            const countPipeline = [...aggregationPipeline];
            countPipeline.pop(); // Remove limit
            countPipeline.pop(); // Remove skip
            countPipeline.push({ $count: 'total' });

            const countResult = await Product.aggregate(countPipeline);
            const totalReviews = countResult.length > 0 ? countResult[0].total : 0;

            return res.success({
                reviews,
                totalReviews,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(totalReviews / limit)
            });
        } catch (error) {
            console.error('Error getting all reviews:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new ReviewController();
