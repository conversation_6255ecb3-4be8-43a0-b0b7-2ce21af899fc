import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation } from "swiper/modules";
import LazyLoad from "react-lazyload";
// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Link } from "react-router-dom";
import { useCategoriesQuery } from "api/categories.api";
import { isMobileOnly } from "react-device-detect";
import CustomLoadingOverlay from "./molecules/CustomLoadingOverlay";

const WooBanner = () => {
  const { data: categories = [], isLoading, isFetching } = useCategoriesQuery();

  return (
    <>
      <CustomLoadingOverlay active={isLoading || isFetching} />
      <div className="container mt-5 mb-5 woobanner">
        <div className="row">
          <Swiper
            //slidesPerView={5}
            breakpoints={{
              479: {
                slidesPerView: 1,
                spaceBetween: 15,
              },
              480: {
                slidesPerView: 3,
                spaceBetween: 15,
              },
              720: {
                slidesPerView: 3,
                spaceBetween: 20,
              },
              960: {
                slidesPerView: 4,
                spaceBetween: 20,
              },
              1200: {
                slidesPerView: 5,
                spaceBetween: 20,
              },
            }}
            loop={true}
            pagination={false}
            navigation={!isMobileOnly}
            modules={[Pagination, Navigation]}
            className="swiper-wrapper"
          >
            {categories?.length > 0 &&
              categories.map((category) => {
                return (
                  <div className="col-md-3">
                    <SwiperSlide
                      className="swiper-slide"
                      data-swiper-autoplay="3000"
                      data-anim-in="fadeInUp"
                    >
                      <Link to={`/category?catId=${category?._id}`}>
                        <div className="gc-woo-banner-wrapper">
                          <LazyLoad height={200} once>
                            <img
                              src={category?.image}
                              alt="Lazy-loaded image 1"
                              className="img-fluid"
                            />
                          </LazyLoad>
                          <div className="gc-banner-content">
                            <div className="gc-banner-content-top"></div>
                            <div className="gc-banner-content-center"></div>
                            <div className="gc-banner-content-bottom">
                              <h4 className="gc-banner-title banner-content-item">
                                {category?.name}
                              </h4>
                              <span className="gc-banner-catcount banner-content-item">
                                {category?.description}
                              </span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    </SwiperSlide>
                  </div>
                );
              })}
          </Swiper>
        </div>
      </div>
    </>
  );
};

export default WooBanner;
