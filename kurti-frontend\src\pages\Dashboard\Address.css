/* Address List Styling */
.address-list-container {
    margin-bottom: 30px;
  }
  
  .address-list-header {
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
    padding: 15px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #eee;
  }
  
  .address-list-item {
    padding: 20px;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
  }
  
  .address-list-item:hover {
    background-color: rgba(108,94,188, 0.03);
  }
  
  .address-list-item:last-child {
    border-bottom: none;
    border-radius: 0 0 8px 8px;
  }
  
  .address-type {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
  }
  
  .address-details {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
  }
  
  .address-action-btn {
    background-color: var(--gc-purple, #6c5ebc);
    color: white !important;
    border: none;
    padding: 8px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(108,94,188, 0.2);
    text-decoration: none !important;
    display: inline-block;
    text-align: center;
    min-width: 120px;
  }
  
  .address-action-btn:hover {
    background-color: #6c5ebc;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(108,94,188, 0.3);
    color: white !important;
  }
  
  .address-action-btn:active {
    transform: translateY(0);
  }
  
  .address-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s ease;
  }
  
  .address-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
  }
  
  .address-card-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
  }
  
  .address-card-body {
    padding: 20px;
  }
  
  .address-card-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
  }
  
  /* Add Address Form Styling */
  .add-address-container {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    padding: 30px;
    margin-bottom: 30px;
  }
  
  .add-address-title {
    font-size: 22px;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 12px;
  }
  
  .add-address-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--gc-purple);
    border-radius: 3px;
  }
  
  .add-address-form .form-row {
    margin-bottom: 20px;
  }
  
  .add-address-form label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #555;
    font-size: 14px;
    display: block;
  }
  
  .add-address-form input,
  .add-address-form select,
  .add-address-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
  }
  
  .add-address-form input:focus,
  .add-address-form select:focus,
  .add-address-form textarea:focus {
    border-color: var(--gc-purple);
    box-shadow: 0 0 0 3px rgba(108,94,188, 0.1);
    outline: none;
  }
  
  .add-address-form .btn-submit {
    background-color: var(--gc-purple);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(108,94,188, 0.2);
    margin-top: 10px;
  }
  
  .add-address-form .btn-submit:hover {
    background-color: #6c5ebc;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(242, 82, 82, 0.3);
  }
  
  .add-address-form .btn-submit:active {
    transform: translateY(0);
  }
  
  .add-address-form .error {
    color: #dc3545;
    font-size: 13px;
    margin-top: 5px;
    display: block;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .add-address-container {
      padding: 20px;
    }
  
    .address-card {
      margin-bottom: 15px;
    }
  }
  