import Pagination from "react-bootstrap/Pagination";
import "./styles.scss";

const CustomPagination = ({ totalData, limit, page, setCurrentPage }) => {
  const totalPages = Math.ceil(totalData / limit);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const renderPaginationItems = () => {
    const items = [];
    let startPage = Math.max(1, page - 3);
    let endPage = Math.min(totalPages, startPage + 6);

    if (endPage - startPage < 6) {
      startPage = Math.max(1, endPage - 6);
    }

    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
      items.push(
        <Pagination.Item
          key={pageNum}
          active={pageNum === page}
          onClick={() => handlePageChange(pageNum)}
        >
          {pageNum}
        </Pagination.Item>
      );
    }
    return items;
  };

  return (
    <div className="custom-pagination-container my-5 pt-5">
      <Pagination>
        <Pagination.Prev
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 1}
        >
          <span aria-hidden="true">Prev</span>
        </Pagination.Prev>

        {renderPaginationItems()}

        <Pagination.Next
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages}
        >
          <span aria-hidden="true">Next</span>
        </Pagination.Next>
      </Pagination>
    </div>
  );
};

export default CustomPagination;
