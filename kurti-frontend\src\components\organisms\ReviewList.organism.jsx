import React, { useState, useEffect } from 'react';
import { useProductReviewsQuery, useDeleteReviewMutation } from 'api/review.api';
import StarRating from '../atoms/StarRating';
import { Button, Card, Pagination } from 'react-bootstrap';
import { toast } from 'react-toastify';
import useUserStore from 'stores/user';
import ReviewForm from '../molecules/ReviewForm.molecule';
import { formatDistanceToNow } from 'date-fns';
import { Rating } from '@mui/material';
import { useGetAdminSettingsQuery } from 'api/settings.api';
import { useQueryClient } from '@tanstack/react-query';

const ReviewList = ({ productId, refreshTrigger = 0 }) => {
  const [page, setPage] = useState(1);
  const [editingReviewId, setEditingReviewId] = useState(null);
  const queryClient = useQueryClient();

  // Get admin settings to check if reviews are enabled
  const { data: adminSettings, isError: isSettingsError } = useGetAdminSettingsQuery();
  // Default to false if settings API fails or enableReviews is not explicitly set to true
  // Use strict equality to ensure we're checking for exactly true or false
  const reviewsEnabled = !isSettingsError && adminSettings?.enableReviews === true;

  // Only fetch reviews if reviews are enabled
  const { data: reviewsData, isLoading, refetch } = useProductReviewsQuery(
    productId,
    { page }
  );

  // Set up an effect to listen for changes to the product info or refreshTrigger
  useEffect(() => {
    // Refetch when the component mounts, when productId changes, or when refreshTrigger changes
    if (productId) {
      refetch();
    }
  }, [productId, refreshTrigger, refetch]);

  const { mutateAsync: deleteReview } = useDeleteReviewMutation();
  const userInfo = useUserStore((state) => state.userInfo);
  const userId = userInfo?.user?._id;

  // If reviews are disabled, don't render anything
  if (reviewsEnabled !== true) {
    return null;
  }

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const handleDeleteReview = async (reviewId) => {
    if (window.confirm('Are you sure you want to delete this review?')) {
      try {
        await deleteReview({ productId, reviewId });
        toast.success('Review deleted successfully');
        // Invalidate queries in a more controlled way
        queryClient.invalidateQueries({
          queryKey: ['product-info', productId],
          exact: true
        });
        refetch();
      } catch (error) {
        toast.error(error?.response?.data?.message || 'Failed to delete review');
      }
    }
  };

  const handleEditClick = (reviewId) => {
    setEditingReviewId(reviewId);
  };

  const handleEditSuccess = () => {
    setEditingReviewId(null);
    // Invalidate queries in a more controlled way
    queryClient.invalidateQueries({
      queryKey: ['product-info', productId],
      exact: true
    });
    refetch();
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading reviews...</div>;
  }

  const { reviews = [], totalReviews = 0, avgRating = 0 } = reviewsData || {};
  const totalPages = Math.ceil(totalReviews / 10); // Assuming 10 reviews per page

  if (reviews.length === 0 && page === 1) {
    return (
      <div className="reviews-container">
        <div className="text-center py-4">No reviews yet. Be the first to review this product!</div>
      </div>
    );
  }

  return (
    <div className="reviews-container">
      <div className="reviews-summary mb-4">
        <div className="d-flex align-items-center">
          <h4 className="mb-0 me-3">Customer Reviews</h4>
          <Rating value={avgRating} readOnly precision={0.5} size="small" />
          <span className="ms-2">({totalReviews} reviews)</span>
        </div>
      </div>

      <div className="reviews-list">
        {reviews.map((review) => (
          <Card key={review._id} className="mb-3">
            <Card.Body>
              {editingReviewId === review._id ? (
                <ReviewForm
                  productId={productId}
                  reviewId={review._id}
                  initialRating={review.rating}
                  initialComment={review.comment}
                  onSuccess={handleEditSuccess}
                />
              ) : (
                <>
                  <div className="d-flex justify-content-between align-items-start">
                    <div>
                      <div className="d-flex align-items-center mb-2">
                        <h5 className="mb-0 me-2">{review.user?.fullName || 'Anonymous'}</h5>
                        <Rating value={review.rating} readOnly precision={0.5} size="small" />
                      </div>
                      <p className="text-muted small">
                        {formatDistanceToNow(new Date(review.createdAt), { addSuffix: true })}
                      </p>
                    </div>

                    {userId === review.fcmId && (
                      <div className="review-actions">
                        <Button
                          variant="link"
                          size="sm"
                          onClick={() => handleEditClick(review._id)}
                          className="text-primary"
                        >
                          Edit
                        </Button>
                        <Button
                          variant="link"
                          size="sm"
                          onClick={() => handleDeleteReview(review._id)}
                          className="text-danger"
                        >
                          Delete
                        </Button>
                      </div>
                    )}
                  </div>
                  <p>{review.comment}</p>
                </>
              )}
            </Card.Body>
          </Card>
        ))}
      </div>

      {totalPages > 1 && (
        <div className="d-flex justify-content-center mt-4">
          <Pagination>
            <Pagination.Prev
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
            />

            {[...Array(totalPages)].map((_, index) => (
              <Pagination.Item
                key={index + 1}
                active={index + 1 === page}
                onClick={() => handlePageChange(index + 1)}
              >
                {index + 1}
              </Pagination.Item>
            ))}

            <Pagination.Next
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
            />
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default ReviewList;
