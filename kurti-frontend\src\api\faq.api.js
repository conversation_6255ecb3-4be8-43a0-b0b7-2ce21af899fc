import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./api-client";

export const useFaqsQuery = (params = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_FAQS, {
        params,
      });
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["faqs", params],
  });

export const useFaqCategoriesQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_FAQ_CATEGORIES);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["faq-categories"],
  });

export const useFaqDetailQuery = (id) =>
  useQuery({
    queryFn: async () => {
      if (!id) return null;
      const response = await apiClient.get(`${API_ENDPOINTS.GET_FAQS}/${id}`);
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["faq", id],
    enabled: !!id,
  });
