import React from "react";

import "../styles/contactus.scss";
import { useContactUsMutation, useGeneralSettingsQuery } from "api/util.api";
import { ErrorMessage, useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import GoogleMap from "../molicules/GoogleMap";

const ContactUs = () => {
  const { data: settingsData = {} } = useGeneralSettingsQuery();
  const { mutateAsync: contactUs } = useContactUsMutation();

  const formik = useFormik({
    initialValues: {
      type: "inquiry",
      name: "",
      email: "",
      message: "",
    },
    validationSchema: Yup.object().shape({
      name: Yup.string().required("Name is required"),
      email: Yup.string().required("Email is required"),
      message: Yup.string().required("Message is required"),
    }),
    onSubmit: async (values) => {
      try {
        const result = await contactUs(values);
        if (result?.success) {
          toast.success("Inquiry submitted successfully!");
          formik.resetForm();
        } else {
          toast.error(result?.data?.error)
        }
      } catch (err) {
        console.log(err);
      }
    },
  });

  const renderError = (fieldName) => {
    if (formik.touched[fieldName] && formik.errors[fieldName]) {
      return <span className="error">{formik.errors[fieldName]}</span>;
    }
    return null;
  };
  return (
    <>
      <div className="contact-page">
        <div className="container">
          <div className="row">
            <div className="col-md-6 order-md-2">
              <div className="row">
                <div className="col-lg-6">
                  <div className="address">
                    <h3>Our Showroom</h3>
                    <p>
                      <strong>
                        {settingsData?.address ||
                          "614 morya nagar opposite baba memorial hospital sahi point dindoli surat 394210."}
                      </strong>
                    </p>
                    <p>{settingsData?.contact || "9712227585, 7984544851"}</p>
                  </div>
                </div>
                <div className="col-lg-6">
                  <div className="address">
                    <h3>Quick Help</h3>
                    <p>
                      You can ask anything you want to know about our products
                      or services via this e-mail.
                    </p>
                    <p>{settingsData?.email || "<EMAIL>"}</p>
                  </div>
                </div>
              </div>
              <div className="contact-form">
                <h3>Send a Message</h3>

                <form onSubmit={formik.handleSubmit}>
                  <div class="mb-3">
                    <input
                      type="text"
                      class="form-control"
                      placeholder="Your name"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      name="name"
                    />
                    {renderError("name")}
                  </div>
                  <div class="mb-3">
                    <input
                      type="email"
                      class="form-control"
                      placeholder="Your E-mail"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      name="email"
                    />
                    {renderError("email")}
                  </div>
                  <div class="mb-3">
                    <textarea
                      type="text"
                      class="form-control"
                      placeholder="Message"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      name="message"
                    />
                    {renderError("message")}
                  </div>
                  <div class="mb-3">
                    <input
                      type="submit"
                      value="Submit"
                      className="gc-btn-medium gc-btn gc-btn-black"
                      style={{
                        backgroundColor: "#6c5ebc",
                        color: "white",
                        border: "none"
                      }}
                    />
                  </div>
                </form>
              </div>
            </div>
            <div className="col-md-6 order-md-1"><GoogleMap settingsData={settingsData} /></div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContactUs;
