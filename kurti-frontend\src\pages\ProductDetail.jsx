import { Box, Rating } from "@mui/material";
import { useRemoveCartMutation, useUpdateCartMutation } from "api/cart.api";
import { useProductInfoQuery, useProductsQuery } from "api/product.api";
import { useGetAdminSettingsQuery } from "api/settings.api";
import {
  useRemoveWishlistMutation,
  useUpdateWishlistMutation,
} from "api/wishlist.api";
import CheckPincodeAvailability from "components/atoms/CheckPincodeAvailability";
import CustomLoadingOverlay from "components/molecules/CustomLoadingOverlay";
import ProductColorListMolecule from "components/molecules/ProductColorList.molecule";
import ProductSizeListMolecule from "components/molecules/ProductSizeList.molecule";
import ReviewForm from "components/molecules/ReviewForm.molecule";
import ReviewList from "components/organisms/ReviewList.organism";
import { SETTINGS } from "globals";
import parse from "html-react-parser";
import { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Tab, Tabs } from "react-bootstrap";
import { Helmet } from "react-helmet";
import { Link, useParams } from "react-router-dom";
import useCartStore, { setCartItems } from "stores/cart";
import useUserStore from "stores/user";
import useWishlistStore, { setWishlistItems } from "stores/wishlist";
import { generateRandomNumber, getEstimatedDeliveryDate } from "utils";
import {
  IconCalendar,
  IconDeliverReturn,
  IconSizeGuide,
  IconSmiley,
  IconWishListFilled
} from "utils/icons";
import ProductDetailsSlider from "../components/ProductDetailsSlider";
import ProductSliderWithTitleTemplate from "../components/templates/ProductSliderWithTitle.template";
import { IconWishListEmpty } from "../utils/icons";

const ProductDetail = () => {
  const [quantity, setQuantity] = useState(SETTINGS.PRODUCT_QTY_MIN);
  const [ctaText, setCtaText] = useState(SETTINGS.ADD_TO_CART);
  const [params, setParams] = useState({ page: 1, limit: 20 });
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedColor, setSelectedColor] = useState(null);
  const [isChangingProduct, setIsChangingProduct] = useState(false);
  const [isApiCalling, setIsApiCalling] = useState(false);
  const [showSizeGuide, setShowSizeGuide] = useState(false);
  const [activeTab, setActiveTab] = useState('description');
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [userHasReviewed, setUserHasReviewed] = useState(false);
  const [reviewRefreshTrigger, setReviewRefreshTrigger] = useState(0);

  const handleShowSizeGuide = () => setShowSizeGuide(true);
  const handleCloseSizeGuide = () => setShowSizeGuide(false);

  // Get admin settings to check if reviews are enabled
  const { data: adminSettings, isError: isSettingsError } = useGetAdminSettingsQuery();
  // Default to false if settings API fails or enableReviews is not explicitly set to true
  // Use strict equality to ensure we're checking for exactly true or false
  const reviewsEnabled = !isSettingsError && adminSettings?.enableReviews === true;

  const { id } = useParams();

  const { data: { items: products = [] } = {}, refetch } =
    useProductsQuery(params);

  const {
    data: productInfo = {},
    refetch: infoRefetch,
    isLoading,
    isFetching,
  } = useProductInfoQuery(id);
  const { mutateAsync: updateCart } = useUpdateCartMutation();
  const { mutateAsync: removeCart } = useRemoveCartMutation();
  const { mutateAsync: updateWishlist } = useUpdateWishlistMutation();
  const { mutateAsync: removeWishlist } = useRemoveWishlistMutation();

  const cartItems = useCartStore((state) => state.cartItems);
  const wishlistItems = useWishlistStore((state) => state.wishlistItems);
  const token = useUserStore((state) => state.userInfo.token);

  const productCheckQry = (item, product, selectedColor, selectedSize) => {
    return (
      item?._id === product?._id &&
      item?.selectedColor?.hexCode === selectedColor?.hexCode &&
      item?.selectedSize === selectedSize
    );
  };

  const isProductInCart = useMemo(
    () =>
      cartItems.some((item) =>
        productCheckQry(item, productInfo, selectedColor, selectedSize)
      ),
    [cartItems, productInfo, selectedColor, selectedSize]
  );

  const isProductInWishlist = useMemo(
    () => wishlistItems.some((item) => item._id === productInfo._id),
    [wishlistItems, productInfo]
  );

  useEffect(() => {
    setIsChangingProduct(true);
    window.scrollTo(0, 0);
    infoRefetch().then(() => setIsChangingProduct(false));
  }, [id, infoRefetch]);

  useEffect(() => {
    refetch();
  }, [params, refetch]);

  useEffect(() => {
    if (productInfo && Object.keys(productInfo)?.length) {
      // Only set these values if they haven't been set yet or if they're null
      if (!selectedColor) {
        setSelectedColor(productInfo?.attributes?.color[0]);
      }
      if (!selectedSize) {
        setSelectedSize(productInfo?.stock[0]?.size);
      }

      // Check if the user has already reviewed this product
      if (token && productInfo.reviews && productInfo.reviews.length > 0) {
        const userId = useUserStore.getState().userInfo?.user?._id;
        const hasReviewed = productInfo.reviews.some(review =>
          review.fcmId === userId
        );
        setUserHasReviewed(hasReviewed);
      } else {
        setUserHasReviewed(false);
      }
    }
  }, [productInfo, token, selectedColor, selectedSize]);

  const productPrices = useMemo(() => {
    if (productInfo?.stock?.length) {
      return productInfo.stock.find((item) => item.size === selectedSize);
    }
  }, [productInfo, selectedSize]);

  const productImages = useMemo(() => {
    if (productInfo?.attributes?.color?.length && selectedColor?.hexCode) {
      return productInfo.attributes.color.find(
        (item) => item.hexCode === selectedColor.hexCode
      )?.images;
    }
  }, [productInfo, selectedColor]);

  const availableSizes = useMemo(() => {
    if (productInfo?.stock?.length) {
      return productInfo.stock.map((item) => item.size);
    }
  }, [productInfo]);

  const getAttributeValues = (data) => {
    if (Array.isArray(data)) {
      const isItemObject = data.some((item) => typeof item === "object");
      if (isItemObject) {
        return data.map((item) => item.name).join(", ");
      }
      return data.join(", ");
    }
    return data;
  };

  const handleUpdateQuantity = (type) => {
    if (isProductInCart) return;
    if (type === "increment" && quantity <= SETTINGS.PRODUCT_QTY_MAX - 1) {
      setQuantity(quantity + 1);
    } else if (type === "decrement" && quantity > SETTINGS.PRODUCT_QTY_MIN) {
      setQuantity(quantity - 1);
    }
  };

  const handleAddToCart = () => {
    setIsApiCalling(true);
    if (isProductInCart) {
      setCartItems(cartItems.filter((item) => item._id !== productInfo._id));
      removeCart(productInfo?._id);
      setIsApiCalling(false);
      return;
    }
    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productImages?.mainImage,
      price: productPrices?.sellingPrice,
      originalPrice: productPrices?.unitPrice,
      discount: productInfo.prices.b2cPrice.discount,
      quantity,
      selectedSize,
      selectedColor,
    };
    if (token) {
      updateCart({
        data: {
          productId: productInfo._id,
          quantity,
          selectedSize,
          selectedColor,
        },
        token,
      });
    }
    setCartItems([...cartItems, itemInfo]);
    setQuantity(1);
    setCtaText(SETTINGS.ADDED_TO_CART);

    setTimeout(() => {
      setCtaText(SETTINGS.ADD_TO_CART);
      setIsApiCalling(false);
    }, 2000);
  };

  const handleAddToWishlist = () => {
    if (isProductInWishlist) {
      setWishlistItems(
        wishlistItems.filter((item) => item._id !== productInfo._id)
      );
      removeWishlist(productInfo?._id);
      return;
    }
    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productImages?.mainImage,
      prices: productInfo?.prices,
    };
    if (token) {
      updateWishlist({
        data: {
          productId: productInfo._id,
        },
        token,
      });
    }
    setWishlistItems([...wishlistItems, itemInfo]);
  };

  const handleSelectColor = (color) => {
    setSelectedColor(color);
  };

  const handleSelectSize = (size) => {
    setSelectedSize(size);
  };

  const handleClearSelection = () => {
    setSelectedSize(null);
  };

  return (
    <>
      <CustomLoadingOverlay
        active={isLoading || isFetching || isChangingProduct}
      />
      {productInfo.seo && (
        <Helmet>
          <title>{import.meta.env.VITE_DEFAULT_TITLE}</title>
          <meta
            name="description"
            content={productInfo.seo.metaDescription || "Default Description"}
          />
          <meta
            name="keywords"
            content={productInfo.seo.keywords || "default, keywords"}
          />
        </Helmet>
      )}
      <div className="nt-gc-inner-container pt-30">
        <div className="container">
          {/* <div className="row">
            <div className="col-12">
              <div className="gc-product-top-nav gc-flex gc-align-center">
                <nav className="woocommerce-breadcrumb" aria-label="Breadcrumb">
                  <a href="/">Home</a>&nbsp;/&nbsp;
                  <a href="#">Kids</a>
                  &nbsp;/&nbsp;
                  <a href="#">Lehenga Set</a>
                  &nbsp;/&nbsp; Tiber Taber
                </nav>
              </div>
            </div>
          </div> */}
          <div className="row">
            <div className="col-lg-12 shop-has-sidebar gc-sidebar-right-sidebar order-1">
              <div className="content-container">
                <div className="row gc-row-summary">
                  <div className="col-12 col-lg-5 gallery-col gc-sticky pid-gallery">
                    <ProductDetailsSlider
                      images={
                        [
                          productImages?.mainImage,
                          ...(productImages?.images ?? []),
                        ] ?? []
                      }
                    />
                  </div>

                  <div className="col-12 col-lg-7 summary-col px-lg-4 mt-4 mt-lg-0">
                    <div className="gc-product-summary">
                      <div className="gc-product-summary-inner">
                        <h1 className="gc-summary-item gc-product-title">
                          {productInfo?.name}
                        </h1>
                        <div className="gc-summary-item gc-price price">
                          <span className="gc-primary-color del">
                            <span className="woocommerce-Price-currencySymbol">
                              {parse(SETTINGS.CURRENCY)}
                            </span>
                            {productPrices?.unitPrice}
                          </span>{" "}
                          –
                          <span className="gc-primary-color ins">
                            <span className="woocommerce-Price-currencySymbol">
                              {parse(SETTINGS.CURRENCY)}
                            </span>
                            {productPrices?.sellingPrice}
                          </span>
                        </div>
                        <div className="gc-summary-item product-details__short-description">
                          {productInfo?.description}
                        </div>
                        <div className="gc-variations variations">
                          <div className="gc-select-variations-terms-title">
                            Selected Features
                          </div>
                          <div className="gc-selected-variations-terms">
                            <span className="selected-features">
                              color: {selectedColor?.name}
                            </span>
                            &nbsp;&nbsp;&nbsp;
                            <span className="selected-features">
                              size: {selectedSize}
                            </span>
                          </div>
                          <div className="gc-variations-items variations-items">
                            <div className="gc-flex gc-align-center">
                              <span className="gc-small-title">color</span>
                              <div className="gc-flex value">
                                <ProductColorListMolecule
                                  colors={productInfo?.attributes?.color || []}
                                  selectedColor={selectedColor}
                                  onSelectColor={handleSelectColor}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="gc-variations-items variations-items">
                            <div className="gc-flex gc-align-center">
                              <span className="gc-small-title">size</span>
                              <div className="gc-flex value">
                                <ProductSizeListMolecule
                                  size={productInfo?.sizeType || []}
                                  selectedSize={selectedSize}
                                  onSelect={handleSelectSize}
                                  availableSizes={availableSizes}
                                />

                                {/* <button
                                  className="gc-btn-reset reset_variations active"
                                  onClick={handleClearSelection}
                                >
                                  Clear
                                </button> */}
                                {/* <a class="gc-btn-reset reset_variations active" href="#">Clear</a> */}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="gc-summary-item gc-flex cart">
                          {!isProductInCart && (
                            <div className="quantity type-number">
                              <label
                                className="screen-reader-text"
                                for="quantity_66496fb0bda2f"
                              >
                                Tiber Taber quantity
                              </label>
                              <div
                                className="quantity-button minus"
                                onClick={() =>
                                  handleUpdateQuantity("decrement")
                                }
                              ></div>
                              <input
                                type=""
                                id="quantity_66496fb0bda2f"
                                className="input-text qty text"
                                name="quantity"
                                value={quantity}
                                aria-label="Product quantity"
                                size="4"
                                inputmode="numeric"
                                autocomplete="off"
                                readOnly
                              />
                              <div
                                className="quantity-button plus"
                                onClick={() =>
                                  handleUpdateQuantity("increment")
                                }
                              >
                                +
                              </div>
                            </div>
                          )}

                          <button
                            type="submit"
                            className="gc-btn gc-btn-medium gc-bg-primary single_add_to_cart_button"
                            onClick={handleAddToCart}
                            // disabled={ctaText !== SETTINGS.ADD_TO_CART}
                            disabled={isApiCalling}
                          >
                            {isApiCalling
                              ? "Loading..."
                              : isProductInCart
                              ? SETTINGS.REMOVE_FROM_CART
                              : ctaText}
                          </button>

                          <Box
                            className="gc-wishlist-btn gc-product-button"
                            onClick={handleAddToWishlist}
                          >
                            {isProductInWishlist ? (
                              <IconWishListFilled className="svgwishlist gc-svg-icon" />
                            ) : (
                              <IconWishListEmpty />
                            )}
                          </Box>
                        </div>

                        <div className="pid-description gc-summary-item">
                          <Tabs
                            activeKey={activeTab}
                            onSelect={(k) => setActiveTab(k)}
                            className="mb-4"
                          >
                            <Tab eventKey="description" title="Description">
                              <div className="tab-content py-3">
                                <h4 className="title">Product Details</h4>
                                <p>{productInfo?.additionalInformation}</p>
                              </div>
                            </Tab>
                            <Tab eventKey="additional" title="Additional Information">
                              <div className="tab-content py-3">
                                <table className="shop_attributes">
                                  <tbody>
                                    {productInfo?.attributes &&
                                      Object.keys(productInfo?.attributes).map(
                                        (item, idx) => {
                                          return (
                                            <>
                                              {(
                                                Array.isArray(
                                                  productInfo?.attributes[item]
                                                )
                                                  ? productInfo?.attributes[
                                                      item
                                                    ]?.length
                                                  : productInfo?.attributes[
                                                      item
                                                    ]
                                              ) ? (
                                                <tr key={idx}>
                                                  <th>{item}</th>
                                                  <td>
                                                    {getAttributeValues(
                                                      productInfo?.attributes[
                                                        item
                                                      ]
                                                    )}
                                                  </td>
                                                </tr>
                                              ) : (
                                                <></>
                                              )}
                                            </>
                                          );
                                        }
                                      )}
                                  </tbody>
                                </table>
                              </div>
                            </Tab>
                            {/* Only render the Reviews tab if reviews are enabled in admin settings */}
                            {reviewsEnabled === true && (
                              <Tab
                                eventKey="reviews"
                                title={
                                  <div className="d-flex align-items-center">
                                    <span>Reviews</span>
                                    {productInfo?.avgrating > 0 && (
                                      <div className="ms-2 d-flex align-items-center">
                                        <Rating value={productInfo?.avgrating || 0} readOnly precision={0.5} size="small" />
                                        <span className="ms-1">({productInfo?.reviews?.length || 0})</span>
                                      </div>
                                    )}
                                  </div>
                                }
                              >
                                <div className="tab-content py-3">
                                  {token ? (
                                    <>
                                      {!showReviewForm ? (
                                        !userHasReviewed ? (
                                          <Button
                                            className="gc-btn gc-btn-medium gc-bg-primary mb-4"
                                            onClick={() => setShowReviewForm(true)}
                                          >
                                            Write a Review
                                          </Button>
                                        ) : (
                                          <div className="alert alert-success mb-4">
                                            Thank you for reviewing this product!
                                          </div>
                                        )
                                      ) : (
                                        <div className="mb-4">
                                          <ReviewForm
                                            productId={productInfo?._id}
                                            onSuccess={() => {
                                              setShowReviewForm(false);
                                              setUserHasReviewed(true);
                                              // Increment the refresh trigger to force the ReviewList to update
                                              setReviewRefreshTrigger(prev => prev + 1);
                                            }}
                                          />
                                          <Button
                                            variant="link"
                                            className="mt-2"
                                            onClick={() => setShowReviewForm(false)}
                                          >
                                            Cancel
                                          </Button>
                                        </div>
                                      )}
                                    </>
                                  ) : (
                                    <div className="alert alert-info mb-4">
                                      Please <Link to="/sign-in">log in</Link> to write a review.
                                    </div>
                                  )}

                                  <ReviewList
                                    productId={productInfo?._id}
                                    refreshTrigger={reviewRefreshTrigger}
                                  />
                                </div>
                              </Tab>
                            )}
                          </Tabs>
                        </div>

                        <div className="gc-summary-item gc-product-popup-details">
                          <CheckPincodeAvailability />
                          <div className="gc-product-delivery-btn has-svg-icon gc-flex gc-align-center">
                            <IconDeliverReturn />
                            <Link to="/return-policy" className="gc-open-popup">
                              Delivery &amp; Return
                            </Link>
                          </div>
                          <div
                            className="gc-product-question-btn has-svg-icon gc-flex gc-align-center cursor-pointer"
                            onClick={handleShowSizeGuide}
                          >
                            <IconSizeGuide />
                            <span>Size Guide</span>
                          </div>

                          <div className="gc-estimated-delivery gc-align-center">
                            <IconCalendar />
                            <span>Estimated Delivery&nbsp;</span>
                            {getEstimatedDeliveryDate()}{" "}
                          </div>
                          <div className="gc-product-view inited gc-align-center">
                            <IconSmiley />
                            <span>
                              <span className="gc-view-count">
                                {" "}
                                {generateRandomNumber()}
                              </span>{" "}
                              people&nbsp;are viewing this right now
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="nt-gc-inner-container pt-30">
          <ProductSliderWithTitleTemplate
            title="Related Products"
            products={
              products?.length
                ? products.filter(
                    (productItem) => productItem._id !== productInfo?._id
                  )
                : []
            }
            sideLink=""
            sideLinkText=""
          />
        </div>
      </div>
      {showSizeGuide && (
        <SizeGuideModal
          productInfo={productInfo}
          show={showSizeGuide}
          handleClose={handleCloseSizeGuide}
        />
      )}
    </>
  );
};

const SizeGuideModal = ({ show, handleClose, productInfo }) => {
  return (
    <>
      <Modal show={show} onHide={handleClose} centered>
        <Modal.Header closeButton>
          <Modal.Title>Size Guide</Modal.Title>
        </Modal.Header>
        <Modal.Body className="text-center">
          <img
            src={productInfo?.categoryId?.sizeChart}
            alt="Size Guide"
            className="img-fluid"
          />
        </Modal.Body>
      </Modal>
    </>
  );
};

export default ProductDetail;
