import { useFaqCategoriesQuery, useFaqsQuery } from "api/faq.api";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Col, Container, Row } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch } from "@fortawesome/free-solid-svg-icons";
import "../styles/faq.scss";

const Faq = () => {
  const [activeCategory, setActiveCategory] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [params, setParams] = useState({});

  const { data: faqData } = useFaqsQuery(params);
  const { data: categories = [] } = useFaqCategoriesQuery();

  const faqs = faqData?.items || [];
  const allCategories = ["All", ...categories];

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    const newParams = {};

    if (activeCategory !== "All") {
      newParams.category = activeCategory;
    }

    if (searchTerm) {
      newParams.search = searchTerm;
    }

    setParams(newParams);
  }, [activeCategory, searchTerm]);

  const handleSearch = (e) => {
    e.preventDefault();
    setSearchTerm(e.target.elements.search.value);
  };

  const handleCategoryClick = (category) => {
    setActiveCategory(category);
  };

  return (
    <div className="faq-page py-5">
      <Container>
        <div className="text-center">
          <h1 className="text-center">Frequently Asked Questions</h1>
        </div>

        {/* Search Bar */}
        <div className="search-container">
          <form onSubmit={handleSearch}>
            <div className="input-group">
              <input
                type="text"
                className="form-control"
                placeholder="Search FAQs..."
                name="search"
              />
              <Button type="submit" className="search-btn">
                <FontAwesomeIcon icon={faSearch} className="me-2" />
                Search
              </Button>
            </div>
          </form>
        </div>

        <Row>
          {/* Category Sidebar */}
          <Col md={3}>
            <div className="category-sidebar">
              <h4>Categories</h4>
              <ul className="category-list">
                {allCategories.map((category) => (
                  <li
                    key={category}
                    className={activeCategory === category ? "active" : ""}
                    onClick={() => handleCategoryClick(category)}
                  >
                    {category}
                  </li>
                ))}
              </ul>
            </div>
          </Col>

          {/* FAQ Accordion */}
          <Col md={9}>
            {faqs.length > 0 ? (
              <Accordion defaultActiveKey="0" className="faq-accordion">
                {faqs.map((faq, index) => (
                  <Accordion.Item key={faq._id} eventKey={index.toString()}>
                    <Accordion.Header>{faq.question}</Accordion.Header>
                    <Accordion.Body>{faq.answer}</Accordion.Body>
                  </Accordion.Item>
                ))}
              </Accordion>
            ) : (
              <div className="no-faqs-found">
                <p>No FAQs found. Please try a different search or category.</p>
              </div>
            )}
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default Faq;
