export const IconLogout = ({ className }) => (
  <svg
    className={className}
    width="100"
    height="100"
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M49.9977 6.25C31.3424 6.25 15.4168 17.9305 9.12637 34.375H15.8918C17.727 30.3742 20.2779 26.6895 23.4832 23.484C30.5658 16.401 39.9822 12.5 49.9977 12.5C60.0145 12.5 69.432 16.401 76.5154 23.4842C83.599 30.5674 87.5 39.9842 87.5 50C87.5 60.0166 83.599 69.4338 76.5158 76.5166C69.4324 83.5994 60.0146 87.5 49.9977 87.5C39.9818 87.5 30.5654 83.5994 23.483 76.5168C20.2775 73.3113 17.7266 69.6262 15.891 65.625H9.12598C15.4162 82.0705 31.342 93.75 49.9979 93.75C74.1607 93.75 93.75 74.1631 93.75 50C93.75 25.8395 74.1607 6.25 49.9977 6.25Z"
      fill="#1B1B1B"
    />
    <path
      d="M40.4029 63.2584L44.8223 67.6777L62.5 50L44.8223 32.3223L40.4027 36.7418L50.5361 46.875H6.25V53.125H50.5361L40.4029 63.2584Z"
      fill="#1B1B1B"
    />
  </svg>
);
export const IconLogin = ({ className }) => (
  <svg
    className={className}
    width="100"
    height="100"
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M50.0023 93.75C68.6576 93.75 84.5832 82.0695 90.8736 65.625L84.1082 65.625C82.273 69.6258 79.7221 73.3105 76.5168 76.516C69.4342 83.599 60.0178 87.5 50.0023 87.5C39.9855 87.5 30.568 83.599 23.4846 76.5158C16.401 69.4326 12.5 60.0158 12.5 50C12.5 39.9834 16.401 30.5662 23.4842 23.4834C30.5676 16.4006 39.9853 12.5 50.0023 12.5C60.0182 12.5 69.4346 16.4006 76.517 23.4832C79.7225 26.6887 82.2734 30.3738 84.109 34.375L90.874 34.375C84.5838 17.9295 68.658 6.25 50.0021 6.25C25.8392 6.25001 6.24999 25.8369 6.25 50C6.25 74.1606 25.8393 93.75 50.0023 93.75Z"
      fill="#1B1B1B"
    />
    <path
      d="M59.5971 36.7416L55.1777 32.3223L37.5 50L55.1777 67.6777L59.5973 63.2582L49.4639 53.125L93.75 53.125L93.75 46.875L49.4639 46.875L59.5971 36.7416Z"
      fill="#1B1B1B"
    />
  </svg>
);
export const IconCart = ({ className = "svgaddtocart gc-svg-icon" }) => (
  <svg
    className={className}
    width="512"
    height="512"
    fill="currentColor"
    viewBox="0 0 32 32"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="m26 8.9a1 1 0 0 0 -1-.9h-3a6 6 0 0 0 -12 0h-3a1 1 0 0 0 -1 .9l-1.78 17.8a3 3 0 0 0 .78 2.3 3 3 0 0 0 2.22 1h17.57a3 3 0 0 0 2.21-1 3 3 0 0 0 .77-2.31zm-10-4.9a4 4 0 0 1 4 4h-8a4 4 0 0 1 4-4zm9.53 23.67a1 1 0 0 1 -.74.33h-17.58a1 1 0 0 1 -.74-.33 1 1 0 0 1 -.26-.77l1.7-16.9h2.09v3a1 1 0 0 0 2 0v-3h8v3a1 1 0 0 0 2 0v-3h2.09l1.7 16.9a1 1 0 0 1 -.26.77z"></path>
  </svg>
);
export const IconSpecialCampagin = ({ className = "" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    height="360pt"
    viewBox="-3 0 360 360.13443"
    width="360pt"
  >
    <path d="m7.066406 159.609375h15.222656v162.058594c.023438 21.234375 17.234376 38.441406 38.46875 38.464843h232.621094c21.234375-.023437 38.441406-17.230468 38.464844-38.464843v-162.058594h15.222656c3.867188 0 7-3.132813 7-7v-43.914063c-.023437-21.234374-17.230468-38.445312-38.464844-38.46875h-37.863281l7.78125-7.988281c14.238281-14.238281 14.238281-37.320312 0-51.558593-14.238281-14.238282-37.320312-14.238282-51.558593 0l-56.035157 56.03125-41.632812-41.632813c-13.117188-13.117187-33.992188-14.296875-48.5-2.734375-14.507813 11.558594-18.019531 32.171875-8.160157 47.882812h-41.101562c-21.234375.027344-38.4414062 17.234376-38.4648438 38.46875v43.914063c0 3.867187 3.1367188 7 6.9999998 7zm310.777344 162.058594c-.015625 13.507812-10.960938 24.453125-24.464844 24.464843h-232.621094c-13.507812-.011718-24.453124-10.957031-24.46875-24.464843v-162.058594h281.554688zm-140.746094-237.441407c.066406 0 .136719.011719.199219.011719s.136719-.007812.199219-.011719h33.570312v61.382813h-66v-61.382813zm162.96875 24.46875v36.914063h-115v-61.382813h90.535156c13.503907.019532 24.449219 10.960938 24.464844 24.46875zm-96.207031-88.117187c8.789063-8.753906 23.007813-8.726563 31.761719.0625s8.726562 23.011719-.0625 31.765625l-17.363282 17.820312h-63.984374zm-149.230469 46.160156c-8.769531-8.769531-8.769531-22.992187 0-31.761719 8.773438-8.769531 22.992188-8.769531 31.761719 0l35.25 35.25h-63.519531zm-80.5625 41.957031c.019532-13.507812 10.960938-24.449218 24.46875-24.46875h92.53125v61.382813h-117zm0 0"></path>
    <path d="m106.539062 272.984375-1.183593 23.757813c-.164063 3.289062 1.984375 6.246093 5.160156 7.105468l22.960937 6.222656 13.007813 19.914063c1.800781 2.757813 5.277344 3.886719 8.355469 2.714844l22.226562-8.472657 22.230469 8.46875c3.074219 1.171876 6.554687.042969 8.351563-2.714843l13.007812-19.910157 22.960938-6.21875c3.179687-.859374 5.328124-3.816406 5.164062-7.105468l-1.1875-23.757813 14.925781-18.523437c2.0625-2.5625 2.0625-6.21875 0-8.785156l-14.925781-18.523438 1.1875-23.757812c.164062-3.285157-1.984375-6.246094-5.164062-7.105469l-22.960938-6.214844-13.007812-19.917969c-1.796876-2.753906-5.277344-3.886718-8.351563-2.710937l-22.230469 8.460937-22.226562-8.46875c-3.078125-1.171875-6.554688-.042968-8.355469 2.714844l-13.007813 19.917969-22.960937 6.214843c-3.175781.859376-5.324219 3.816407-5.160156 7.105469l1.183593 23.757813-14.921874 18.523437c-2.066407 2.5625-2.066407 6.222657 0 8.785157zm12.574219-39.125c1.078125-1.335937 1.628907-3.023437 1.542969-4.742187l-1.039062-20.789063 20.09375-5.4375c1.65625-.449219 3.09375-1.492187 4.03125-2.929687l11.382812-17.429688 19.449219 7.414062c1.605469.609376 3.378906.609376 4.984375 0l19.453125-7.414062 11.378906 17.429688c.941406 1.4375 2.375 2.480468 4.03125 2.929687l20.09375 5.4375-1.035156 20.789063c-.085938 1.71875.460937 3.40625 1.539062 4.742187l13.058594 16.210937-13.058594 16.207032c-1.078125 1.339844-1.625 3.023437-1.539062 4.742187l1.035156 20.789063-20.09375 5.4375c-1.65625.449218-3.089844 1.492187-4.03125 2.929687l-11.378906 17.429688-19.453125-7.410157c-1.605469-.613281-3.378906-.613281-4.984375 0l-19.449219 7.410157-11.382812-17.429688c-.9375-1.4375-2.375-2.480469-4.03125-2.929687l-20.09375-5.4375 1.039062-20.789063c.085938-1.71875-.464844-3.402343-1.542969-4.742187l-13.058593-16.207032zm0 0"></path>
    <path d="m152.878906 243.875c11.457032 0 20.742188-9.289062 20.742188-20.746094 0-11.457031-9.285156-20.742187-20.742188-20.742187-11.457031 0-20.746094 9.289062-20.746094 20.742187.015626 11.453125 9.292969 20.734375 20.746094 20.746094zm0-27.488281c3.726563 0 6.742188 3.019531 6.742188 6.746093 0 3.722657-3.019532 6.742188-6.742188 6.742188-3.726562 0-6.746094-3.019531-6.746094-6.746094.003907-3.722656 3.023438-6.738281 6.746094-6.742187zm0 0"></path>
    <path d="m179.414062 278.105469c0 11.457031 9.285157 20.742187 20.742188 20.742187s20.746094-9.285156 20.746094-20.742187-9.289063-20.746094-20.746094-20.742188c-11.453125.011719-20.730469 9.289063-20.742188 20.742188zm27.484376 0c0 3.722656-3.019532 6.742187-6.742188 6.742187-3.726562 0-6.742188-3.019531-6.742188-6.742187 0-3.726563 3.015626-6.742188 6.742188-6.742188 3.722656.003907 6.738281 3.019531 6.742188 6.742188zm0 0"></path>
    <path d="m152.3125 288.023438c3.242188 2.109374 7.578125 1.199218 9.691406-2.039063l44.015625-67.496094c1.398438-2.09375 1.566407-4.78125.433594-7.03125-1.128906-2.25-3.382813-3.722656-5.902344-3.847656-2.515625-.128906-4.90625 1.105469-6.257812 3.230469l-44.019531 67.496094c-2.109376 3.238281-1.195313 7.574218 2.039062 9.6875zm0 0"></path>
  </svg>
);
export const IconCustomerService = ({ className = "" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    height="486pt"
    viewBox="-32 0 486 486.03473"
    width="486pt"
  >
    <path d="m421.273438 283.546875c0-50.683594-42.28125-91.917969-94.257813-91.917969-40.605469 0-75.296875 25.171875-88.527344 60.351563-22.351562-10.542969-46.757812-16.003907-71.472656-15.980469-45.082031 0-87.039063 17.511719-118.140625 49.304688-31.503906 32.210937-48.859375 76.28125-48.859375 124.101562 0 3.867188 3.136719 7 7 7h253.949219v62.628906c0 2.78125 1.648437 5.300782 4.199218 6.414063 2.550782 1.113281 5.515626.613281 7.558594-1.277344l54.292969-50.292969 54.300781 50.292969c2.042969 1.890625 5.011719 2.390625 7.558594 1.277344 2.550781-1.113281 4.199219-3.632813 4.199219-6.414063v-129.984375c17.964843-17.058593 28.160156-40.730469 28.199219-65.503906zm-94.257813-77.917969c44.253906 0 80.257813 34.953125 80.257813 77.917969-.050782 21.277344-8.953126 41.574219-24.566407 56.027344-.953125.527343-1.773437 1.265625-2.394531 2.160156-30.660156 26.304687-75.925781 26.304687-106.589844 0-.617187-.894531-1.4375-1.636719-2.394531-2.160156-15.613281-14.453125-24.515625-34.75-24.566406-56.027344 0-42.964844 36-77.917969 80.253906-77.917969zm-312.863281 196.773438c1.59375-41.414063 17.335937-79.300782 44.730468-107.308594 28.449219-29.078125 66.847657-45.09375 108.132813-45.09375 23.417969-.03125 46.527344 5.3125 67.550781 15.625-6.199218 30.5 3.78125 62.042969 26.398438 83.425781v53.351563zm364.917968 60.597656-47.300781-43.808594c-2.683593-2.484375-6.828125-2.484375-9.511719 0l-47.296874 43.808594v-102.863281c31.691406 20.433593 72.410156 20.433593 104.101562 0zm0 0"></path>
    <path d="m274.847656 275.808594 25.5 18.035156-9.71875 29.097656c-.957031 2.871094.035156 6.03125 2.464844 7.835938 2.429688 1.804687 5.746094 1.84375 8.214844.097656l25.710937-18.175781 25.707031 18.175781c2.472657 1.746094 5.785157 1.707031 8.214844-.097656 2.429688-1.804688 3.425782-4.964844 2.46875-7.835938l-9.71875-29.097656 25.5-18.035156c2.488282-1.761719 3.554688-4.929688 2.628906-7.835938-.921874-2.90625-3.621093-4.878906-6.671874-4.878906h-31.726563l-9.765625-29.25c-.957031-2.859375-3.628906-4.785156-6.640625-4.785156s-5.6875 1.925781-6.640625 4.785156l-9.765625 29.25h-31.726563c-3.050781 0-5.75 1.972656-6.671874 4.878906-.921876 2.90625.140624 6.074219 2.628906 7.835938zm40.8125 1.285156c3.011719 0 5.683594-1.925781 6.636719-4.785156l4.726563-14.148438 4.722656 14.15625c.953125 2.855469 3.628906 4.78125 6.640625 4.78125h14.746093l-11.769531 8.316406c-2.519531 1.78125-3.578125 5.003907-2.601562 7.933594l4.601562 13.765625-12.300781-8.699219c-2.421875-1.710937-5.660156-1.710937-8.082031 0l-12.300781 8.699219 4.601562-13.765625c.976562-2.925781-.078125-6.148437-2.601562-7.933594l-11.761719-8.316406zm0 0"></path>
    <path d="m64.015625 103c0 56.886719 46.117187 103 103 103 56.886719 0 103-46.113281 103-103s-46.113281-103-103-103c-56.855469.0664062-102.933594 46.140625-103 103zm192 0c0 49.152344-39.84375 89-89 89-49.152344 0-89-39.847656-89-89s39.847656-89 89-89c49.132813.054688 88.945313 39.871094 89 89zm0 0"></path>
  </svg>
);

export const IconQuickView = ({ className = "quickview gc-svg-icon" }) => (
  <svg
    className={className}
    width="512"
    height="512"
    fill="currentColor"
    viewBox="0 0 32 32"
  >
    <path d="m29.91 15.59c-.17-.39-4.37-9.59-13.91-9.59s-13.74 9.2-13.91 9.59a1 1 0 0 0 0 .82c.17.39 4.37 9.59 13.91 9.59s13.74-9.2 13.91-9.59a1 1 0 0 0 0-.82zm-13.91 8.41c-7.17 0-11-6.32-11.88-8 .88-1.68 4.71-8 11.88-8s11 6.32 11.88 8c-.88 1.68-4.71 8-11.88 8z"></path>
    <path d="m16 10a6 6 0 1 0 6 6 6 6 0 0 0 -6-6zm0 10a4 4 0 1 1 4-4 4 4 0 0 1 -4 4z"></path>
  </svg>
);
export const IconRightArrow = ({ className = "svgRight" }) => (
  <svg
    className={className}
    width="512"
    height="512"
    fill="currentColor"
    viewBox="0 0 512 512"
  >
    <g>
      <path
        clip-rule="evenodd"
        d="m463.966 235.526-189.753-189.753c-4.16-4.16-10.923-4.16-15.083 0s-4.16 10.923 0 15.083l174.315 174.314h-380.117c-5.888 0-10.667 4.779-10.667 10.667 0 5.909 4.779 10.667 10.667 10.667h379.072l-173.269 173.269c-4.16 4.16-4.16 10.923 0 15.083 4.181 4.181 10.901 4.16 15.083 0l189.76-189.739c.571-.543 4.757-4.66 4.246-10.897-.404-4.919-3.465-7.956-4.254-8.694z"
        fill-rule="evenodd"
      ></path>
    </g>
  </svg>
);
export const IconSecurePayment = ({ className = "" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    height="420pt"
    viewBox="-15 0 420 420.95493"
    width="420pt"
  >
    <path d="m319.449219 256.25-23.621094-133.339844c-2.085937-13.445312-13.691406-23.34375-27.296875-23.289062h-25.820312v-24.386719c0-41.550781-33.683594-75.234375-75.234376-75.234375-41.550781 0-75.234374 33.683594-75.234374 75.234375v24.386719h-25.816407c-13.605469-.058594-25.210937 9.84375-27.300781 23.289062l-38.539062 217.578125c-.074219.40625-.109376.8125-.109376 1.222657.03125 26.65625 21.632813 48.257812 48.292969 48.289062h194.167969c23.230469 28.929688 62.867188 38.871094 96.996094 24.332031 34.132812-14.539062 54.425781-50.011719 49.65625-86.804687-4.765625-36.789063-33.429688-65.917969-70.140625-71.277344zm-213.207031-181.015625c0-33.820313 27.417968-61.234375 61.234374-61.234375 33.820313 0 61.234376 27.414062 61.234376 61.234375v24.386719h-122.46875zm-91.757813 267.0625 38.4375-217.011719c.007813-.042968.015625-.089844.023437-.132812 1.015626-6.652344 6.75-11.558594 13.480469-11.53125h25.816407v38.035156c0 3.867188 3.136718 7 7 7 3.867187 0 7-3.132812 7-7v-38.035156h122.46875v38.035156c0 3.867188 3.132812 7 7 7 3.867187 0 7-3.132812 7-7v-38.035156h25.820312c6.726562-.027344 12.464844 4.878906 13.476562 11.53125.007813.042968.015626.089844.023438.132812l23.054688 130.144532c-28.410157.828124-54.410157 16.167968-68.875 40.636718-14.460938 24.46875-15.367188 54.644532-2.394532 79.933594h-185.042968c-18.703126-.019531-33.949219-15.003906-34.289063-33.703125zm293 64.652344c-21.894531-.050781-42.472656-10.46875-55.464844-28.09375-15.328125-20.902344-17.605469-48.648438-5.894531-71.769531 11.714844-23.125 35.429688-37.703126 61.351562-37.710938 1.808594 0 3.640626.070312 5.441407.210938 36.816406 2.921874 64.753906 34.394531 63.292969 71.296874-1.460938 36.902344-31.800782 66.066407-68.734376 66.070313zm0 0"></path>
    <path d="m337.476562 299.140625c3.867188 0 7-3.132813 7-7s-3.132812-7-7-7h-60c-3.863281 0-7 3.132813-7 7s3.136719 7 7 7h17.167969c7.644531.011719 14.515625 4.65625 17.375 11.742187h-34.542969c-3.863281 0-7 3.132813-7 7 0 3.867188 3.136719 7 7 7h34.542969c-2.859375 7.085938-9.730469 11.730469-17.375 11.742188h-17.167969c-2.867187.003906-5.441406 1.75-6.5 4.414062-1.058593 2.664063-.386718 5.707032 1.695313 7.675782l42.910156 40.550781c2.8125 2.652344 7.242188 2.527344 9.898438-.28125s2.527343-7.242187-.28125-9.894531l-30.125-28.46875c15.214843-.21875 28.277343-10.878906 31.550781-25.738282h10.851562c3.863282 0 7-3.132812 7-7 0-3.867187-3.136718-7-7-7h-10.847656c-.925781-4.21875-2.679687-8.207031-5.160156-11.742187zm0 0"></path>
  </svg>
);
export const IconShippingTruck = ({ className = "" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    height="424pt"
    viewBox="0 -32 424.01095 424"
    width="424pt"
  >
    <path d="m230.488281 40.039062h-70.296875c-17.453125-27.222656-48.570312-42.519531-80.789062-39.703124-32.214844 2.8125-60.210938 23.269531-72.679688 53.109374-12.46875 29.839844-7.347656 64.132813 13.289063 89.03125v156.300782c.015625 14.90625 12.09375 26.984375 27 27h18.597656c3.414063 20.207031 20.914063 35 41.40625 35s37.992187-14.792969 41.40625-35h147.1875c3.414063 20.207031 20.914063 35 41.40625 35s37.992187-14.792969 41.40625-35h18.589844c14.902343-.015625 26.984375-12.09375 27-27v-89.5625c0-.058594-.007813-.113282-.011719-.175782 0-.125-.007812-.25-.015625-.375-.007813-.109374-.019531-.21875-.035156-.324218-.011719-.105469-.03125-.234375-.050781-.351563-.023438-.117187-.042969-.222656-.066407-.332031-.027343-.109375-.054687-.222656-.085937-.332031s-.0625-.21875-.101563-.328125c-.039062-.105469-.074219-.214844-.117187-.320313-.039063-.105469-.082032-.210937-.128906-.3125-.046876-.105469-.101563-.207031-.152344-.308593-.046875-.105469-.097656-.203126-.160156-.300782-.058594-.101562-.113282-.195312-.175782-.289062-.058594-.097656-.128906-.199219-.199218-.300782-.066407-.101562-.125-.171874-.191407-.257812-.066406-.085938-.160156-.199219-.242187-.292969-.039063-.042969-.074219-.089843-.113282-.132812l-51.511718-56.132813c-5.121094-5.5625-12.332032-8.734375-19.894532-8.742187h-93.269531v-72.566407c-.015625-14.902343-12.09375-26.980468-27-27zm-143.476562-26c40.316406 0 73 32.683594 73 73 0 40.320313-32.683594 73-73 73-40.316407 0-73-32.679687-73-73 .046875-40.296874 32.703125-72.953124 73-73zm-53 284.738282v-142.796875c31.171875 23.976562 74.554687 24.054687 105.808593.183593 31.257813-23.867187 42.605469-65.738281 27.683594-102.125h62.984375c7.175781.011719 12.992188 5.824219 13 13v244.738282h-95.074219c-3.410156-20.207032-20.910156-35-41.402343-35-20.492188 0-37.992188 14.792968-41.40625 35h-18.59375c-7.175781-.007813-12.992188-5.820313-13-13zm73 48c-15.464844 0-28-12.535156-28-28 0-15.460938 12.535156-28 28-28 15.464843 0 28 12.539062 28 28-.019531 15.457031-12.542969 27.984375-28 28zm230 0c-15.464844 0-28-12.535156-28-28 0-15.460938 12.535156-28 28-28 15.464843 0 28 12.539062 28 28-.019531 15.457031-12.542969 27.984375-28 28zm73-48c-.007813 7.179687-5.824219 12.992187-13 13h-18.601563c-3.410156-20.207032-20.910156-35-41.402344-35-20.496093 0-37.996093 14.792968-41.40625 35h-38.113281v-95.5625h152.523438zm-59.253907-145.167969c3.640626.003906 7.113282 1.53125 9.578126 4.210937l40.75 44.398438h-143.601563v-48.609375zm0 0"></path>
    <path d="m87.285156 94.199219h41.601563c3.863281 0 7-3.132813 7-7 0-3.867188-3.136719-7-7-7h-34.601563v-34.601563c0-3.863281-3.132812-7-7-7-3.867187 0-7 3.136719-7 7v41.601563c0 3.867187 3.132813 7 7 7zm0 0"></path>
  </svg>
);
export const IconWishList = ({ className = "svgwishlist gc-svg-icon" }) => (
  <svg
    className={className}
    width="512"
    height="512"
    fill="currentColor"
    viewBox="0 0 32 32"
  >
    <path d="m29.55 6.509c-1.73-2.302-3.759-3.483-6.031-3.509h-.076c-3.29 0-6.124 2.469-7.443 3.84-1.32-1.371-4.153-3.84-7.444-3.84h-.075c-2.273.026-4.3 1.207-6.059 3.549a8.265 8.265 0 0 0 1.057 10.522l11.821 11.641a1 1 0 0 0 1.4 0l11.82-11.641a8.278 8.278 0 0 0 1.03-10.562zm-2.432 9.137-11.118 10.954-11.118-10.954a6.254 6.254 0 0 1 -.832-7.936c1.335-1.777 2.831-2.689 4.45-2.71h.058c3.48 0 6.627 3.924 6.658 3.964a1.037 1.037 0 0 0 1.57 0c.032-.04 3.2-4.052 6.716-3.964a5.723 5.723 0 0 1 4.421 2.67 6.265 6.265 0 0 1 -.805 7.976z"></path>
  </svg>
);

export const IconWishListEmpty = ({
  className = "svgwishlist gc-svg-icon",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 64 64"
    stroke-width="3"
    stroke="currentColor"
    fill="none"
  >
    <path d="M9.06,25C7.68,17.3,12.78,10.63,20.73,10c7-.55,10.47,7.93,11.17,9.55a.13.13,0,0,0,.25,0c3.25-8.91,9.17-9.29,11.25-9.5C49,9.45,56.51,13.78,55,23.87c-2.16,14-23.12,29.81-23.12,29.81S11.79,40.05,9.06,25Z" />
  </svg>
);

export const IconWishListFilled = ({
  className = "svgwishlist gc-svg-icon",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 64 64"
    stroke-width="3"
    stroke="red"
    fill="red"
  >
    <path d="M9.06,25C7.68,17.3,12.78,10.63,20.73,10c7-.55,10.47,7.93,11.17,9.55a.13.13,0,0,0,.25,0c3.25-8.91,9.17-9.29,11.25-9.5C49,9.45,56.51,13.78,55,23.87c-2.16,14-23.12,29.81-23.12,29.81S11.79,40.05,9.06,25Z" />
  </svg>
);

export const IconSearchMagnifyGlass = ({
  className = "svgSearch gc-svg-icon",
}) => (
  <svg
    className={className}
    width="512"
    height="512"
    fill="currentColor"
    viewBox="0 0 48 48"
    enableBackground="new 0 0 48 48"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g>
      <path d="m40.2850342 37.4604492-6.4862061-6.4862061c1.9657593-2.5733643 3.0438843-5.6947021 3.0443115-8.9884033 0-3.9692383-1.5458984-7.7011719-4.3530273-10.5078125-2.8066406-2.8066406-6.5380859-4.3525391-10.5078125-4.3525391-3.9692383 0-7.7011719 1.5458984-10.5078125 4.3525391-5.7939453 5.7944336-5.7939453 15.222168 0 21.015625 2.8066406 2.8071289 6.5385742 4.3530273 10.5078125 4.3530273 3.2937012-.0004272 6.4150391-1.0785522 8.9884033-3.0443115l6.4862061 6.4862061c.3901367.390625.9023438.5859375 1.4140625.5859375s1.0239258-.1953125 1.4140625-.5859375c.78125-.7807617.78125-2.0473633 0-2.828125zm-25.9824219-7.7949219c-4.234375-4.234375-4.2338867-11.1245117 0-15.359375 2.0512695-2.0507813 4.7788086-3.1806641 7.6796875-3.1806641 2.9013672 0 5.628418 1.1298828 7.6796875 3.1806641 2.0512695 2.0512695 3.1811523 4.7788086 3.1811523 7.6796875 0 2.9013672-1.1298828 5.628418-3.1811523 7.6796875s-4.7783203 3.1811523-7.6796875 3.1811523c-2.9008789.0000001-5.628418-1.1298827-7.6796875-3.1811523z"></path>
    </g>
  </svg>
);

export const IconPaperSearchMagnifyGlass = ({
  className = "svgPaperSearch gc-svg-icon",
}) => (
  <svg
    className={className}
    strokeWidth
    height="512"
    width="512"
    fill="currentColor"
    enable-background="new 0 0 24 24"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="m17 22c-2.757 0-5-2.243-5-5s2.243-5 5-5 5 2.243 5 5-2.243 5-5 5zm0-8.5c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5z"></path>
    <path d="m23.25 24c-.192 0-.384-.073-.53-.22l-3.25-3.25c-.293-.293-.293-.768 0-1.061s.768-.293 1.061 0l3.25 3.25c.293.293.293.768 0 1.061-.147.147-.339.22-.531.22z"></path>
    <path d="m10.53 21h-7.78c-1.517 0-2.75-1.233-2.75-2.75v-15.5c0-1.517 1.233-2.75 2.75-2.75h11.5c1.517 0 2.75 1.233 2.75 2.75v7.04c0 .414-.336.75-.75.75s-.75-.336-.75-.75v-7.04c0-.689-.561-1.25-1.25-1.25h-11.5c-.689 0-1.25.561-1.25 1.25v15.5c0 .689.561 1.25 1.25 1.25h7.78c.414 0 .75.336.75.75s-.336.75-.75.75z"></path>
    <path d="m13.25 9.5h-9.5c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h9.5c.414 0 .75.336.75.75s-.336.75-.75.75z"></path>
    <path d="m9.25 13.5h-5.5c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h5.5c.414 0 .75.336.75.75s-.336.75-.75.75z"></path>
    <path d="m8.25 5.5h-4.5c-.414 0-.75-.336-.75-.75s.336-.75.75-.75h4.5c.414 0 .75.336.75.75s-.336.75-.75.75z"></path>
  </svg>
);

export const IconUserProfile = ({ className = "svgUser2 gc-svg-icon" }) => (
  <svg
    className={className}
    enableBackground="new 0 0 512 512"
    height="512"
    viewBox="0 0 512 512"
    width="512"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g>
      <g>
        <path d="m256 253.7c-62 0-112.4-50.4-112.4-112.4s50.4-112.4 112.4-112.4 112.4 50.4 112.4 112.4-50.4 112.4-112.4 112.4zm0-195.8c-46 0-83.4 37.4-83.4 83.4s37.4 83.4 83.4 83.4 83.4-37.4 83.4-83.4-37.4-83.4-83.4-83.4z"></path>
      </g>
      <g>
        <path d="m452.1 483.2h-392.2c-8 0-14.5-6.5-14.5-14.5 0-106.9 94.5-193.9 210.6-193.9s210.6 87 210.6 193.9c0 8-6.5 14.5-14.5 14.5zm-377-29.1h361.7c-8.1-84.1-86.1-150.3-180.8-150.3s-172.7 66.2-180.9 150.3z"></path>
      </g>
    </g>
  </svg>
);
export const IconDeliverReturn = ({
  className = "svgDeliveryReturn gc-svg-icon",
}) => (
  <svg
    className={className}
    width="40.124px"
    height="40.124px"
    enableBackground="new 0 0 512 512"
    version="1.1"
    viewBox="0 0 512 512"
    xmlSpace="preserve"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="m386.69 304.4c-35.587 0-64.538 28.951-64.538 64.538s28.951 64.538 64.538 64.538c35.593 0 64.538-28.951 64.538-64.538s-28.951-64.538-64.538-64.538zm0 96.807c-17.796 0-32.269-14.473-32.269-32.269s14.473-32.269 32.269-32.269 32.269 14.473 32.269 32.269c0 17.797-14.473 32.269-32.269 32.269z"></path>
    <path d="m166.18 304.4c-35.587 0-64.538 28.951-64.538 64.538s28.951 64.538 64.538 64.538 64.538-28.951 64.538-64.538-28.951-64.538-64.538-64.538zm0 96.807c-17.796 0-32.269-14.473-32.269-32.269s14.473-32.269 32.269-32.269c17.791 0 32.269 14.473 32.269 32.269 0 17.797-14.473 32.269-32.269 32.269z"></path>
    <path d="m430.15 119.68c-2.743-5.448-8.32-8.885-14.419-8.885h-84.975v32.269h75.025l43.934 87.384 28.838-14.5-48.403-96.268z"></path>
    <rect x="216.2" y="353.34" width="122.08" height="32.269"></rect>
    <path d="m117.78 353.34h-55.932c-8.912 0-16.134 7.223-16.134 16.134 0 8.912 7.223 16.134 16.134 16.134h55.933c8.912 0 16.134-7.223 16.134-16.134 0-8.912-7.223-16.134-16.135-16.134z"></path>
    <path d="m508.61 254.71-31.736-40.874c-3.049-3.937-7.755-6.239-12.741-6.239h-117.24v-112.94c0-8.912-7.223-16.134-16.134-16.134h-268.91c-8.912 0-16.134 7.223-16.134 16.134s7.223 16.134 16.134 16.134h252.77v112.94c0 8.912 7.223 16.134 16.134 16.134h125.48l23.497 30.268v83.211h-44.639c-8.912 0-16.134 7.223-16.134 16.134 0 8.912 7.223 16.134 16.134 16.134h60.773c8.912 0 16.134-7.223 16.135-16.134v-104.87c0-3.582-1.194-7.067-3.388-9.896z"></path>
    <path d="m116.71 271.6h-74.219c-8.912 0-16.134 7.223-16.134 16.134 0 8.912 7.223 16.134 16.134 16.134h74.218c8.912 0 16.134-7.223 16.134-16.134 1e-3 -8.911-7.222-16.134-16.133-16.134z"></path>
    <path d="m153.82 208.13h-137.68c-8.911 0-16.134 7.223-16.134 16.135s7.223 16.134 16.134 16.134h137.68c8.912 0 16.134-7.223 16.134-16.134s-7.222-16.135-16.134-16.135z"></path>
    <path d="m180.17 144.67h-137.68c-8.912 0-16.134 7.223-16.134 16.134 0 8.912 7.223 16.134 16.134 16.134h137.68c8.912 0 16.134-7.223 16.134-16.134 1e-3 -8.911-7.222-16.134-16.134-16.134z"></path>
  </svg>
);
export const IconSizeGuide = ({ className = "svgQuestion gc-svg-icon" }) => (
  <svg
    className={className}
    width="40.124px"
    height="40.124px"
    enableBackground="new 0 0 20 20"
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="m10 0c-5.5 0-10 4.5-10 10s4.5 10 10 10 10-4.5 10-10-4.5-10-10-10zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8z"></path>
    <path d="m10.7 4.1c-1.2-.2-2.4.1-3.3.8-.9.8-1.4 1.9-1.4 3.1h2c0-.6.3-1.2.7-1.5.5-.4 1.1-.6 1.7-.5.8.1 1.5.8 1.6 *******-.2 1.7-1 2.1-1.2.7-2 1.9-2 3.2h2c0-.6.4-1.2.9-1.5 1.5-.8 2.3-2.5 2-4.2-.2-1.5-1.6-2.9-3.2-3.1z"></path>
    <path d="m9 14h2v2h-2z"></path>
  </svg>
);
export const IconCalendar = ({ className = "svgShipping gc-svg-icon" }) => (
  <svg
    className={className}
    width="40.124px"
    height="40.124px"
    enableBackground="new 0 0 512 512"
    version="1.1"
    viewBox="0 0 512 512"
    xmlSpace="preserve"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="386" cy="210" r="20"></circle>
    <path d="m432 40h-26v-20c0-11.046-8.954-20-20-20s-20 8.954-20 20v20h-91v-20c0-11.046-8.954-20-20-20s-20 8.954-20 20v20h-90v-20c0-11.046-8.954-20-20-20s-20 8.954-20 20v20h-25c-44.112 0-80 35.888-80 80v312c0 44.112 35.888 80 80 80h153c11.046 0 20-8.954 20-20s-8.954-20-20-20h-153c-22.056 0-40-17.944-40-40v-312c0-22.056 17.944-40 40-40h25v20c0 11.046 8.954 20 20 20s20-8.954 20-20v-20h90v20c0 11.046 8.954 20 20 20s20-8.954 20-20v-20h91v20c0 11.046 8.954 20 20 20s20-8.954 20-20v-20h26c22.056 0 40 17.944 40 40v114c0 11.046 8.954 20 20 20s20-8.954 20-20v-114c0-44.112-35.888-80-80-80z"></path>
    <path d="m391 270c-66.72 0-121 54.28-121 121s54.28 121 121 121 121-54.28 121-121-54.28-121-121-121zm0 202c-44.663 0-81-36.336-81-81s36.337-81 81-81 81 36.336 81 81-36.337 81-81 81z"></path>
    <path d="m420 371h-9v-21c0-11.046-8.954-20-20-20s-20 8.954-20 20v41c0 11.046 8.954 20 20 20h29c11.046 0 20-8.954 20-20s-8.954-20-20-20z"></path>
    <circle cx="299" cy="210" r="20"></circle>
    <circle cx="212" cy="297" r="20"></circle>
    <circle cx="125" cy="210" r="20"></circle>
    <circle cx="125" cy="297" r="20"></circle>
    <circle cx="125" cy="384" r="20"></circle>
    <circle cx="212" cy="384" r="20"></circle>
    <circle cx="212" cy="210" r="20"></circle>
  </svg>
);
export const IconSmiley = ({ className = "svgSmile gc-svg-icon" }) => (
  <svg
    className={className}
    width="40.124px"
    height="40.124px"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 512 512"
    enableBackground="new 0 0 512 512"
    xmlSpace="preserve"
  >
    <path d="M437.02,74.98C388.667,26.629,324.38,0,256,0S123.333,26.629,74.98,74.98C26.629,123.333,0,187.62,0,256 s26.629,132.668,74.98,181.02C123.333,485.371,187.62,512,256,512s132.667-26.629,181.02-74.98 C485.371,388.668,512,324.38,512,256S485.371,123.333,437.02,74.98z M256,472c-119.103,0-216-96.897-216-216S136.897,40,256,40 s216,96.897,216,216S375.103,472,256,472z"></path>
    <path d="M368.993,285.776c-0.072,0.214-7.298,21.626-25.02,42.393C321.419,354.599,292.628,368,258.4,368 c-34.475,0-64.195-13.561-88.333-40.303c-18.92-20.962-27.272-42.54-27.33-42.691l-37.475,13.99 c0.42,1.122,10.533,27.792,34.013,54.273C171.022,389.074,212.215,408,258.4,408c46.412,0,86.904-19.076,117.099-55.166 c22.318-26.675,31.165-53.55,31.531-54.681L368.993,285.776z"></path>
    <circle cx="168" cy="180.12" r="32"></circle>
    <circle cx="344" cy="180.12" r="32"></circle>
  </svg>
);

export const IconStar = ({ className = "star-icon" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
  </svg>
);

export const IconStarFilled = ({ className = "star-icon-filled" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="#ffc107"
    stroke="#ffc107"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
  </svg>
);

export const IconEdit = ({ className = "edit-icon" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
  </svg>
);

export const IconDelete = ({ className = "delete-icon" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polyline points="3 6 5 6 21 6"></polyline>
    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
    <line x1="10" y1="11" x2="10" y2="17"></line>
    <line x1="14" y1="11" x2="14" y2="17"></line>
  </svg>
);

export const IconPlus = ({ className = "plus-icon" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <line x1="12" y1="5" x2="12" y2="19"></line>
    <line x1="5" y1="12" x2="19" y2="12"></line>
  </svg>
);

export const IconMinus = ({ className = "minus-icon" }) => (
  <svg
    className={className}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <line x1="5" y1="12" x2="19" y2="12"></line>
  </svg>
);

export const MeeshoIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="100%"
    height="100%"
    viewBox="0 0 156 36"
    fill="none"
  >
    <g clip-path="url(#clip0_2271_104976)">
      <rect width="156" height="36" fill="white" />
      <path
        d="M56.307 23.6979C56.6873 23.4071 56.8752 22.991 56.8752 22.4452C56.8752 20.7137 56.6381 19.1567 56.1683 17.7697C55.6985 16.3872 55.014 15.2105 54.1147 14.2351C53.2199 13.2643 52.1371 12.5171 50.8799 12.0025C49.6182 11.488 48.1954 11.2285 46.616 11.2285C44.4595 11.2285 42.5356 11.7207 40.8489 12.7095C39.1621 13.6982 37.8422 15.0584 36.8803 16.7899C35.9228 18.5214 35.4441 20.5437 35.4441 22.8523C35.4441 25.2236 35.9362 27.2728 36.925 29.0088C37.9138 30.7403 39.3187 32.078 41.1441 33.0221C42.9696 33.9661 45.1396 34.4359 47.663 34.4359C48.8486 34.4359 50.1327 34.2748 51.5152 33.9572C52.8977 33.6395 54.1192 33.1429 55.184 32.4762C55.7298 32.1407 56.1191 31.7469 56.3473 31.2906C56.5755 30.8342 56.6605 30.3868 56.5978 29.9439C56.5352 29.5054 56.3607 29.1296 56.0744 28.8253C55.7835 28.5211 55.3988 28.3466 54.9111 28.3018C54.4234 28.2571 53.8641 28.4137 53.2243 28.7806C52.3429 29.2683 51.4078 29.6083 50.419 29.8052C49.4303 30.002 48.5399 30.1004 47.7524 30.1004C45.4706 30.1004 43.7481 29.4875 42.5759 28.2526C41.6497 27.2773 41.0949 25.8948 40.9025 24.1275H54.6829C55.3898 24.1275 55.9267 23.9843 56.307 23.6979ZM43.587 15.9935C44.4819 15.3984 45.5691 15.1031 46.8487 15.1031C48.003 15.1031 48.9694 15.3537 49.7434 15.8548C50.5175 16.3559 51.1125 17.0807 51.5197 18.0203C51.8597 18.8032 52.0521 19.7518 52.1103 20.8479H40.9294C41.0368 19.9844 41.2336 19.1925 41.5334 18.499C42.0077 17.4207 42.6922 16.5841 43.587 15.9935Z"
        fill="#570D48"
      />
      <path
        d="M81.1298 23.6979C81.5101 23.4071 81.698 22.991 81.698 22.4452C81.698 20.7137 81.4609 19.1567 80.9911 17.7697C80.5213 16.3872 79.8367 15.2105 78.9374 14.2351C78.0426 13.2643 76.9599 12.5171 75.7026 12.0025C74.4409 11.488 73.0181 11.2285 71.4388 11.2285C69.2822 11.2285 67.3584 11.7207 65.6716 12.7095C63.9849 13.6982 62.665 15.0584 61.703 16.7899C60.7456 18.5214 60.2668 20.5437 60.2668 22.8523C60.2668 25.2236 60.759 27.2728 61.7478 29.0088C62.7366 30.7403 64.1415 32.078 65.9669 33.0221C67.7924 33.9661 69.9623 34.4359 72.4857 34.4359C73.6714 34.4359 74.9554 34.2748 76.338 33.9572C77.7205 33.6395 78.9419 33.1429 80.0068 32.4762C80.5526 32.1407 80.9419 31.7469 81.17 31.2906C81.3982 30.8342 81.4832 30.3868 81.4206 29.9439C81.3579 29.5054 81.1835 29.1296 80.8971 28.8253C80.6063 28.5211 80.2215 28.3466 79.7338 28.3018C79.2462 28.2571 78.6869 28.4137 78.0471 28.7806C77.1657 29.2683 76.2306 29.6083 75.2418 29.8052C74.253 30.002 73.3627 30.1004 72.5752 30.1004C70.2934 30.1004 68.5709 29.4875 67.3986 28.2526C66.468 27.2773 65.9177 25.8948 65.7253 24.1275H79.5057C80.2081 24.1275 80.7495 23.9843 81.1298 23.6979ZM68.4098 15.9935C69.3046 15.3984 70.3918 15.1031 71.6714 15.1031C72.8258 15.1031 73.7922 15.3537 74.5662 15.8548C75.3402 16.3559 75.9353 17.0807 76.3424 18.0203C76.6825 18.8032 76.8749 19.7518 76.933 20.8479H65.7521C65.8595 19.9844 66.0564 19.1925 66.3562 18.499C66.8259 17.4207 67.5105 16.5841 68.4098 15.9935Z"
        fill="#570D48"
      />
      <path
        d="M97.9931 21.3941L93.434 20.5261C92.5526 20.374 91.8993 20.0876 91.4743 19.6581C91.0492 19.2331 90.8345 18.7006 90.8345 18.0608C90.8345 17.2108 91.1924 16.5262 91.9038 16.0072C92.6197 15.4927 93.7203 15.2332 95.2102 15.2332C96.0021 15.2332 96.8298 15.3406 97.6933 15.5508C98.5613 15.7656 99.4651 16.1146 100.405 16.5978C100.919 16.8394 101.385 16.9244 101.796 16.8483C102.208 16.7723 102.548 16.5754 102.821 16.2533C103.094 15.9356 103.277 15.5687 103.367 15.1571C103.456 14.7455 103.42 14.3428 103.255 13.9491C103.089 13.5554 102.776 13.2332 102.32 12.9916C101.228 12.3832 100.091 11.9357 98.9237 11.6449C97.7515 11.3541 96.4987 11.2109 95.161 11.2109C93.3087 11.2109 91.6667 11.5017 90.235 12.0789C88.8077 12.6561 87.6892 13.4793 86.8838 14.5397C86.0785 15.6045 85.6758 16.8662 85.6758 18.3248C85.6758 19.9355 86.168 21.2509 87.1567 22.2665C88.1455 23.2866 89.5817 23.9757 91.4653 24.3425L96.0245 25.2105C96.9641 25.394 97.671 25.6758 98.1452 26.0517C98.615 26.432 98.8521 26.9733 98.8521 27.6713C98.8521 28.4901 98.4942 29.1522 97.7828 29.6533C97.067 30.1544 95.9842 30.405 94.5212 30.405C93.4876 30.405 92.4407 30.2931 91.3759 30.065C90.311 29.8368 89.1701 29.4341 87.9576 28.8569C87.4699 28.6153 87.0225 28.5393 86.6109 28.6288C86.1993 28.7182 85.8637 28.9196 85.6087 29.2194C85.3492 29.5236 85.1971 29.8815 85.1523 30.2931C85.1076 30.7048 85.1836 31.1119 85.3805 31.5235C85.5774 31.9352 85.9219 32.2931 86.4051 32.5928C87.5594 33.264 88.8659 33.7338 90.3244 34.0067C91.783 34.2796 93.1655 34.4183 94.472 34.4183C97.3578 34.4183 99.6709 33.7874 101.402 32.5257C103.134 31.264 104.002 29.5236 104.002 27.3044C104.002 25.6624 103.492 24.356 102.476 23.3851C101.465 22.4276 99.9662 21.761 97.9931 21.3941Z"
        fill="#570D48"
      />
      <path
        d="M125.603 12.3203C124.448 11.6536 122.972 11.3181 121.182 11.3181C119.388 11.3181 117.786 11.7342 116.372 12.5708C115.348 13.1793 114.52 13.9981 113.889 15.0137V4.43686C113.889 3.49281 113.639 2.78142 113.138 2.29374C112.637 1.80606 111.93 1.56445 111.017 1.56445C110.104 1.56445 109.406 1.80606 108.918 2.29374C108.431 2.78142 108.189 3.49281 108.189 4.43686V31.5188C108.189 32.4629 108.431 33.1832 108.918 33.6843C109.406 34.1854 110.104 34.436 111.017 34.436C112.932 34.436 113.889 33.4651 113.889 31.5188V21.5325C113.889 19.8011 114.381 18.4096 115.37 17.3627C116.359 16.3157 117.688 15.7878 119.361 15.7878C120.73 15.7878 121.741 16.1815 122.395 16.9734C123.048 17.7653 123.374 19.027 123.374 20.7585V31.5188C123.374 32.4629 123.625 33.1832 124.126 33.6843C124.627 34.1854 125.334 34.436 126.247 34.436C127.16 34.436 127.858 34.1854 128.345 33.6843C128.833 33.1832 129.074 32.4629 129.074 31.5188V20.4856C129.074 18.4185 128.784 16.7094 128.207 15.3582C127.625 14.0026 126.761 12.9914 125.603 12.3203Z"
        fill="#570D48"
      />
      <path
        d="M150.618 12.6423C148.918 11.6983 146.909 11.2285 144.6 11.2285C142.869 11.2285 141.303 11.497 139.902 12.0249C138.506 12.5573 137.303 13.3314 136.301 14.3515C135.298 15.3716 134.529 16.5841 133.996 17.9979C133.464 19.4117 133.2 21.0135 133.2 22.8076C133.2 25.1789 133.67 27.237 134.614 28.9864C135.553 30.7358 136.878 32.078 138.582 33.0221C140.283 33.9661 142.291 34.4359 144.6 34.4359C146.332 34.4359 147.897 34.1674 149.298 33.6395C150.694 33.1071 151.897 32.333 152.9 31.3129C153.902 30.2928 154.671 29.0714 155.204 27.6441C155.736 26.2169 156 24.6062 156 22.8121C156 20.4408 155.53 18.3916 154.586 16.6557C153.642 14.9197 152.322 13.5819 150.618 12.6423ZM149.548 26.8433C149.079 27.9215 148.416 28.7358 147.566 29.2817C146.716 29.8275 145.728 30.1004 144.605 30.1004C142.904 30.1004 141.535 29.4875 140.502 28.2526C139.468 27.0222 138.954 25.2057 138.954 22.8031C138.954 21.1924 139.191 19.8457 139.661 18.7674C140.13 17.6892 140.793 16.8838 141.643 16.3514C142.493 15.819 143.482 15.555 144.605 15.555C146.305 15.555 147.674 16.1545 148.707 17.3536C149.741 18.5527 150.255 20.3692 150.255 22.8031C150.255 24.4183 150.018 25.765 149.548 26.8433Z"
        fill="#570D48"
      />
      <path
        d="M15.5118 34.4312C14.1249 34.4312 12.9571 33.2635 12.9571 31.8765V20.1811C12.9705 18.0156 11.1675 16.2662 9.03329 16.302C6.89913 16.2662 5.10052 18.0201 5.10947 20.1811V31.8765C5.10947 33.2858 3.96409 34.4312 2.55473 34.4312C1.18117 34.4312 0 33.2456 0 31.8765C0 31.872 0 20.2214 0 20.2214C0 17.7248 1.01115 15.4654 2.64869 13.8323C4.28623 12.1948 6.54119 11.1836 9.03329 11.1836C11.5746 11.1836 13.8743 12.235 15.5163 13.9262C17.1539 12.235 19.4536 11.1836 21.9993 11.1836C24.4959 11.1836 26.7509 12.1948 28.3839 13.8323C30.0215 15.4698 31.0326 17.7293 31.0326 20.2214C31.0326 20.2214 31.0326 31.872 31.0326 31.8765C31.0326 33.2456 29.8515 34.4312 28.4779 34.4312C27.0686 34.4312 25.9232 33.2858 25.9232 31.8765V20.1811C25.9366 18.0156 24.1335 16.2662 21.9993 16.302C19.8652 16.2662 18.0666 18.0201 18.0755 20.1811V31.8765C18.0666 33.2635 16.8988 34.4312 15.5118 34.4312Z"
        fill="#570D48"
      />
    </g>
    <defs>
      <clipPath id="clip0_2271_104976">
        <rect width="100%" height="100%" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
