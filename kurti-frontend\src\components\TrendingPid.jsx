import React, { useEffect, useState } from "react";
import { Nav } from "react-bootstrap";
import { Swiper, SwiperSlide } from "swiper/react";
// Import Swiper styles
import { Link } from "react-router-dom";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Navigation, Pagination } from "swiper/modules";
import { useProductsQuery } from "api/product.api";
import { useTagsQuery } from "api/master.api";
import ProductTile from "./organisms/ProductTile.organisms.jsx";
import { IconRightArrow } from "utils/icons";
import ProductSliderTemplate from "./templates/ProductSlider.template.jsx";

const TabMenu = ({ cssClass, activeKey, onSelect }) => {
  const { data: tags = [] } = useTagsQuery();
  return (
    <Nav
      variant="tabs"
      activeKey={activeKey}
      onSelect={onSelect}
      className={cssClass}
    >
      <Nav.Item>
        <Nav.Link eventKey="allProducts">All Products</Nav.Link>
      </Nav.Item>
      {tags?.length > 0 &&
        tags.map((tag) => {
          return (
            <Nav.Item>
              <Nav.Link eventKey={tag?.name}>{tag?.name}</Nav.Link>
            </Nav.Item>
          );
        })}
    </Nav>
  );
};

const TrendingPid = () => {
  const [activeKey, setActiveKey] = useState("allProducts");
  const [params, setParams] = useState({ page: 1, limit: 20 });

  const { data: { items: products = [] } = {}, refetch } =
    useProductsQuery(params);

  useEffect(() => {
    setParams({
      ...params,
      tags: activeKey === "allProducts" ? undefined : activeKey,
    });
  }, [activeKey]);

  useEffect(() => {
    refetch();
  }, [params]);

  const handleSelect = (key) => {
    setActiveKey(key);
  };

  return (
    <div className="container">
      <div className="gc-tab-header">
        <h3 className="gc-tab-title  order-lg-1">Trending Best Selling Products</h3>
        <Link
          className="gc-tab-button gc-btn gc-btn-secondary gc-btn-text gc-square has-icon icon-after order-lg-3"
          to="/products"
        >
          <IconRightArrow />
          <span className="btn-text" data-hover="All Products"></span>
        </Link>
        <TabMenu
          cssClass={"gc-tab-menu fast-filters order-lg-2"}
          activeKey={activeKey}
          onSelect={handleSelect}
        />
      </div>

      <div className="tab-content">
      <ProductSliderTemplate products={products} />
      </div>
    </div>
  );
};

export default TrendingPid;
