import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { Box, Button, TextField, useTheme } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import Header from "components/Header";
import { useDebounce, useDeleteRecord } from "hooks";
import { useDispatch } from "react-redux";
import { useGetSizeTypesQuery } from "services";
import {
  setConfirmModalConfig,
  setSelectedModelContent,
  setShowMasterModal,
} from "store/slices/utilSlice";
import { tokens } from "theme";
import dataGridStyles from "../../../styles/dataGridStyles";
import StyledDataGrid from "components/datagrids/StyledDataGrid";
import { useSearchParams } from "react-router-dom";

const List = () => {
  // hooks
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const searchName = searchParams.get("name");
  const debouncedNameQry = useDebounce(searchName, 500);
  // queries
  const {
    data: sizeTypesData = [],
    isLoading,
    refetch,
  } = useGetSizeTypesQuery(null, {
    refetchOnMountOrArgChange: true,
  });

  // hooks
  const { deleteRecord } = useDeleteRecord();

  const theme = useTheme();
  const styles = dataGridStyles(theme.palette.mode);
  const colors = tokens(theme.palette.mode);
  const columns = [
    {
      field: "name",
      headerName: "Name",
      flex: 1,
      cellClassName: "name-column--cell",
      renderCell: (params) => (
        <span
          style={{
            textTransform: "capitalize",
          }}
        >
          {params.value}
        </span>
      ),
    },
    {
      field: "values",
      headerName: "Values",
      flex: 1,
      renderCell: (params) => {
        const values = params.row.values;
        return (
          <Box display="flex" justifyContent="center">
            {values.map((value) => (
              <Box
                key={value}
                sx={{
                  backgroundColor: colors.blueAccent[600],
                  color: colors.whiteAccent[100],
                  fontSize: "12px",
                  fontWeight: "normal",
                  padding: "5px 10px",
                  margin: "0 5px",
                  borderRadius: "5px",
                }}
              >
                {value}
              </Box>
            ))}
          </Box>
        );
      },
    },
    {
      headerName: "Action",
      flex: 1,
      renderCell: (params) => (
        <Box display="flex" justifyContent="center" gap={2}>
          <span
            className="cursor-pointer"
            onClick={() => handleEdit(params.row)}
          >
            <EditIcon />
          </span>
          <span
            className="cursor-pointer"
            onClick={() => {
              const payload = {
                modelName: "SizeType",
                status: true,
                modelId: params.row._id,
              };
              dispatch(
                setConfirmModalConfig({
                  visible: true,
                  data: {
                    onSubmit: () => deleteRecord(payload, refetch),
                    content: {
                      heading: "Delete Size Type?",
                      description: "Are you sure you want to delete this size type?",
                    },
                  },
                })
              );
            }}
          >
            <DeleteIcon />
          </span>
        </Box>
      ),
    },
  ];

  const handleChange = (evt) => {
    setSearchParams({
      name: evt.target.value,
    });
  };
  const handleOpen = () => {
    dispatch(setShowMasterModal(true));
    dispatch(setSelectedModelContent({}));
  };

  const handleEdit = (row) => {
    dispatch(setShowMasterModal(true));
    dispatch(setSelectedModelContent(row));
  };

  return (
    <Box m="20px">
      <Box
        sx={{
          ...styles.headingsContainer,
          ...styles.dFlex,
          ...styles.alignItemsStart,
          ...styles.justifyContentBetween,
        }}
      >
        <Box sx={{ ...styles.mainHeadings }}>
          <Header title="SIZE TYPES" subtitle="Managing the Size Types" />
        </Box>
        <Box>
          <Button sx={styles.buttonMd} onClick={handleOpen}>
            <AddOutlinedIcon sx={{ mr: "10px" }} />
            Add Size Type
          </Button>
        </Box>
      </Box>
      <Box
        sx={{
          ...styles.formContainer,
        }}
      >
        {/* <Box
          sx={{
            ...styles.searchBar,
          }}
        >
          <TextField
            placeholder="Search"
            onChange={handleChange}
            value={searchName}
          />
        </Box> */}

        <StyledDataGrid
          rows={sizeTypesData}
          columns={columns}
          getRowId={(row) => row._id}
          loading={isLoading}
        />
      </Box>
    </Box>
  );
};

export default List;
