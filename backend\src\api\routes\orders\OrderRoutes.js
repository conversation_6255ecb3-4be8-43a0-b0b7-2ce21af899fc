const express = require('express');
const router = express.Router();
const OrderController = require('./OrderController');
const InvoiceController = require('./InvoiceController');
const validations = require('./OrderValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin, verifyTokenAdmin } = require('../../util/auth');

router.get('/my-orders', verifyTokenUserOrAdmin, OrderController.MyOrdersList);
router.get('/all-orders', verifyTokenAdmin, OrderController.AllOrdersList);
router.post('/place-order', verifyTokenUserOrAdmin, OrderController.placeOrder);
router.get('/order-detail/:orderId', verifyTokenUserOrAdmin, OrderController.orderDetail);
router.post('/create-order', OrderController.paymentOrder);
router.post('/cancel-order', verifyTokenUserOrAdmin, OrderController.cancelOrder);
router.get('/track-order/:trackingId', OrderController.trackOrder);
router.post('/get-refund', verifyTokenUserOrAdmin, OrderController.processRefund);
router.put('/update-order-status/', verifyTokenUserOrAdmin, OrderController.updateOrderStatus);

// Invoice routes
router.get('/invoice/:orderId/generate', verifyTokenUserOrAdmin, InvoiceController.generateInvoice);
router.get('/invoice/:orderId/download', verifyTokenUserOrAdmin, InvoiceController.downloadInvoice);
router.get('/invoice/:orderId/public-download', InvoiceController.publicDownloadInvoice);
router.post('/invoice/:orderId/email', verifyTokenUserOrAdmin, InvoiceController.emailInvoice);
router.post('/invoice/:orderId/whatsapp', verifyTokenUserOrAdmin, InvoiceController.whatsappInvoice);

module.exports = router;
