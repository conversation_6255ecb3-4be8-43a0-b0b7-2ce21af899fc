const { Joi, common } = require('../../util/validations');

const addFaq = Joi.object().keys({
    question: Joi.string()
        .trim()
        .required(),
    answer: Joi.string()
        .trim()
        .required(),
    category: Joi.string()
        .trim()
        .optional()
        .allow(''),
    sortOrder: Joi.number()
        .optional()
        .allow(''),
});

const updateFaq = Joi.object().keys({
    question: Joi.string()
        .trim()
        .optional(),
    answer: Joi.string()
        .trim()
        .optional(),
    category: Joi.string()
        .trim()
        .optional()
        .allow(''),
    sortOrder: Joi.number()
        .optional()
        .allow(''),
});

module.exports = {
    addFaq,
    updateFaq
};
