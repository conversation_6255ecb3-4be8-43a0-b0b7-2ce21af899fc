.custom-pagination-container {
    .pagination {
        justify-content: center;
        margin-bottom: 0;
        gap: 10px;
    }

    .page-item {
        width: 30px;
        height: 30px;
        border: 1px solid #257ed4;
        border-radius: 50%;
        color: #257ed4;

        &:first-child,
        &:last-child {
            border: none;
            border-radius: 0px;
            font-size: 18px;
            width: 40px !important;

            & span {
                font-size: 17px;

                &.disabled {
                    color: #6e6e6e !important;
                }
            }
        }

        &:first-child {
            margin-right: 30px;
        }

        &:last-child {
            margin-left: 30px;
        }

        &.active {
            background-color: #257ed4;
            color: #ffffff;
        }
    }

    .page-link {
        padding: 0;
        border: none;
        display: block;
        background-color: transparent !important;
        border-color: transparent !important;
        font-size: 12px;
        font-weight: 400;
        width: 100%;
        height: 100%;
        text-align: center;
        margin: 0;
        line-height: 27px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:focus{
            box-shadow: none;
        }
    }
}
