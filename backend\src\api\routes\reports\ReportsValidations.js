const { Joi, common } = require('../../util/validations');

const addCoupon = Joi.object().keys({
    code: Joi.string()
        .trim()
        .required(),
    description: Joi.string()
        .trim()
        .optional()
        .allow(null, ''),
    discountType: Joi.string()
        .valid('flat', 'percentage', 'bundle')
        .required(),
    discountValue: Joi.number().when('discountType', {
        is: 'bundle',
        then: Joi.forbidden(),
        otherwise: Joi.required(),
    }),
    maxDiscount: Joi.number().when('discountType', {
        is: 'percentage',
        then: Joi.required(),
        otherwise: Joi.forbidden(),
    }),
    bundleDetails: Joi.object({
        category: Joi.string().required(),
        quantity: Joi.number()
            .min(1)
            .required(),
        bundlePrice: Joi.number().required(),
    }).when('discountType', { is: 'bundle', then: Joi.required(), otherwise: Joi.forbidden() }),
    expiry: Joi.date().required(),
    maxUses: Joi.number()
        .min(1)
        .required(),
    minCartValue: Joi.number().when('discountType', { is: 'bundle', then: Joi.forbidden(), otherwise: Joi.required() }),
    status: Joi.string()
        .valid('Active', 'Inactive')
        .required(),
});

module.exports = { addCoupon };
