/*
* Cart free shipping calculator progressbar
*/
.gc-before-cart-table .gc-cart-goal-wrapper {
    padding: 20px 20px 25px;
    margin-bottom: 30px;
    border: 2px solid var(--gc-purple);
    border-radius: var(--gc-border-radius);
    max-width: 40%;
    max-width: 320px;
}

.gc-before-cart-table .gc-cart-goal-wrapper.free-shipping-success {
    border-color: var(--gc-success);
}

.gc-header-mobile-content .gc-cart-goal-wrapper,
.gc-side-panel .gc-cart-goal-wrapper {
    margin-top: 20px;
}

.gc-free-shipping-progress .gc-progress-bar-wrap {
    position: relative;
    height: 6px;
    margin: 0 13px;
}

.gc-free-shipping-progress .gc-progress-bar-wrap:before {
    position: absolute;
    content: "";
    top: 0;
    left: -13px;
    right: -13px;
    height: 100%;
    border-radius: var(--gc-border-radius);
    background: var(--gc-purple-bg);
}

.gc-free-shipping-progress .gc-progress-bar:before {
    position: absolute;
    content: "";
    top: 0;
    left: -13px;
    right: -13px;
    height: 100%;
    border-radius: var(--gc-border-radius);
    background: var(--gc-purple);
}

.gc-free-shipping-progress .gc-progress-bar {
    position: relative;
    border-radius: inherit;
    height: 100%;
    max-width: 100%;
    -webkit-transition: all .3s;
    transition: all .3s;
}

.gc-free-shipping-progress .gc-progress-value {
    position: absolute;
    top: 50%;
    right: 0;
    -webkit-transform: translate(50%, -50%);
    -ms-transform: translate(50%, -50%);
    transform: translate(50%, -50%);
    background: #fff;
    border: 1px solid var(--gc-purple);
    border-radius: 50%;
    font-size: 12px;
    width: 26px;
    height: 26px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
    z-index: 1;
}

.gc-free-shipping-progress .gc-progress-value svg {
    width: 17px;
    height: 17px;
    fill: var(--gc-purple);
}

.gc-free-shipping-progress .gc-progress-value svg * {
    fill: var(--gc-purple);
}

.free-shipping-success .gc-progress-bar:before {
    background: var(--gc-success);
}

.free-shipping-success .gc-progress-value svg * {
    fill: var(--gc-success);
}

.free-shipping-success .gc-progress-value {
    border-color: var(--gc-success);
}

.gc-cart-goal-text {
    font-size: 12px;
    margin-bottom: 15px;
    color: var(--gc-dark);
    font-weight: 500;
}

.gc-cart-goal-text .woocommerce-Price-amount.amount {
    color: var(--gc-purple);
    font-weight: 600;
}

.shakeY {
    -webkit-animation-name: shakeY;
    animation-name: shakeY;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-delay: .6s;
    animation-delay: .6s;
}

@-webkit-keyframes shakeY {

    0%,
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0)
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0)
    }
}

@keyframes shakeY {

    0%,
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0)
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0)
    }
}

/* Cart free shipping calculator progressbar */




/* cart-totals */
.gc-cart-totals,
.gc-order-review {
    padding: 30px;
    border: 2px solid var(--gc-border);
    padding: 30px;
    border: 2px solid var(--gc-purple);
    position: relative;
    background: #fff;
    padding: 2rem 2rem 1rem;
    margin: 0 15px;

    &:before {
        content: "";
        position: absolute;
        top: -1rem;
        left: -1rem;
        display: block;
        background-color: #ffffff;
        background-image: linear-gradient(45deg, transparent 49%, var(--gc-purple) 50%, transparent 51%);
        background-size: 5px 5px;
        right: -1rem;
        bottom: -1rem;
        z-index: -1;
    }

    .gc-cart-totals-inner {
        margin: 0px 0 20px;
    }

    .gc-cart-total-title {
        margin: 0;
        text-transform: uppercase;
        font-size: 18px;
        font-weight: 600;
    }

    .gc-cart-total {
        color: var(--gc-black);
        overflow: hidden;
        padding-top: 15px;
        margin-top: 0px;
        margin-bottom: 15px;
        text-transform: uppercase;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .gc-cart-total {

        .cart-total-value,
        strong {
            color: var(--gc--black);
            font-weight: 500;
        }
    }

    .gc-shipping-calculator-button {
        color: var(--gc-gray-dark);
        transition: "background-color 0.3s ease, color 0.3s ease",
    }


    &.cart_totals {
        @media (max-width: 992px) {
            margin-top: 60px;
        }
    }

    .shipping-calculator {
        a {
            color: var(--gc-black);
            display: flex;
            margin-bottom: 10px;

            .dropdown-btn {
                margin-left: 10px;
                margin-top: 3px;
                font-size: 12px;
            }
        }
    }

    .gc-wc-proceed-to-checkout {
        a {
            background: var(--gc-purple);
        }
    }

}


.gc-checkout-review-order-table {

    .gc-cart-items,
    .gc-checkout-review-order-footer {
        flex-wrap: wrap;
        flex-direction: column;
    }
}

.gc-checkout-review-order-table .gc-cart-item,
.gc-checkout-review-order-table .gc-checkout-footer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

}

.gc-checkout-review-order-table .gc-checkout-review-order-footer #shipping_method {
    margin: 0;
    list-style: none;
}

.gc-checkout-review-order-table .gc-checkout-review-order-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--gc-border);
}

.gc-checkout-review-order-table .gc-cart-items {
    margin-top: -10px;
}

.gc-checkout-footer-item.order-total,
.gc-checkout-review-order-table .gc-cart-item {
    padding-top: 10px;
    margin-top: 10px;
    border-top: 1px solid var(--gc-border);
}

.gc-checkout-footer-item.order-total {
    text-transform: uppercase;
    color: var(--gc-black);
    /* margin-bottom: 20px; */
}

.gc-checkout-review-order-table .gc-cart-item .gc-product-name {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.gc-checkout-review-order-table .gc-cart-item .product-name,
.gc-checkout-review-order-table .gc-cart-item .product-img {
    margin-right: 10px;
}

.gc-checkout-review-order-table .gc-cart-item .product-img img {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
}


.shipping-totals {
    font-size: 16px;
    color: var(--e-global-color-text);

    &.shipping {
        display: flex;
        gap: 10px;
        margin-top: 10px;
        flex-direction: column;
        flex-wrap: nowrap;
        align-content: center;
        align-items: baseline;
    }
}

ul.shipping-methods,
ul.payment_methods {
    list-style-type: none;
    padding: 0;

    li {
        list-style-type: none;
    }
}


ul.payment_methods.methods {
    li {
        label {
            color: var(--gc-black);
            font-weight: 500;
            width: auto;
        }
    }
}

.privacy-policy-text p {
    font-size: 12px;
    margin: 0px 0 24px;
    border-style: solid;
    border-width: 1px;
    border-color: var(--gc-gray);
    padding: 15px;
    border-radius: 3px;
}

.place_order {
    width: 100%;
    min-height: 48px;
    margin-top: 40px;
    background-color: var(--gc-purple);
    transition: "background-color 0.3s ease, color 0.3s ease",
}

.coupon-dialog {
    .couponTile {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      transition: border 0.3s ease-in-out;
      cursor: pointer;
  
      &:hover {
        border-color: green;
      }
  
      &.selectedCouponTile {
        border: 1px solid green;
      }
    }
  
    .dialogContent {
      min-width: 30vw;
      padding: 24px;
    }
  
    .applyButton {
      color: #fff;
      background-color: #ddd;
      cursor: not-allowed;
      transition: background-color 0.3s ease, color 0.3s ease;
  
      &.active {
        background-color: #000;
        cursor: pointer;
  
        &:hover {
          background-color: #333;
        }
      }
    }
  
    .closeIcon {
      position: absolute;
      top: 8px;
      right: 8px;
      color: #6c5ebc;
      cursor: pointer;
    }
  
    .resultMsg {
      font-size: 14px;
      margin-top: 8px;
      min-width: 30vw;
  
      &.success {
        color: green;
      }
  
      &.error {
        color: red;
      }
    }
  
    .dialogPaper {
      background-color: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      padding: 16px;
    }
  
    .backdrop {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }