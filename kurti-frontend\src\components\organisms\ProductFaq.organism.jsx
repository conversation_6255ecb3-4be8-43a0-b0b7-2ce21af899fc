import React, { useState, useEffect } from "react";
import { useFaqsQuery } from "api/faq.api";
import { Accordion, Card } from "react-bootstrap";
import { IconPlus, IconMinus } from "utils/icons";

const ProductFaqOrganism = () => {
  const [activeKey, setActiveKey] = useState(null);
  const { data: faqs = [], isLoading } = useFaqsQuery({ category: "Product" });

  const handleAccordionToggle = (eventKey) => {
    setActiveKey(activeKey === eventKey ? null : eventKey);
  };

  if (isLoading) {
    return (
      <div className="product-faq-section">
        <h3 className="section-title">Frequently Asked Questions</h3>
        <div className="loading-skeleton">
          <div className="skeleton-line"></div>
          <div className="skeleton-line"></div>
          <div className="skeleton-line"></div>
        </div>
      </div>
    );
  }

  if (!faqs || faqs.length === 0) {
    return null;
  }

  return (
    <div className="product-faq-section mt-5">
      <h3 className="section-title mb-4">Frequently Asked Questions</h3>
      <div className="faq-container">
        <Accordion activeKey={activeKey} className="product-faq-accordion">
          {faqs.map((faq, index) => (
            <Card key={faq._id || index} className="faq-card mb-3">
              <Card.Header 
                className="faq-header d-flex justify-content-between align-items-center"
                onClick={() => handleAccordionToggle(index.toString())}
              >
                <span className="faq-question">{faq.question}</span>
                <span className="faq-icon">
                  {activeKey === index.toString() ? <IconMinus /> : <IconPlus />}
                </span>
              </Card.Header>
              <Accordion.Collapse eventKey={index.toString()}>
                <Card.Body className="faq-answer">
                  {faq.answer}
                </Card.Body>
              </Accordion.Collapse>
            </Card>
          ))}
        </Accordion>
      </div>
    </div>
  );
};

export default ProductFaqOrganism;
