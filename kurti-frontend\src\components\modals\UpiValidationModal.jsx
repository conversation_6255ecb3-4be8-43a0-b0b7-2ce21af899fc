import React from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from '@mui/material';
import { Link } from 'react-router-dom';

const UpiValidationModal = ({ open, onClose }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="upi-validation-dialog-title"
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle id="upi-validation-dialog-title" style={{ backgroundColor: '#f8d7da', color: '#721c24' }}>
        UPI ID Required
      </DialogTitle>
      <DialogContent>
        <Typography variant="body1" style={{ marginTop: '16px', marginBottom: '16px' }}>
          To process your return request, you need to add a UPI ID to your profile. This is required for refund processing.
        </Typography>
        <Typography variant="body2" style={{ marginBottom: '16px' }}>
          Please update your profile with a valid UPI ID before proceeding with the return request.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button 
          onClick={onClose} 
          style={{ color: 'white', backgroundColor: '#6c757d', marginRight: '8px' }}
        >
          Cancel
        </Button>
        <Button 
          component={Link} 
          to="/my-profile" 
          style={{ color: 'white', backgroundColor: 'rgb(108 94 188)' }}
        >
          Update Profile
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UpiValidationModal;
