const express = require('express');
const router = express.Router();
const MasterController = require('./MasterController');
const validations = require('./MasterValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');

router.put('/size-type/:dataId',  MasterController.updateSize);
router.get('/size-type',  MasterController.getSize);
router.post('/size-type', verifyTokenUserOrAdmin, validate(validations.addSize), MasterController.addSize);

router.put('/caption-tag/:dataId',  MasterController.updateCaptionTag);
router.get('/caption-tag',  MasterController.getCaptionTag);
router.post('/caption-tag', verifyTokenUserOrAdmin, validate(validations.addCaptionTag), MasterController.addCaptionTag);

router.put('/cloth-length/:dataId',  MasterController.updateClothLength);
router.get('/cloth-length',  MasterController.getClothLength);
router.post('/cloth-length', verifyTokenUserOrAdmin, validate(validations.addClothLength), MasterController.addClothLength);

router.put('/colors/:dataId',  MasterController.updateColor);
router.get('/colors',  MasterController.getColors);
router.post('/colors', verifyTokenUserOrAdmin, validate(validations.addColor), MasterController.addColor);

router.put('/discount/:dataId',  MasterController.updateDiscount);
router.get('/discount',  MasterController.getDiscountList);
router.post('/discount', verifyTokenUserOrAdmin, validate(validations.addDiscount), MasterController.addDiscount);

router.put('/fabric/:dataId',  MasterController.updateFabric);
router.get('/fabric',  MasterController.getFabrics);
router.post('/fabric', verifyTokenUserOrAdmin, validate(validations.addFabric), MasterController.addFabric);

router.put('/sections/:dataId',  MasterController.updateSection);
router.get('/sections',  MasterController.getSections);
router.post('/sections', verifyTokenUserOrAdmin, validate(validations.addSections), MasterController.addSections);

router.put('/occasions/:dataId',  MasterController.updateOccasion);
router.get('/occasions',  MasterController.getOccasions);
router.post('/occasions', verifyTokenUserOrAdmin, validate(validations.addOccasions), MasterController.addOccasions);

router.put('/patterns/:dataId',  MasterController.updatePattern);
router.get('/patterns',  MasterController.getPatterns);
router.post('/patterns', verifyTokenUserOrAdmin, validate(validations.addPattern), MasterController.addPattern);

router.put('/payment-modes/:dataId',  MasterController.updatePaymentMode);
router.get('/payment-modes',  MasterController.getPaymentMode);
router.post('/payment-modes', verifyTokenUserOrAdmin, validate(validations.addPaymentMode), MasterController.addPaymentMode);

router.put('/brands/:dataId',  MasterController.updateBrand);
router.get('/brands',  MasterController.getBrands);
router.post('/brands', verifyTokenUserOrAdmin,  MasterController.addBrand);

module.exports = router;
