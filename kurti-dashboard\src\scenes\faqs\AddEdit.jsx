import {
  Box,
  Button,
  TextField,
  useTheme,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
import Header from "components/Header";
import { faqInitialValues, faqValidationSchema } from "constants/schemas/faq";
import { useFormik } from "formik";
import { useNavigate, useParams } from "react-router-dom";
import {
  useCreateFaqMutation,
  useGetFaqQuery,
  useUpdateFaqMutation,
  useGetFaqCategoriesQuery,
} from "services";
import dataGridStyles from "../../styles/dataGridStyles";
import { toast } from "react-toastify";
import { useEffect, useState } from "react";

const AddEdit = () => {
  const isNonMobile = useMediaQuery("(min-width:600px)");
  const { id: faqId } = useParams();
  const theme = useTheme();
  const styles = dataGridStyles(theme.palette.mode);
  const navigate = useNavigate();
  const [newCategory, setNewCategory] = useState("");

  // Queries
  const { data: faqData, isLoading: isFaqLoading } = useGetFaqQuery(faqId, {
    skip: !faqId,
    refetchOnMountOrArgChange: true,
  });
  
  const { data: categoriesData = [] } = useGetFaqCategoriesQuery();

  // Mutations
  const [createFaq, { isLoading: isCreating }] = useCreateFaqMutation();
  const [updateFaq, { isLoading: isUpdating }] = useUpdateFaqMutation();

  const savedFaqValues = {
    question: faqData?.question || "",
    answer: faqData?.answer || "",
    category: faqData?.category || "General",
    sortOrder: faqData?.sortOrder || 0,
  };

  const formik = useFormik({
    initialValues: { ...faqInitialValues, ...savedFaqValues },
    enableReinitialize: true,
    validationSchema: faqValidationSchema,
    onSubmit: (values) => {
      if (faqId) {
        updateFaq({
          id: faqId,
          body: values,
        })
          .unwrap()
          .then(() => {
            toast.success("FAQ updated successfully");
            navigate("/faqs");
          })
          .catch((error) => {
            toast.error("Failed to update FAQ");
          });
      } else {
        createFaq(values)
          .unwrap()
          .then(() => {
            formik.resetForm();
            toast.success("FAQ created successfully");
            navigate("/faqs");
          })
          .catch((error) => {
            toast.error("Failed to create FAQ");
          });
      }
    },
  });

  const handleAddCategory = () => {
    if (newCategory && !categoriesData.includes(newCategory)) {
      formik.setFieldValue("category", newCategory);
      setNewCategory("");
    }
  };

  const isLoading = isFaqLoading || isCreating || isUpdating;

  return (
    <Box m="20px">
      <Box sx={styles.mainHeadings}>
        <Header
          title={`${faqId ? "EDIT" : "CREATE"} FAQ`}
          subtitle={`${faqId ? "Edit an existing FAQ" : "Create a new FAQ"}`}
        />
        {faqId && (
          <Box display="flex" justifyContent="end">
            <Button
              onClick={() => navigate("/faqs")}
              color="secondary"
              variant="contained"
              sx={styles.buttonMd}
            >
              Back to FAQs
            </Button>
          </Box>
        )}
      </Box>
      <form onSubmit={formik.handleSubmit}>
        <Box
          sx={{
            ...styles.formContainer,
          }}
          className="extra-space"
        >
          <Box
            display="grid"
            gap="30px"
            gridTemplateColumns="repeat(4, minmax(0, 1fr))"
            sx={{
              "& > div": {
                gridColumn: isNonMobile ? undefined : "span 4",
              },
            }}
          >
            <TextField
              fullWidth
              variant="filled"
              type="text"
              label="Question"
              name="question"
              value={formik.values.question}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.question && Boolean(formik.errors.question)}
              helperText={formik.touched.question && formik.errors.question}
              sx={{ gridColumn: "span 4" }}
              disabled={isLoading}
            />

            <TextField
              fullWidth
              variant="filled"
              type="text"
              label="Answer"
              name="answer"
              value={formik.values.answer}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.answer && Boolean(formik.errors.answer)}
              helperText={formik.touched.answer && formik.errors.answer}
              sx={{ gridColumn: "span 4" }}
              multiline
              rows={4}
              disabled={isLoading}
            />

            <FormControl 
              variant="filled" 
              sx={{ gridColumn: "span 2" }}
              error={formik.touched.category && Boolean(formik.errors.category)}
            >
              <InputLabel id="category-label">Category</InputLabel>
              <Select
                labelId="category-label"
                name="category"
                value={formik.values.category}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                disabled={isLoading}
              >
                {categoriesData.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
                {formik.values.category && !categoriesData.includes(formik.values.category) && (
                  <MenuItem value={formik.values.category}>
                    {formik.values.category}
                  </MenuItem>
                )}
              </Select>
              {formik.touched.category && formik.errors.category && (
                <FormHelperText>{formik.errors.category}</FormHelperText>
              )}
            </FormControl>

            <Box sx={{ gridColumn: "span 2", display: "flex", gap: 1, alignItems: "start" }}>
              <TextField
                fullWidth
                variant="filled"
                type="text"
                label="New Category"
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                disabled={isLoading}
              />
              <Button
                variant="contained"
                color="secondary"
                onClick={handleAddCategory}
                disabled={!newCategory || isLoading}
                sx={{...styles.buttonxs, mt: 1}}
              >
                Add
              </Button>
            </Box>

            <TextField
              fullWidth
              variant="filled"
              type="number"
              label="Sort Order"
              name="sortOrder"
              value={formik.values.sortOrder}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.sortOrder && Boolean(formik.errors.sortOrder)}
              helperText={formik.touched.sortOrder && formik.errors.sortOrder}
              sx={{ gridColumn: "span 2" }}
              disabled={isLoading}
            />
          </Box>
          <Box display="flex" justifyContent="end" mt="20px">
            <Button
              type="button"
              color="info"
              variant="contained"
              sx={{ ...styles.buttonMd, mr: 2 }}
              onClick={() => navigate("/faqs")}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="secondary"
              variant="contained"
              sx={styles.buttonMd}
              disabled={isLoading}
            >
              {faqId ? "Update" : "Create"}
            </Button>
          </Box>
        </Box>
      </form>
    </Box>
  );
};

export default AddEdit;
