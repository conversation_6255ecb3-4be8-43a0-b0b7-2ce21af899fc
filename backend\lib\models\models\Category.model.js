const mongoose = require('mongoose'),
    Schema = mongoose.Schema,
    ObjectId = mongoose.Types.ObjectId;

const CategorySchema = new Schema(
    {
        name: {
            type: String,
            trim: true,
            required: true,
        },
        slug: {
            type: String,
            trim: true,
        },
        image: {
            type: String,
            trim: true,
        },
        sizeChart: {
            type: String,
            trim: true,
            default: '',
        },
        adBanner: [{
            image: {
                type: String,
                trim: true,
                required: false,
                default: ''
            },
            redirectURL: {
                type: String,
                trim: true,
                required: false,
                default: ''
            },
        }],
        description: {
            type: String,
            trim: true,
        },
        parentId: {
            type: ObjectId,
            default: null,
        },
        hasChild: {
            type: Boolean,
            default: false,
        },
        isSuspended: {
            type: Boolean,
            default: false,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: {
            createdAt: 'created',
            updatedAt: 'updated',
        },
        id: false,
        toJSON: {
            getters: true,
        },
        toObject: {
            getters: true,
        },
    }
);

module.exports = mongoose.model('Category', CategorySchema);
