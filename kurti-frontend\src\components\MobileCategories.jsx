import { useCategoriesQuery } from "api/categories.api";
import LazyLoad from "react-lazyload";
import { Link } from "react-router-dom";

const MobileCategories = () => {
  const { data: categories = [] } = useCategoriesQuery();

  return (
    <div className="container my-3 woobanner">
      <h3 className="text-center">Our Categories</h3>
      <div className="row">
        {categories?.length > 0 &&
          categories.map((category) => (
            <div className="col-6 m-0" key={category?._id} style={{padding: "0.2rem"}}>
              <Link to={`/category?catId=${category?._id}`}>
                <div className="gc-woo-banner-wrapper">
                  <LazyLoad height={200} once>
                    <img
                      src={category?.image}
                      alt={category?.name}
                      className="img-fluid"
                    />
                  </LazyLoad>
                  <div className="gc-banner-content">
                    <div className="gc-banner-content-top"></div>
                    <div className="gc-banner-content-center"></div>
                    <div className="gc-banner-content-bottom">
                      <h4 className="gc-banner-title banner-content-item">
                        {category?.name}
                      </h4>
                      <span className="gc-banner-catcount banner-content-item">
                        {category?.description}
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
      </div>
    </div>
  );
};

export default MobileCategories;
