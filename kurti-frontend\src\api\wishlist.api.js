import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "api/api-client";
import { API_ENDPOINTS } from "globals/endpoints";
import { getAuthToken } from "stores/user";

export const useWishlistQuery = (token) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_WISHLIST, {
        headers: {
          Authorization: token,
        },
      });
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["wishlist"],
    enabled: !!token,
  });

export const useUpdateWishlistMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(
        API_ENDPOINTS.UPDATE_WISHLIST,
        payload?.data,
        {
          headers: {
            Authorization: payload?.token,
          },
        }
      );
      return response;
    },
  });

export const useRemoveWishlistMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const token = getAuthToken();
      if(!token) return;
      const response = await apiClient.delete(
        `${API_ENDPOINTS.REMOVE_WISHLIST}/${payload}`
      );
      return response;
    },
  });
