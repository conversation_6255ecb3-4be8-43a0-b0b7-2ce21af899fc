import { InputAdornment, TextField, IconButton } from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import CloseIcon from "@mui/icons-material/Close";
import { useCouponsQuery, useValidateCouponMutation } from "api/coupon.api";
import { useState } from "react";
import { toast } from "react-toastify";

const CouponModal = ({ show, handleClose, handleSubmit, cartStoredData }) => {
  const { data: { items: couponsData = [] } = {} } = useCouponsQuery({
    params: { page: 1, limit: 50, status: "Active" },
  });

  const { mutateAsync: checkCoupon } = useValidateCouponMutation();

  const [selectedCoupon, setSelectedCoupon] = useState({});
  const [resultMsg, setResultMsg] = useState({
    type: "",
    msg: "",
  });
  const [isApplyButtonActive, setIsApplyButtonActive] = useState(false);

  const handleCheckCoupon = async () => {
    try {
      const productsInCart = cartStoredData?.products?.map((product) => {
        return {
          productId: product?.productId?._id,
          category:
            product?.productId?.categoryId ?? "665a8de4e247c9d5473738f7",
          quantity: product?.quantity,
        };
      });
      const payload = {
        couponCode: selectedCoupon?.code,
        cartTotal: cartStoredData?.totalPrice ?? 0,
        productsInCart,
      };
      const result = await checkCoupon(payload);
      if (result?.success) {
        setResultMsg({
          type: "success",
          msg: result?.data?.message,
        });
        setSelectedCoupon({
          ...selectedCoupon,
          savingAmount: result?.data?.discountAmount,
        });
        setIsApplyButtonActive(true); // Activate the apply button after successful validation
      }
    } catch (err) {
      setResultMsg({
        type: "error",
        msg: err?.response?.data?.message,
      });
      console.log(err);
    }
  };

  const onClose = () => {
    handleClose();
    setSelectedCoupon({});
    setResultMsg({ type: "", msg: "" });
    setIsApplyButtonActive(false); // Reset the apply button
  };

  return (
    <Dialog
      className="coupon-dialog"
      open={show}
      onClose={onClose}
      scroll="paper"
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
      PaperProps={{
        className: "dialogPaper",
      }}
      BackdropProps={{
        className: "backdrop",
      }}
    >
      <DialogTitle id="scroll-dialog-title">
        Apply Coupon
        <IconButton onClick={onClose} className="closeIcon">
          <CloseIcon />
        </IconButton>
        <hr />
        <TextField
          fullWidth
          placeholder="Enter coupon code"
          value={selectedCoupon?.code ?? ""}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <Button
                  sx={{
                    color: "#6C5EBC",
                  }}
                  onClick={handleCheckCoupon}
                  disabled={!selectedCoupon?.code}
                >
                  Check
                </Button>
              </InputAdornment>
            ),
          }}
        />
        {resultMsg?.msg && (
          <div className={`resultMsg ${resultMsg.type}`}>{resultMsg?.msg}</div>
        )}
      </DialogTitle>
      <DialogContent dividers={scroll === "paper"} className="dialogContent">
        {couponsData?.length > 0 &&
          couponsData.map((couponItem, idx) => {
            return (
              <div
                key={idx}
                className={`couponTile ${
                  couponItem?.code === selectedCoupon?.code
                    ? "selectedCouponTile"
                    : ""
                }`}
                onClick={() => {
                  setSelectedCoupon(couponItem);
                  setResultMsg({ type: "", msg: "" });
                  setIsApplyButtonActive(false); // Reset the apply button when a new coupon is selected
                }}
              >
                <h6>{couponItem.code}</h6>
                <div>
                  <strong>Discount:</strong> {couponItem.discountValue}{" "}
                  {couponItem.discountType === "flat" ? "INR" : "%"}
                </div>
                <div>
                  <strong>Minimum Cart Value:</strong> {couponItem.minCartValue}{" "}
                  INR
                </div>
                <div>
                  <strong>Expiry Date:</strong>{" "}
                  {new Date(couponItem.expiry).toLocaleDateString()}
                </div>
                <span>
                  <strong>Description:</strong> {couponItem.description}
                </span>
              </div>
            );
          })}
      </DialogContent>
      <DialogActions
        sx={{
          display: "flex",
          justifyContent: "space-between",
          mx: 3,
          color: "#6C5EBC",
        }}
      >
        <p>Maximum Savings: {selectedCoupon?.savingAmount ?? 0} INR</p>
        <div>
          <Button
            className={`applyButton ${isApplyButtonActive ? "active" : ""}`}
            onClick={() => {
              if (isApplyButtonActive) {
                handleSubmit({
                  ...selectedCoupon,
                  calculateDiscount: selectedCoupon?.savingAmount,
                });
                onClose();
              }
            }}
            disabled={!isApplyButtonActive}
            style={{
              backgroundColor: "#6c5ebc",
              color: "white",
              border: "none",
            }}
            variant=""
          >
            Apply
          </Button>
        </div>
      </DialogActions>
    </Dialog>
  );
};

export default CouponModal;
