const mongoose = require('mongoose');

const ContactUsSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
    },
    type: {
        type: String,
        default: '',
    },
    message: {
        type: String,
        default: '',
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

// Create and export the SizeType model
const ContactUs = mongoose.model('ContactUs', ContactUsSchema);

module.exports = ContactUs;
