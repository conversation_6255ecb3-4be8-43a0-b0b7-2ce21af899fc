import React from "react";

const ProductSizeListMolecule = ({
  size = [],
  selectedSize,
  onSelect,
  onClear,
  availableSizes = [],
}) => {
  const handleSizeClick = (s) => {
    if (onSelect) onSelect(s);
  };
  return (
    <div className="gc-terms gc-type-button terms-shape-default outline-0">
      {size.length > 0 &&
        size.map((s, index) => {
          if (availableSizes?.includes(s)) {
            return (
              <button
                key={index}
                className={`gc-term ${
                  selectedSize === s ? "bg-dark text-light" : ""
                }`}
                onClick={() => handleSizeClick(s)}
              >
                {s}
              </button>
            );
          }
        })}
    </div>
  );
};

export default ProductSizeListMolecule;
