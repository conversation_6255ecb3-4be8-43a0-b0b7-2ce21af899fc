const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const CouponSchema = new Schema({
    code: {
        type: String,
        required: true,
        unique: true,
    },
    description: {
        type: String,
        default: '',
    },
    discountType: {
        type: String,
        enum: ['flat', 'percentage', 'bundle'],
        required: true,
    },
    discountValue: {
        type: Number,
        required: function() {
            return this.discountType !== 'bundle';
        },
    },
    maxDiscount: {
        type: Number,
        required: function() {
            return this.discountType === 'percentage';
        },
    },
    bundleDetails: {
        category: {
            type: String,
            required: function() {
                return this.discountType === 'bundle';
            },
            ref: 'Category'
        },
        quantity: {
            type: Number,
            required: function() {
                return this.discountType === 'bundle';
            },
            min: 1,
        },
        bundlePrice: {
            type: Number,
            required: function() {
                return this.discountType === 'bundle';
            },
        },
    },
    expiry: {
        type: Date,
        required: true,
    },
    numberOfUses: {
        type: Number,
        required: true,
        default: 0,
    },
    maxUses: {
        type: Number,
        required: true,
        default: null,
    },
    minCartValue: {
        type: Number,
        required: function() {
            return this.discountType !== 'bundle';
        },
    },
    status: {
        type: String,
        enum: ['Active', 'Inactive'],
        default: 'Inactive',
    },
    createdAt: {
        type: Date,
        default: Date.now,
    },
});

module.exports = mongoose.model('Coupon', CouponSchema);
