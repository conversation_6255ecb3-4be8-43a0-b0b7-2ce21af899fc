const {
    models: { Faq },
} = require('../../../../lib/models');

class FaqController {
    async list(req, res) {
        try {
            const { page = 1, limit = 10, search, category, sortBy = 'sortOrder', sortOrder = 'asc', isAllTrue = false } = req.query;
            
            // Base query object
            let query = {
                isDeleted: false,
            };
            
            // Add search criteria if provided
            if (search) {
                const searchRegex = new RegExp(search, 'i');
                query.$or = [
                    { question: searchRegex },
                    { answer: searchRegex }
                ];
            }
            
            // Add category filter if provided
            if (category) {
                query.category = category;
            }
            
            // For frontend, only return active FAQs
            if (!isAllTrue) {
                query.isSuspended = false;
            }
            
            // Calculate pagination
            const skip = (parseInt(page) - 1) * parseInt(limit);
            
            // Determine sort direction
            const sort = {};
            sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
            
            // Execute query with pagination
            const faqs = await Faq.find(query)
                .sort(sort)
                .skip(skip)
                .limit(parseInt(limit));
                
            // Get total count for pagination
            const totalCount = await Faq.countDocuments(query);
            
            // Prepare response
            const response = {
                items: faqs,
                count: totalCount,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(totalCount / parseInt(limit))
            };
            
            return res.success(response, req.__('FAQS_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error retrieving FAQs:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async detail(req, res) {
        try {
            const { id } = req.params;
            
            const faq = await Faq.findOne({
                _id: id,
                isDeleted: false
            });
            
            if (!faq) {
                return res.warn({}, req.__('FAQ_NOT_FOUND'));
            }
            
            return res.success(faq, req.__('FAQ_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error retrieving FAQ:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async add(req, res) {
        try {
            const { question, answer, category, sortOrder } = req.body;
            
            const faqData = {
                question,
                answer,
                category: category || 'General',
                sortOrder: sortOrder || 0
            };
            
            const faq = new Faq(faqData);
            const newFaq = await faq.save();
            
            return res.success(newFaq, req.__('FAQ_ADDED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error adding FAQ:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async update(req, res) {
        try {
            const { id } = req.params;
            const { question, answer, category, sortOrder } = req.body;
            
            const faq = await Faq.findOne({
                _id: id,
                isDeleted: false
            });
            
            if (!faq) {
                return res.warn({}, req.__('FAQ_NOT_FOUND'));
            }
            
            // Update fields
            if (question) faq.question = question;
            if (answer) faq.answer = answer;
            if (category) faq.category = category;
            if (sortOrder !== undefined) faq.sortOrder = sortOrder;
            
            const updatedFaq = await faq.save();
            
            return res.success(updatedFaq, req.__('FAQ_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error updating FAQ:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async delete(req, res) {
        try {
            const { id } = req.params;
            
            const faq = await Faq.findOne({
                _id: id,
                isDeleted: false
            });
            
            if (!faq) {
                return res.warn({}, req.__('FAQ_NOT_FOUND'));
            }
            
            // Soft delete
            faq.isDeleted = true;
            await faq.save();
            
            return res.success({}, req.__('FAQ_DELETED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error deleting FAQ:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async toggleStatus(req, res) {
        try {
            const { id } = req.params;
            
            const faq = await Faq.findOne({
                _id: id,
                isDeleted: false
            });
            
            if (!faq) {
                return res.warn({}, req.__('FAQ_NOT_FOUND'));
            }
            
            // Toggle status
            faq.isSuspended = !faq.isSuspended;
            await faq.save();
            
            const statusMessage = faq.isSuspended ? 'FAQ_DEACTIVATED_SUCCESSFULLY' : 'FAQ_ACTIVATED_SUCCESSFULLY';
            
            return res.success({}, req.__(statusMessage));
        } catch (error) {
            console.error('Error toggling FAQ status:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    
    async getCategories(req, res) {
        try {
            // Get distinct categories from FAQs
            const categories = await Faq.distinct('category', { isDeleted: false });
            
            return res.success(categories, req.__('FAQ_CATEGORIES_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error retrieving FAQ categories:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
}

module.exports = new FaqController();
