import React from "react";

const CustomAccorion = () => {
  return (
    <Accordion defaultActiveKey="1">
      <Accordion.Item eventKey="0">
        <Accordion.Header>Filter By Price</Accordion.Header>
        <Accordion.Body>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
          eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
          minim veniam, quis nostrud exercitation ullamco laboris nisi ut
          aliquip ex ea commodo consequat. Duis aute irure dolor in
          reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
          pariatur. Excepteur sint occaecat cupidatat non proident, sunt in
          culpa qui officia deserunt mollit anim id est laborum.
        </Accordion.Body>
      </Accordion.Item>
      <Accordion.Item eventKey="1">
        <Accordion.Header>Filter By Color</Accordion.Header>
        <Accordion.Body>
          <ul className="woocommerce-widget-layered-nav-list">
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#000000" }}
                ></span>
                Black
              </a>{" "}
              <span className="widget-list-span">41</span>
            </li>
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#1e73be" }}
                ></span>
                Blue
              </a>{" "}
              <span className="widget-list-span">8</span>
            </li>
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#81d742" }}
                ></span>
                Green
              </a>{" "}
              <span className="widget-list-span">7</span>
            </li>
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#dd3333" }}
                ></span>
                Red
              </a>{" "}
              <span className="widget-list-span">7</span>
            </li>
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#eeee22" }}
                ></span>
                Yellow
              </a>{" "}
              <span className="widget-list-span">7</span>
            </li>
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#a87323" }}
                ></span>
                brown
              </a>{" "}
              <span className="widget-list-span">1</span>
            </li>
            <li className="woocommerce-widget-layered-nav-list__item wc-layered-nav-term ">
              <a rel="nofollow" href="#">
                <span
                  className="gc-swatches-widget-color-item"
                  style={{ backgroundColor: "#f7f7f7" }}
                ></span>
                white
              </a>{" "}
              <span className="widget-list-span">1</span>
            </li>
          </ul>
        </Accordion.Body>
      </Accordion.Item>
      <Accordion.Item eventKey="2">
        <Accordion.Header>Product Status</Accordion.Header>
        <Accordion.Body>
          <ul>
            <li>
              <a href="#">
                <input
                  name="stockonsale"
                  value="instock"
                  id="instock"
                  type="checkbox"
                />
                <label>
                  <span></span>In Stock
                </label>
              </a>
            </li>
            <li>
              <a href="#">
                <input
                  name="stockonsale"
                  value="onsale"
                  id="onsale"
                  type="checkbox"
                />
                <label>
                  <span></span>On Sale
                </label>
              </a>
            </li>
          </ul>
        </Accordion.Body>
      </Accordion.Item>
    </Accordion>
  );
};

export default CustomAccorion;
