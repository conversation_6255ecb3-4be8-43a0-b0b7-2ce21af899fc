import { auth } from "config/firebaseConfig";
import {
  sendEmailVerification,
  signInWithEmailAndPassword,
} from "firebase/auth";

const FirebaseEmailLogin = () => {
  const email = "<EMAIL>";
  const password = "test1234";
  const handleSignIn = async () => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;
      if (user && !user.emailVerified) {
        await sendEmailVerification(user);
        alert("Verification email sent. Please check your inbox.");
      } else if (user && user.emailVerified) {
        alert("Successfully signed in!");
      }
    } catch (error) {
      console.error("Error signing in:", error);
      alert("Error signing in: " + error.message);
    }
  };
  return (
    <div className="form-group">
      <button type="button" className="btn btn-black" onClick={handleSignIn}>
        Firebase Sign In
      </button>
    </div>
  );
};

export default FirebaseEmailLogin;
