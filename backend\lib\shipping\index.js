const { ShipRocketStatusCodes } = require("../models/enums");


function getStatusText(statusCode) {
    const statusMap = {
        [ShipRocketStatusCodes.PENDING]: 'Pending',
        [ShipRocketStatusCodes.CANCELED_BEFORE_SHIP]: 'Canceled Before Ship',
        [ShipRocketStatusCodes.SHIPPED]: 'Shipped',
        [ShipRocketStatusCodes.DELIVERED]: 'Delivered',
        [ShipRocketStatusCodes.CANCELED]: 'Canceled',
        [ShipRocketStatusCodes.RTO_INITIATED]: 'RTO Initiated',
        [ShipRocketStatusCodes.RTO_DELIVERED]: 'RTO Delivered',
        [ShipRocketStatusCodes.LOST]: 'Lost',
        [ShipRocketStatusCodes.PICKUP_ERROR]: 'Pickup Error',
        [ShipRocketStatusCodes.RTO_ACKNOWLEDGED]: 'RTO Acknowledged',
        [ShipRocketStatusCodes.PICKUP_RESCHEDULED]: 'Pickup Rescheduled',
        [ShipRocketStatusCodes.CANCELLATION_REQUESTED]: 'Cancellation Requested',
        [ShipRocketStatusCodes.OUT_FOR_DELIVERY]: 'Out For Delivery',
        [ShipRocketStatusCodes.IN_TRANSIT]: 'In Transit',
        [ShipRocketStatusCodes.OUT_FOR_PICKUP]: 'Out For Pickup',
        [ShipRocketStatusCodes.PICKUP_EXCEPTION]: 'Pickup Exception',
        [ShipRocketStatusCodes.UNDELIVERED]: 'Undelivered',
        [ShipRocketStatusCodes.DELAYED]: 'Delayed',
        [ShipRocketStatusCodes.PARTIAL_DELIVERED]: 'Partial Delivered',
        [ShipRocketStatusCodes.DESTROYED]: 'Destroyed',
        [ShipRocketStatusCodes.DAMAGED]: 'Damaged',
        [ShipRocketStatusCodes.FULFILLED]: 'Fulfilled',
        [ShipRocketStatusCodes.PICKUP_BOOKED]: 'Pickup Booked',
        [ShipRocketStatusCodes.REACHED_AT_DESTINATION_HUB]: 'Reached at Destination Hub',
        [ShipRocketStatusCodes.MISROUTED]: 'Misrouted',
        [ShipRocketStatusCodes.RTO_NDR]: 'RTO NDR',
        [ShipRocketStatusCodes.RTO_OFD]: 'RTO Out For Delivery',
        [ShipRocketStatusCodes.PICKED_UP]: 'Picked Up',
        [ShipRocketStatusCodes.SELF_FULFILLED]: 'Self Fulfilled',
        [ShipRocketStatusCodes.DISPOSED_OFF]: 'Disposed Off',
        [ShipRocketStatusCodes.CANCELLED_BEFORE_DISPATCHED]: 'Cancelled Before Dispatched',
        [ShipRocketStatusCodes.RTO_IN_INTRANSIT]: 'RTO In Transit',
        [ShipRocketStatusCodes.QC_FAILED]: 'QC Failed',
        [ShipRocketStatusCodes.REACHED_WAREHOUSE]: 'Reached Warehouse',
        [ShipRocketStatusCodes.CUSTOM_CLEARED]: 'Custom Cleared',
        [ShipRocketStatusCodes.IN_FLIGHT]: 'In Flight',
        [ShipRocketStatusCodes.HANDOVER_TO_COURIER]: 'Handover to Courier',
        [ShipRocketStatusCodes.SHIPMENT_BOOKED]: 'Shipment Booked',
        [ShipRocketStatusCodes.IN_TRANSIT_OVERSEAS]: 'In Transit Overseas',
        [ShipRocketStatusCodes.CONNECTION_ALIGNED]: 'Connection Aligned',
        [ShipRocketStatusCodes.REACHED_OVERSEAS_WAREHOUSE]: 'Reached Overseas Warehouse',
        [ShipRocketStatusCodes.CUSTOM_CLEARED_OVERSEAS]: 'Custom Cleared Overseas',
        [ShipRocketStatusCodes.BOX_PACKING]: 'Box Packing',
        [ShipRocketStatusCodes.PROCESSED_AT_WAREHOUSE]: 'Processed at Warehouse',
        [ShipRocketStatusCodes.FC_ALLOCATED]: 'FC Allocated',
        [ShipRocketStatusCodes.PICKLIST_GENERATED]: 'Picklist Generated',
        [ShipRocketStatusCodes.READY_TO_PACK]: 'Ready to Pack',
        [ShipRocketStatusCodes.PACKED]: 'Packed',
        [ShipRocketStatusCodes.FC_MANIFEST_GENERATED]: 'FC Manifest Generated',
        [ShipRocketStatusCodes.HANDOVER_EXCEPTION]: 'Handover Exception',
        [ShipRocketStatusCodes.PACKED_EXCEPTION]: 'Packed Exception',
        [ShipRocketStatusCodes.RTO_LOCK]: 'RTO Lock',
        [ShipRocketStatusCodes.UNTRACEABLE]: 'Untraceable',
        [ShipRocketStatusCodes.ISSUE_RELATED_TO_THE_RECIPIENT]: 'Issue Related to the Recipient',
        [ShipRocketStatusCodes.REACHED_BACK_AT_SELLER_CITY]: 'Reached Back at Seller City',
    };

    return statusMap[statusCode] || 'Unknown Status'; // Default to 'Unknown Status' if not found
}

module.exports = { getStatusText };