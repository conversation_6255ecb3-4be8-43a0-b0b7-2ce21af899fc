const express = require('express');
const router = express.Router();
const WishlistController = require('./WishlistController');
const validations = require('./WishlistValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin } = require('../../util/auth');

router.delete('/:productId', verifyTokenUserOrAdmin, WishlistController.removeFromWishlist);
router.get('/', verifyTokenUserOrAdmin, WishlistController.getWishlist);
router.post('/', verifyTokenUserOrAdmin, WishlistController.addToWishlist);

module.exports = router;
