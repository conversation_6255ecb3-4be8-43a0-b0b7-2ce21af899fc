const { Joi, common } = require('../../util/validations');

const optionalId = Joi.object().keys({
    id: Joi.objectId()
        .valid()
        .optional(),
});

const updateProfile = Joi.object().keys({
    type: Joi.string()
        .trim()
        .valid('PROFILE_UPDATE', 'ACCOUNT_COMPLETE')
        .required(),
    fullName: Joi.string().when('type', {
        is: 'PROFILE_UPDATE',
        then: Joi.string()
            .trim()
            .min(3)
            .max(30)
            .required(),
    }),
    age: Joi.string()
        .when('type', {
            is: 'PROFILE_UPDATE',
            then: common.age,
        })
        .optional(),
    userName: Joi.string()
        .trim()
        .min(3)
        .max(30)
        .optional(),
    phone: Joi.string()
        .trim()
        .min(10)
        .max(13)
        .optional(),
    bio: Joi.string()
        .trim()
        .min(3)
        .optional()
        .allow(''),
    description: Joi.string()
        .trim()
        .min(3)
        .optional()
        .allow(''),
    avatar: Joi.string()
        .trim()
        .max(500)
        .optional()
        .allow(''),
    upiId: Joi.string()
        .trim()
        .regex(/^[a-zA-Z0-9.\-_]{2,256}@[a-zA-Z][a-zA-Z]{2,64}$/, 'upiIdPattern')
        .optional(),
    email: Joi.string()
        .trim()
        .email()
        .optional(),
});

const updatePassword = Joi.object().keys({
    currentPassword: Joi.string()
        .trim()
        .required(),
    newPassword: common.password,
});

const updateEmail = Joi.object().keys({
    currentPassword: Joi.string()
        .trim()
        .required(),
    email: common.email,
});

const updatePhone = Joi.object().keys({
    currentPassword: Joi.string()
        .trim()
        .required(),
    countryCode: common.countryCode,
    phone: common.phone,
});

const addAddress = Joi.object().keys({
    nickName: Joi.string()
        .trim()
        .min(3)
        .max(100)
        .allow(''),
    addressLine1: Joi.string()
        .trim()
        .required()
        .min(3)
        .max(100),
    addressLine2: Joi.string()
        .trim()
        .optional()
        .allow(''),
    city: Joi.string()
        .trim()
        .required()
        .min(2)
        .max(50),
    state: Joi.string()
        .trim()
        .required()
        .min(2)
        .max(50),
    postalCode: Joi.string()
        .trim()
        .required()
        .min(3)
        .max(10),
    country: Joi.string()
        .trim()
        .required()
        .min(2)
        .max(50),
    phone: Joi.string()
        .trim()
        .required()
        .min(10)
        .max(15),
    email: Joi.string()
        .trim()
        .required()
        .email(),
    fName: Joi.string()
        .trim()
        .optional()
        .min(2)
        .max(50),
    lName: Joi.string()
        .trim()
        .optional()
        .min(2)
        .max(50),
    cName: Joi.string()
        .trim()
        .min(2)
        .max(100)
        .optional()
        .allow(''),
    fullName: Joi.string()
        .trim()
        .required()
        .min(2)
        .max(50),
});

module.exports = {
    optionalId,
    updateProfile,
    updatePassword,
    updateEmail,
    updatePhone,
    addAddress,
};
