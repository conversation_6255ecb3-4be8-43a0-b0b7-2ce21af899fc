import { useOrdersQuery } from "api/order.api";
import { SETTINGS } from "globals";
import parse from "html-react-parser";
import { Card } from "react-bootstrap";

const MyOrders = () => {
  const { data: { items = [] } = {} } = useOrdersQuery();
  return (
    <div>
      <h1>Order Details</h1>
      <ul>
        {items.map((order) => (
          <div>
            <h3>Order ID: {order?.orderId}</h3>
            <p>
              <strong>Status:</strong> {order?.status}
            </p>
            {order?.products?.length > 0 &&
              order.products.map((item) => {
                return (
                  <Card className="my-3">
                    <div className="row gc-cart-item gc-align-center">
                      <div className="col-12 col-sm-6">
                        <div className="row gc-meta-left gc-flex gc-align-center">
                          <div className="col-xl-4 col-lg-5 col-md-4 col-6 product-thumbnail">
                            <img
                              loading="lazy"
                              decoding="async"
                              width="113"
                              height="150"
                              src={item?.mainImage}
                              className="attachment-thumbnail size-thumbnail"
                              alt=""
                            />
                          </div>
                          <div
                            className="col-xl-8 col-lg-7 col-md-8 col-6 product-name gc-small-title"
                            data-title="Product"
                          >
                            <span>{item?.productId?.name}</span>
                          </div>
                        </div>
                      </div>

                      <div className="col-12 col-sm-6">
                        <div className="row gc-meta-right gc-align-center">
                          <div
                            className="col-auto product-subtotal gc-price"
                            data-title="Subtotal"
                          >
                            <span className="woocommerce-Price-amount amount">
                              <bdi>
                                <span className="woocommerce-Price-currencySymbol">
                                  {parse(SETTINGS.CURRENCY)}
                                </span>
                                {item?.price * item?.quantity}
                              </bdi>
                            </span>{" "}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })}
          </div>
        ))}
      </ul>
    </div>
  );
};

export default MyOrders;
