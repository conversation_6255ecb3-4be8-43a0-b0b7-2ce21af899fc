/* @import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap"); */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");

html,
body,
#root {
  height: 100%;
  width: 100%;
  font-family: "Inter", sans-serif;
}

.app {
  min-height: 100vh;
}

.content {
  width: 100%;
  font-family: "Inter", sans-serif;
}

.app {
  display: flex;
  position: relative;
}

::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #e0e0e0;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

a {
  text-decoration: none;
  color: inherit;
}

.cursor-pointer {
  cursor: pointer;
}

.pro-sidebar {
  position: absolute;
}

.content {
  margin-left: 270px;
  transition: margin, left, right, 0.3s;
}

.sidemenu-collapsed .content {
  margin-left: 80px;
}

#root .sidemenu-collapsed .pro-sidebar .pro-sidebar-inner .pro-sidebar-layout .scrollable-menu {
  height: calc(100vh - 100px);
}

th {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.6);
}


.dashboard-chips {
  margin-bottom: 24px;

}

.dashboard-chips .chips {
  background: #D9D2FD;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.dashboard-chips .chips:after {
  background: #AE9FFA;
  width: 40px;
  height: 2px;
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  content: "";
}

.dashboard-chips .chips .round{
  width:100px;
  height:100px;
  position: absolute;
  top:-30px;
  right:-30px;
  border-radius: 50%;
  background: #C8BEFC;
  


}
.dashboard-chips .chips .round:after{
  content:"";
  position: absolute;
  width:80px;
  height:80px;
  top:10px;
  right:10px;
  border-radius: 50%;
  background: #AE9FFA; 
}

.dashboard-chips .chips svg{color: #9480FA;}
.dashboard-chips .chips h5{color: #9480FA;}

.dashboard-chips .chips.second{background: #FFF7ED;}
.dashboard-chips .chips.second:after{ background: #FED7AA;}
.dashboard-chips .chips.second .round{ background: #FFEDD5;}
.dashboard-chips .chips.second .round:after{ background: #FED7AA;}
.dashboard-chips .chips.second svg{color: #FA8D40; }
.dashboard-chips .chips.second h5{color: #FA8D40;}



.dashboard-chips .chips.third{background: #F0FDF4;}
.dashboard-chips .chips.third:after{ background: #BBF7D0;}
.dashboard-chips .chips.third .round{ background: #DCFCE7;}
.dashboard-chips .chips.third .round:after{ background: #BBF7D0;}
.dashboard-chips .chips.third svg{color: #4FD27F;}
.dashboard-chips .chips.third h5{color: #4FD27F;}

.dashboard-chips .chips.fourth{background: #ECFEFF;}
.dashboard-chips .chips.fourth:after{ background: #A5F3FC;}
.dashboard-chips .chips.fourth .round{ background: #CFFAFE;}
.dashboard-chips .chips.fourth .round:after{ background: #A5F3FC;}
.dashboard-chips .chips.fourth svg{color: #48CBE0;}
.dashboard-chips .chips.fourth h5{color: #48CBE0;}
/* .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: #846cf9 !important;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #846cf9 !important;
} */
