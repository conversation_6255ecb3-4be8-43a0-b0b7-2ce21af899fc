const express = require('express');
const router = express.Router();
const FaqController = require('./FaqController');
const validations = require('./FaqValidations');
const { validate } = require('../../util/validations');
const { verifyToken, verifyTokenUserOrAdmin, verifyTokenAdmin } = require('../../util/auth');

// Public routes
router.get('/', FaqController.list);
router.get('/categories', FaqController.getCategories);
router.get('/:id', FaqController.detail);

// Admin-only routes
router.post('/', verifyTokenAdmin, validate(validations.addFaq), FaqController.add);
router.put('/:id', verifyTokenAdmin, validate(validations.updateFaq), FaqController.update);
router.delete('/:id', verifyTokenAdmin, FaqController.delete);
router.put('/:id/toggle-status', verifyTokenAdmin, FaqController.toggleStatus);

module.exports = router;
