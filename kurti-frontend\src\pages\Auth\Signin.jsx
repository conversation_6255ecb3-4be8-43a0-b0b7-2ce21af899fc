import { Grid, Icon<PERSON>utton, Typography } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { useEmailPasswordSignIn } from "api/auth.api";
import GoogleAuth from "components/atoms/GoogleAuth";
import Forgot<PERSON><PERSON><PERSON><PERSON>tom from "components/atoms/ForgotPassword.atom";
import { ErrorMessage, Field, Form, Formik } from "formik";
import { signinInitialValues, signinValidationSchema } from "globals";
import { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import { setUserInfo } from "stores/user";
import { useCheckUserDisabledMutation } from "../../api/auth.api";



const Signin = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const redirectUrl = searchParams.get("redirect") || "/";
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const { mutateAsync: emailSignIn, isLoading: emailSignInLoading } = useEmailPasswordSignIn();
  const { mutateAsync: checkUserDisabled } = useCheckUserDisabledMutation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleEmailPasswordSignIn = async (values, { setSubmitting }) => {
    try {
      const checkUserDisabledResult = await checkUserDisabled({
        email: values.email
      });

      if (checkUserDisabledResult?.success && checkUserDisabledResult?.data?.isSuspended) {
        toast.error("Account is disabled. Please contact the administrator.");
        return;
      }

      const result = await emailSignIn({
        email: values.email,
        password: values.password
      });

      if (result) {
        setUserInfo(result);
        toast.success("Successfully signed in!");
        navigate(redirectUrl);
      }
    } catch (error) {
      toast.error("Invalid email or password");
    } finally {
      setSubmitting(false);
    }
  };



  return (
    <div className="auth-pages">
      <Grid container>
        <Grid item lg={12} md={12} sm={12} xs>
          <div className="box">
            <h3>Sign In</h3>

            <div className="mt-4">
              <Formik
                initialValues={signinInitialValues}
                validationSchema={signinValidationSchema}
                enableReinitialize
                onSubmit={handleEmailPasswordSignIn}
              >
                {({ isSubmitting }) => (
                  <Form>
                    <div className="form-group">
                      <label htmlFor="email">Email:</label>
                      <Field
                        type="email"
                        id="email"
                        name="email"
                        className="form-control"
                        placeholder="Enter your email"
                      />
                      <ErrorMessage
                        name="email"
                        component="div"
                        className="error"
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="password">Password:</label>
                      <div style={{ position: "relative" }}>
                        <Field
                          type={showPassword ? "text" : "password"}
                          id="password"
                          name="password"
                          className="form-control"
                          placeholder="Enter your password"
                        />
                        <IconButton
                          style={{
                            position: "absolute",
                            right: "10px",
                            top: "50%",
                            transform: "translateY(-50%)",
                          }}
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </div>
                      <ErrorMessage
                        name="password"
                        component="div"
                        className="error"
                      />
                    </div>

                    <div className="text-end mb-3">
                      <button
                        type="button"
                        className="btn btn-link p-0"
                        style={{ color: "#6c5ebc", textDecoration: "none" }}
                        onClick={() => setShowForgotPassword(true)}
                      >
                        Forgot Password?
                      </button>
                    </div>

                    <div className="form-group">
                      <button
                        type="submit"
                        disabled={isSubmitting || emailSignInLoading}
                        className="btn btn-black"
                        style={{
                          backgroundColor: "#6c5ebc",
                          color: "white",
                          border: "none",
                          width: "100%"
                        }}
                      >
                        {isSubmitting || emailSignInLoading ? "Signing In..." : "Sign In"}
                      </button>
                    </div>

                    <div className="form-group text-center">
                      <Typography variant="body2" sx={{ my: 2 }}>
                        OR
                      </Typography>
                      <GoogleAuth />
                    </div>
                  </Form>
                )}
              </Formik>
            </div>

            <div className="text-center mt-3">
              <p>
                Don't have an account?{" "}
                <Link to="/sign-up" className="fw-bold" style={{ color: "#6c5ebc" }}>
                  Sign Up
                </Link>
              </p>
            </div>
          </div>
        </Grid>
      </Grid>

      {/* Forgot Password Modal */}
      <ForgotPasswordAtom
        show={showForgotPassword}
        handleClose={() => setShowForgotPassword(false)}
      />
    </div>
  );
};

export default Signin;
