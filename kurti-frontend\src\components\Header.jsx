import { useCartQuery } from "api/cart.api";
import { useWishlistQuery } from "api/wishlist.api";
import { useEffect, useRef, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { setCartItems } from "stores/cart";
import useUserStore, { resetState } from "stores/user";
import { setWishlistItems } from "stores/wishlist";
import userImg from "../assets/images/14.jpg";
import logo from "../assets/images/logo.png";
import { IconLogin, IconLogout, IconSearchMagnifyGlass } from "../utils/icons";
import MobileHeader from "./MobileHeader";
import CartIconWithCount from "./atoms/CartIconWithCount.atom";
import WishlistIconWithCount from "./atoms/WishlistIconWithCount.atom";
import defaultAvatar from "../assets/images/14.jpg";

const Header = () => {
  const navigate = useNavigate();
  const { token, user } = useUserStore((state) => state.userInfo);
  const { data: cartStoredData = {}, refetch } = useCartQuery(token);
  const { data: wishlistStoredData = {}, refetch: refetchWishlist } =
    useWishlistQuery(token);

  const [isVisible, setIsVisible] = useState(false);
  const searchBoxRef = useRef(null);
  const [searchQry, setSearchQry] = useState();

  const toggleSearchBox = () => {
    setIsVisible(!isVisible);
  };

  const handleClickOutside = (event) => {
    if (searchBoxRef.current && !searchBoxRef.current.contains(event.target)) {
      setIsVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleClick = (path) => {
    navigate(path);
  };

  const handleLogout = () => {
    resetState();
    setCartItems([]);
    setWishlistItems([]);
    navigate("/");
  };

  const handleSearch = () => {
    navigate(`/category?search=${searchQry}`);
    setSearchQry("");
    setIsVisible(false);
  };

  useEffect(() => {
    if (cartStoredData?.products?.length && token) {
      const savedItems = cartStoredData?.products.map((item) => ({
        _id: item.productId?._id,
        name: item.productId?.name,
        mainImage: item.productId?.mainImage,
        price: item.price,
        quantity: item.quantity,
        selectedSize: item.selectedSize,
        selectedColor: item.selectedColor,
      }));
      setCartItems(savedItems);
    }
  }, [cartStoredData, token]);

  useEffect(() => {
    if (wishlistStoredData?.products?.length && token) {
      const savedWishlistItems = wishlistStoredData?.products.map((item) => ({
        _id: item.productId?._id,
        name: item.productId?.name,
        mainImage: item.productId?.mainImage,
        prices: item.prices,
      }));
      setWishlistItems(savedWishlistItems);
    }
  }, [wishlistStoredData, token]);

  return (
    <>
      <header className="gc-header-default header-width-default">
        <div className="container-xl gc-container-xl">
          <div className="gc-header-content">
            <div className="gc-header-top-left header-top-side">
              <div className="gc-header-default-inner">
                <div className="logo logo-type-img">
                  <Link
                    to="/"
                    aria-label="logo image"
                    className="nt-logo header-logo logo-type-img"
                  >
                    <img
                      className="main-logo entered lazyloaded"
                      src={logo}
                      alt="GC"
                    />
                  </Link>
                </div>
              </div>
            </div>
            <div className="gc-header-top-center">
              <div className="gc-header-default-inner">
                <div className="gc-header-top-menu-area menu-item-has-shortcode-parent">
                  <ul className="navigation primary-menu">
                    <li className="menu-item current-menu-item current_page_item menu-item-home current-menu-ancestor current-menu-parent active">
                      <Link to={"/"}>Home</Link>
                    </li>
                    <li className="menu-item  ">
                      <Link to={"/about-us"}>About</Link>
                    </li>
                    <li className="menu-item  ">
                      <Link to={"/products"}>Shop</Link>
                    </li>
                    <li className="menu-item ">
                      <Link to={"/contact"}>Contact Us</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="gc-header-top-right header-top-side">
              <div className="gc-header-default-inner">
                <div
                  className="top-action-btn"
                  data-name="search-popup"
                  onClick={toggleSearchBox}
                  ref={searchBoxRef}
                >
                  <IconSearchMagnifyGlass />
                  {isVisible && (
                    <div
                      className="search-box"
                      onClick={(evt) => evt.stopPropagation()}
                    >
                      <input
                        type="text"
                        placeholder="Search..."
                        onChange={(evt) => setSearchQry(evt.target.value)}
                        value={searchQry}
                        onKeyDown={(evt) => {
                          if (evt.key === "Enter") {
                            handleSearch();
                          }
                        }}
                      />
                      <button
                        id="search-button"
                        className="button min-inherit alt gc-btn-medium gc-btn gc-btn-black"
                        onClick={handleSearch}
                        disabled={!searchQry}
                        style={{
                          backgroundColor: "#6c5ebc",
                          color: "white",
                          border: "none",
                        }}
                      >
                        Search
                      </button>
                    </div>
                  )}
                </div>
                <div className="header-top-buttons">
                  <CartIconWithCount />
                  <WishlistIconWithCount />

                  {token ? (
                    <>
                      {/* <div
                        className="top-action-btn header-top-account"
                        data-account-action="account"
                      >
                        <Link className="account-page-link" to="/my-orders">
                          <IconUserProfile />
                        </Link>
                      </div> */}
                      <div className="top-action-btn header-top-account">
                        <Link className="account-page-link" to="/dashboard">
                          <img
                            src={user?.avatar || defaultAvatar}
                            alt="Profile"
                            className="avatar avatar-sm avatar-rounded"
                          />
                        </Link>
                      </div>

                      <div
                        className="top-action-btn cursor-pointer"
                        data-name="cart"
                        onClick={handleLogout}
                      >
                        <IconLogout className={"gc-svg-icon"} />
                      </div>
                    </>
                  ) : (
                    <Link
                      to="/sign-in"
                      className="top-action-btn"
                      data-name="cart"
                      title="login"
                    >
                      <IconLogin className={"gc-svg-icon"} />
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <MobileHeader handleLogout={handleLogout} />
    </>
  );
};

export default Header;
