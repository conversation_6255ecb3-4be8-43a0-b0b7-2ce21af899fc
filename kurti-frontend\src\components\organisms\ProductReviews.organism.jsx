import React, { useState, useEffect } from "react";
import { useProductReviewsQuery, useAddReviewMutation, useUpdateReviewMutation, useDeleteReviewMutation } from "api/review.api";
import { Form, Button, Card, Row, Col, Modal } from "react-bootstrap";
import { toast } from "react-toastify";
import useUserStore from "stores/user";
import { IconStar, IconStarFilled, IconEdit, IconDelete } from "utils/icons";
import { formatDate } from "utils";

const StarRating = ({ rating, setRating, readOnly = false }) => {
  const [hover, setHover] = useState(0);

  return (
    <div className="star-rating">
      {[...Array(5)].map((_, index) => {
        const ratingValue = index + 1;
        return (
          <span
            key={index}
            className={`star ${readOnly ? "readonly" : ""}`}
            onClick={() => !readOnly && setRating(ratingValue)}
            onMouseEnter={() => !readOnly && setHover(ratingValue)}
            onMouseLeave={() => !readOnly && setHover(0)}
          >
            {ratingValue <= (hover || rating) ? <IconStarFilled /> : <IconStar />}
          </span>
        );
      })}
    </div>
  );
};

const ReviewForm = ({ productId, onReviewAdded, existingReview = null, onCancel = null }) => {
  const [rating, setRating] = useState(existingReview?.rating || 5);
  const [comment, setComment] = useState(existingReview?.comment || "");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { mutateAsync: addReview } = useAddReviewMutation();
  const { mutateAsync: updateReview } = useUpdateReviewMutation();
  const userInfo = useUserStore((state) => state.userInfo);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!userInfo.token) {
      toast.error("Please login to submit a review");
      return;
    }

    if (rating < 1) {
      toast.error("Please select a rating");
      return;
    }

    if (!comment.trim()) {
      toast.error("Please enter a comment");
      return;
    }

    setIsSubmitting(true);

    try {
      // Log authentication state for debugging
      console.log("Review submission auth state:", {
        hasToken: !!userInfo.token,
        tokenPrefix: userInfo.token ? userInfo.token.substring(0, 10) + '...' : 'none',
        userId: userInfo.user?._id,
        fcmId: userInfo.user?._id // Firebase UID is used as fcmId
      });

      if (existingReview) {
        // Update existing review
        await updateReview({
          productId,
          reviewId: existingReview._id,
          rating,
          comment,
          userId: userInfo.user?._id, // Use fcmId instead of userId
          fcmId: userInfo.user?._id // Use fcmId instead of userId
        });
        toast.success("Review updated successfully");
      } else {
        // Add new review
        await addReview({
          productId,
          rating,
          comment,
          userId: userInfo.user?._id,
          fcmId: userInfo.user?._id // Use fcmId instead of userId
        });
        toast.success("Review added successfully");
        setRating(5);
        setComment("");
      }

      if (onReviewAdded) {
        onReviewAdded();
      }
    } catch (error) {
      console.error("Review submission error:", error);
      toast.error(error.message || "Failed to submit review");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form onSubmit={handleSubmit} className="review-form">
      <Form.Group className="mb-3">
        <Form.Label>Your Rating</Form.Label>
        <div>
          <StarRating rating={rating} setRating={setRating} />
        </div>
      </Form.Group>

      <Form.Group className="mb-3">
        <Form.Label>Your Review</Form.Label>
        <Form.Control
          as="textarea"
          rows={4}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Write your review here..."
          required
        />
      </Form.Group>

      <div className="d-flex gap-2">
        <Button
          variant="primary"
          type="submit"
          disabled={isSubmitting}
          className="submit-review-btn"
        >
          {isSubmitting ? "Submitting..." : existingReview ? "Update Review" : "Submit Review"}
        </Button>

        {onCancel && (
          <Button
            variant="outline-secondary"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        )}
      </div>
    </Form>
  );
};

const ProductReviewsOrganism = ({ productId }) => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(5);
  const [editingReview, setEditingReview] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState(null);

  const { data: reviewsData, isLoading, refetch } = useProductReviewsQuery(productId, { page, limit });
  const { mutateAsync: deleteReview } = useDeleteReviewMutation();

  const userInfo = useUserStore((state) => state.userInfo);

  const reviews = reviewsData?.items || [];
  const totalReviews = reviewsData?.count || 0;

  const averageRating = reviews.length > 0
    ? (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1)
    : 0;

  const userReview = userInfo.token
    ? reviews.find(review => review.fcmId === userInfo.user?._id)
    : null;

  const handleReviewAdded = () => {
    refetch();
    setEditingReview(null);
  };

  const handleEditReview = (review) => {
    setEditingReview(review);
  };

  const handleDeleteClick = (review) => {
    setReviewToDelete(review);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!reviewToDelete) return;

    try {
      // Log authentication state for debugging
      console.log("Review deletion auth state:", {
        hasToken: !!userInfo.token,
        tokenPrefix: userInfo.token ? userInfo.token.substring(0, 10) + '...' : 'none',
        userId: userInfo.user?._id,
        fcmId: userInfo.user?._id // Firebase UID is used as fcmId
      });

      await deleteReview({
        productId,
        reviewId: reviewToDelete._id,
        fcmId: userInfo.user?._id // Use fcmId instead of userId
      });
      toast.success("Review deleted successfully");
      refetch();
    } catch (error) {
      console.error("Review deletion error:", error);
      toast.error(error.message || "Failed to delete review");
    } finally {
      setShowDeleteModal(false);
      setReviewToDelete(null);
    }
  };

  const loadMoreReviews = () => {
    setPage(page + 1);
  };

  return (
    <div className="product-reviews-section mt-5">
      <h3 className="section-title mb-4">Ratings & Reviews</h3>

      {reviews.length > 0 && (
        <div className="reviews-summary mb-4">
          <Row>
            <Col md={4} className="text-center">
              <div className="average-rating">
                <h2>{averageRating}</h2>
                <StarRating rating={parseFloat(averageRating)} readOnly={true} />
                <p className="text-muted">{totalReviews} {totalReviews === 1 ? 'Review' : 'Reviews'}</p>
              </div>
            </Col>
            <Col md={8}>
              {!userReview && userInfo.token && (
                <div className="write-review-container">
                  <h5>Write a Review</h5>
                  <ReviewForm productId={productId} onReviewAdded={handleReviewAdded} />
                </div>
              )}
            </Col>
          </Row>
        </div>
      )}

      {reviews.length === 0 && !isLoading && (
        <div className="no-reviews mb-4">
          <p>There are no reviews yet. Be the first to review this product!</p>
          {userInfo.token ? (
            <ReviewForm productId={productId} onReviewAdded={handleReviewAdded} />
          ) : (
            <p>Please <a href="/sign-in">login</a> to write a review.</p>
          )}
        </div>
      )}

      {isLoading ? (
        <div className="loading-skeleton">
          <div className="skeleton-line"></div>
          <div className="skeleton-line"></div>
          <div className="skeleton-line"></div>
        </div>
      ) : (
        <>
          <div className="reviews-list">
            {reviews.map((review) => (
              <Card key={review._id} className="review-card mb-3">
                <Card.Body>
                  <div className="d-flex justify-content-between align-items-start">
                    <div>
                      <h5 className="reviewer-name">{review.userName || "Anonymous"}</h5>
                      <div className="review-meta">
                        <StarRating rating={review.rating} readOnly={true} />
                        <span className="review-date">{formatDate(review.createdAt)}</span>
                      </div>
                    </div>

                    {userInfo.user?._id === review.fcmId && (
                      <div className="review-actions">
                        <Button
                          variant="link"
                          className="p-0 me-2"
                          onClick={() => handleEditReview(review)}
                        >
                          <IconEdit />
                        </Button>
                        <Button
                          variant="link"
                          className="p-0 text-danger"
                          onClick={() => handleDeleteClick(review)}
                        >
                          <IconDelete />
                        </Button>
                      </div>
                    )}
                  </div>

                  {editingReview && editingReview._id === review._id ? (
                    <div className="mt-3">
                      <ReviewForm
                        productId={productId}
                        existingReview={review}
                        onReviewAdded={handleReviewAdded}
                        onCancel={() => setEditingReview(null)}
                      />
                    </div>
                  ) : (
                    <p className="review-comment mt-3">{review.comment}</p>
                  )}
                </Card.Body>
              </Card>
            ))}
          </div>

          {reviews.length < totalReviews && (
            <div className="text-center mt-4">
              <Button
                variant="outline-primary"
                onClick={loadMoreReviews}
                className="load-more-btn"
              >
                Load More Reviews
              </Button>
            </div>
          )}
        </>
      )}

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete this review? This action cannot be undone.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleConfirmDelete}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default ProductReviewsOrganism;
