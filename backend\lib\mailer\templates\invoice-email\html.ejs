<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 10px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .order-details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .order-details p {
            margin: 5px 0;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <% if (email_logo) { %>
                <img src="<%= email_logo %>" alt="<%= site_title %>" class="logo">
            <% } %>
            <h1>Your Invoice</h1>
        </div>
        
        <p>Dear <%= custom.order.shippingAddress.fullName %>,</p>
        
        <p>Thank you for your order. Please find your invoice attached to this email.</p>
        
        <div class="order-details">
            <p><strong>Order ID:</strong> <%= custom.order.orderId %></p>
            <p><strong>Order Date:</strong> <%= new Date(custom.order.createdAt).toLocaleDateString() %></p>
            <p><strong>Total Amount:</strong> ₹<%= custom.order.totalPrice.toFixed(2) %></p>
        </div>
        
        <p>If you have any questions about your order, please contact our customer support team.</p>
        
        <p>Thank you for shopping with us!</p>
        
        <div class="footer">
            <p>&copy; <%= new Date().getFullYear() %> <%= site_title %>. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
