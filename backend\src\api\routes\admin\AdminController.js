const {
    models: {
        Admin,
        AdminSettings,
        User,
        Product,
        SizeType,
        CaptionTag,
        ClothLength,
        Color,
        Discount,
        Fabric,
        Sections,
        Occasion,
        Pattern,
        PaymentMode,
        Brand,
        Category,
    },
} = require('../../../../lib/models');
const { utcDateTime, generateResetToken, logError } = require('../../../../lib/util');
const { signToken } = require('../../util/auth');
const { sendMail } = require('../../../../lib/mailer');
const { getPlatform } = require('../../util/common');

class AdminController {
    async logIn(req, res) {
        const { email, password } = req.body;

        try {
            const admin = await Admin.findOne({ email, isDeleted: false });

            if (!admin) {
                return res.warn('error', req.__('INVALID_CREDENTIALS'));
            }
            if (admin.isSuspended) {
                return res.warn('error', req.__('YOUR_ACCOUNT_SUSPENDED'));
            }

            if (admin.failedLoginAttempts >= parseInt(process.env.allowedFailedLoginAttempts || 3)) {
                let difference = admin.preventLoginTill - utcDateTime().valueOf();
                if (difference > 0) {
                    let differenceInSec = Math.abs(difference) / 1000;
                    differenceInSec -= Math.floor(differenceInSec / 86400) * 86400;
                    differenceInSec -= (Math.floor(differenceInSec / 3600) % 24) * 3600;
                    const minutes = Math.floor(differenceInSec / 60) % 60;
                    differenceInSec -= minutes * 60;
                    const seconds = differenceInSec % 60;
                    return res.warn(
                        'error',
                        req.__(
                            'LOGIN_DISABLED',
                            `${minutes ? `${minutes} ${req.__('KEY_MINUTES')} ` : ''}${seconds} ${req.__(
                                'KEY_SECONDS'
                            )}`
                        )
                    );
                }
            }
            const passwordMatched = await admin.comparePassword(password);
            if (!passwordMatched) {
                admin.failedLoginAttempts = admin.failedLoginAttempts + 1;
                admin.preventLoginTill = utcDateTime(
                    utcDateTime().valueOf() + parseInt(process.env.preventLoginOnFailedAttemptsTill || 5) * 60000
                ).valueOf();
                await admin.save();
                const chanceLeft = parseInt(process.env.allowedFailedLoginAttempts || 3) - admin.failedLoginAttempts;
                return res.warn(
                    'error',
                    req.__(
                        'INVALID_CREDENTIALS_LIMIT',
                        `${chanceLeft <= 0
                            ? `${req.__('KEY_LOGIN_DISABLED')}`
                            : `${req.__('KEY_YOU_HAVE_ONLY')} ${chanceLeft} ${req.__('KEY_CHANCE_LEFT')}`
                        }`
                    )
                );
            }

            admin.authTokenIssuedAt = utcDateTime().valueOf();
            admin.failedLoginAttempts = 0;
            admin.preventLoginTill = 0;
            await admin.save();

            const platform = getPlatform(req);
            const token = signToken(admin, platform);
            const userJson = admin.toJSON();
            ['password', 'authTokenIssuedAt', 'failedLoginAttempts', 'preventLoginTill', 'social', '__v'].forEach(
                key => delete userJson[key]
            );

            return res.success(
                {
                    token,
                    user: userJson,
                },
                req.__('LOGIN_SUCCESS')
            );
        } catch (error) {
            res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async logout(req, res) {
        req.session.user = null;
        req.session.token = null;
        req.flash('success', req.__('LOGOUT_SUCCESS'));
        return res.redirect('/auth/log-in');
    }

    async dashboard(req, res) {
        return res.render('index');
    }

    async profilePage(req, res) {
        const { user } = req;
        return res.success('profile', {
            user,
        });
    }

    async profile(req, res) {
        const { user } = req;
        const { firstName, lastName, email, countryCode, contactNumber } = req.body;
        user.firstName = firstName;
        user.lastName = lastName;
        user.email = email;
        user.countryCode = countryCode;
        user.contactNumber = contactNumber;
        await user.save();
        res.warn('success', req.__('PROFILE_UPDATED'));
        return res.redirect('/profile');
    }

    async activeInactive(req, res) {
        const { modelName, status, modelId } = req.body;

        try {
            const models = {
                User,
                Product,
                SizeType,
                CaptionTag,
                ClothLength,
                Color,
                Discount,
                Fabric,
                Sections,
                Occasion,
                Pattern,
                PaymentMode,
                Brand,
                Category,
            };

            if (!models[modelName]) {
                return res.warn('error', req.__('INVALID_MODEL'));
            }

            const updateStatus = await models[modelName].updateOne({ _id: modelId }, { isSuspended: status });

            if (updateStatus.nModified === 0) {
                return res.warn('error', req.__('RECORD_NOT_FOUND'));
            }

            return res.success({}, req.__('DATA_UPDATE_SUCCESS'));
        } catch (error) {
            console.error('Error updating model:', error);
            return res.error(error, 'An error occurred while updating the model');
        }
    }

    async deleteToggle(req, res) {
        const { modelName, status, modelId } = req.body;

        try {
            const models = {
                User,
                Product,
                SizeType,
                CaptionTag,
                ClothLength,
                Color,
                Discount,
                Fabric,
                Sections,
                Occasion,
                Pattern,
                PaymentMode,
                Brand,
                Category,
            };

            if (!models[modelName]) {
                return res.warn('error', req.__('INVALID_MODEL'));
            }

            const updateStatus = await models[modelName].updateOne({ _id: modelId }, { isDeleted: status });

            if (updateStatus.nModified === 0) {
                return res.warn('error', req.__('RECORD_NOT_FOUND'));
            }

            return res.success({}, req.__('DELETE_SUCCESS'));
        } catch (error) {
            console.error('Error updating model:', error);
            return res.error(error, 'An error occurred while updating the model');
        }
    }

    async changePassword(req, res) {
        const { user } = req;
        const { currentPassword, newPassword, confirmPassword } = req.body;
        const passwordMatched = await user.comparePassword(currentPassword);
        if (!passwordMatched) {
            return res.warn('error', req.__('PASSWORD_MATCH_FAILURE'));
        }

        user.password = newPassword;
        await user.save();

        return res.success('success', req.__('PASSWORD_CHANGED'));
    }

    async forgotPasswordPage(req, res) {
        if (req.session.user) {
            return res.redirect('/');
        }
        return res.render('forgot-password');
    }

    async forgotPassword(req, res) {
        if (req.session.user) {
            return res.redirect('/');
        }
        const { email } = req.body;

        const admin = await Admin.findOne({
            email,
            isDeleted: false,
        });

        if (!admin) {
            req.flash('error', req.__('USER_NOT_FOUND'));
            return res.redirect('/auth/forgot-password');
        }

        if (admin.isSuspended) {
            req.flash('error', req.__('YOUR_ACCOUNT_SUSPENDED'));
            return res.redirect('/auth/forgot-password');
        }

        admin.resetToken = generateResetToken();
        await admin.save();

        req.flash('success', req.__('FORGOT_PASSWORD_MAIL_SUCCESS'));
        res.redirect('/auth/log-in');

        sendMail('admin-forgot-password', req.__('EMAIL_RESET_PASSWORD'), email, {
            verification_code: admin.resetToken,
            resetLink: `${process.env.SITE_URL}/auth/reset-password?email=${email}`,
        }).catch(error => {
            logError(`Failed to send password reset link to ${email}`);
            logError(error);
        });
    }

    async isEmailExists(req, res) {
        const { email, id } = req.body;
        const matchCond = {
            isDeleted: false,
            email,
        };
        id &&
            (matchCond._id = {
                $ne: id,
            });
        const count = await Admin.countDocuments(matchCond);

        return res.send(count === 0);
    }

    async settingsPage(req, res) {
        let settings = await AdminSettings.findOne({}).lean();

        return res.render('setting', {
            settings,
        });
    }

    // async updateSettings(req, res) {
    //     const { androidAppVersion, androidForceUpdate, iosAppVersion, iosForceUpdate, maintenance } = req.body;

    //     await AdminSettings.updateMany(
    //         {},
    //         {
    //             $set: {
    //                 androidAppVersion,
    //                 androidForceUpdate,
    //                 iosAppVersion,
    //                 iosForceUpdate,
    //                 maintenance,
    //             },
    //         },
    //         {
    //             upsert: true,
    //         }
    //     );

    //     req.flash('success', req.__('SETTINGS_UPDATE_SUCCESS'));
    //     return res.redirect('/');
    // }
    async updateSettings(req, res) {
        try {
            const settingsData = req.body;

            // Find and update settings
            const updatedSettings = await AdminSettings.findOneAndUpdate({}, settingsData, {
                new: true, // Return the updated document
                upsert: true, // Create the document if it does not exist
            });

            if (!updatedSettings) {
                return res.warn({}, req.__('SETTINGS_NOT_FOUND'));
            }

            return res.success(updatedSettings, req.__('SETTINGS_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating settings:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getSettings(req, res) {
        try {
            // Retrieve settings
            const settings = await AdminSettings.findOne({});

            if (!settings) {
                return res.warn({}, req.__('SETTINGS_NOT_FOUND'));
            }

            return res.success(settings, req.__('SETTINGS_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while retrieving settings:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async sslcheck(req, res) {
        const axios = require('axios');
        try {
            const { hostname } = req.query;
            const response = await axios.get(`https://api.ssllabs.com/api/v3/analyze?host=${hostname}&all=on`);
            res.json(response.data);
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    }

    async test(req, res) {
        const axios = require('axios');
        const { hostname } = req.query;
        if (!hostname) {
            return res.status(400).json({ error: 'Domain parameter is required' });
        }

        try {
            const response = await fetch(`https://api.whois/v1/whois?domain=${hostname}`);

            if (!response.ok) {
                throw new Error('Failed to fetch domain information');
            }
            const domainInfo = await response.json();
            const creationDate = new Date(domainInfo.creationDate);
            const currentDate = new Date();
            const ageInMilliseconds = currentDate - creationDate;
            const ageInYears = ageInMilliseconds / (1000 * 60 * 60 * 24 * 365);
            res.json({ domainAge: ageInYears.toFixed(2) });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    }

    async updateAdminProfile(req, res) {
        const adminId = req.user._id;
        const { firstName, lastName, email, countryCode, contactNumber } = req.body;

        try {
            // Find the admin by ID
            const admin = await Admin.findById(adminId);
            if (!admin) {
                return res.notFound({}, req.__('ADMIN_NOT_FOUND'));
            }

            // Update fields
            if (firstName) admin.firstName = firstName;
            if (lastName) admin.lastName = lastName;
            if (email) admin.email = email;
            if (countryCode) admin.countryCode = countryCode;
            if (contactNumber) admin.contactNumber = contactNumber;

            // Save the updated admin
            const updatedAdmin = await admin.save();

            return res.success(updatedAdmin, req.__('ADMIN_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating admin profile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getAdminProfile(req, res) {
        const adminId = req.user._id;

        try {
            // Find the admin by ID
            const admin = await Admin.findById(adminId);
            if (!admin) {
                return res.notFound({}, req.__('ADMIN_NOT_FOUND'));
            }

            return res.success(admin, req.__('ADMIN_PROFILE_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while retrieving admin profile:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async createSubAdmin(req, res) {
        const { firstName, lastName, email, password, countryCode, contactNumber } = req.body;

        try {
            // Check if an admin with the same email already exists
            const existingAdmin = await Admin.findOne({ email });
            if (existingAdmin) {
                return res.warn({}, req.__('EMAIL_ALREADY_EXISTS'));
            }

            // Create the new sub-admin
            const newSubAdmin = new Admin({
                firstName,
                lastName,
                email,
                password,
                countryCode,
                contactNumber,
                role: 'SUB_ADMIN',
            });

            // Save the new sub-admin
            await newSubAdmin.save();

            return res.success(newSubAdmin, req.__('SUB_ADMIN_CREATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while creating sub-admin:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getSubAdminList(req, res) {
        const adminId = req.user._id;
        try {
            const subAdmins = await Admin.find({ role: 'SUB_ADMIN' });
            if (subAdmins.length <= 0) {
                return res.warn({}, req.__('NO_SUB_ADMIN_FOUND'));
            }
            return res.success(subAdmins, req.__('SUB_ADMIN_LIST_RETRIEVED_SUCCESSFULL'));
        } catch (error) {
            console.error('Error while retrieving sub-admin list:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }
    async updateSubAdminPassword(req, res) {
        const { user } = req.user;
        try {
            const { subadminId, newPassword, confirmPassword } = req.body;

            if (newPassword !== confirmPassword) {
                return res.badRequest({}, req.__('PASSWORD_MISMATCH'));
            }

            const subAdmin = await Admin.findById(subadminId);

            if (!subAdmin) {
                return res.notFound({}, req.__('SUB_ADMIN_NOT_FOUND'));
            }

            subAdmin.password = newPassword;
            await subAdmin.save();
            return res.success('success', req.__('PASSWORD_CHANGED'));
        } catch (error) {
            console.error('Error while retrieving sub-admin list:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async updateSubAdmin(req, res) {
        const { id } = req.params; // Sub-admin ID to be updated
        const { firstName, lastName, email, countryCode, contactNumber } = req.body;

        try {
            // Check if the sub-admin exists
            const existingSubAdmin = await Admin.findById(id);
            if (!existingSubAdmin) {
                return res.warn({}, req.__('SUB_ADMIN_NOT_FOUND'));
            }

            // If email is being updated, check for conflicts
            if (email && email !== existingSubAdmin.email) {
                const emailExists = await Admin.findOne({ email });
                if (emailExists) {
                    return res.warn({}, req.__('EMAIL_ALREADY_EXISTS'));
                }
                existingSubAdmin.email = email;
            }


            if (firstName) existingSubAdmin.firstName = firstName;
            if (lastName) existingSubAdmin.lastName = lastName;
            if (countryCode) existingSubAdmin.countryCode = countryCode;
            if (contactNumber) existingSubAdmin.contactNumber = contactNumber;


            await existingSubAdmin.save();

            return res.success(existingSubAdmin, req.__('SUB_ADMIN_UPDATED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while updating sub-admin:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

    async getSubAdminById(req, res) {
        const { id } = req.params;

        try {
            const subAdmin = await Admin.findById(id);
            if (!subAdmin) {
                return res.notFound({}, req.__('SUB_ADMIN_NOT_FOUND'));
            }
            return res.success(subAdmin, req.__('SUB_ADMIN_RETRIEVED_SUCCESSFULLY'));
        } catch (error) {
            console.error('Error while retrieving sub-admin:', error);
            return res.serverError({}, req.__('INTERNAL_SERVER_ERROR'));
        }
    }

}

module.exports = new AdminController();
