import React, { useEffect } from 'react';

const loadScript = (url) => {
  return new Promise((resolve, reject) => {
    const existingScript = document.querySelector(`script[src="${url}"]`);
    if (existingScript) {
      resolve();
    } else {
      const script = document.createElement('script');
      script.src = url;
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject();
      document.head.appendChild(script);
    }
  });
};

const GoogleMap = ({ settingsData }) => {
  const { googleApiKey, googleLat = 0, googleLong = 0 } = settingsData ?? {};

  useEffect(() => {
    const loadMap = async () => {
      if (!googleLat || !googleLong) return;

      try {
        await loadScript(`https://maps.googleapis.com/maps/api/js?key=${googleApiKey}`);
        initMap();
      } catch (error) {
        console.error('Failed to load Google Maps script:', error);
      }
    };

    loadMap();
  }, [googleApiKey, googleLat, googleLong]);

  const initMap = () => {
    if (!googleLat || !googleLong) return;

    const coordinates = { lat: googleLat, lng: googleLong };

    const map = new window.google.maps.Map(document.getElementById('map'), {
      center: coordinates,
      zoom: 8
    });

    new window.google.maps.Marker({
      position: coordinates,
      map: map,
      title: 'Location'
    });
  };

  return <div id="map" style={{ width: '100%', height: '100%' }}></div>;
};

export default GoogleMap;
