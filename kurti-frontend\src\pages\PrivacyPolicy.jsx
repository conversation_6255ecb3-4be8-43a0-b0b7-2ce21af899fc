import { useStaticPageQuery } from "api/util.api";
import { useEffect } from "react";
import { decodeHtml } from "utils";
import parse from "html-react-parser";

const PrivacyPolicy = () => {
  const { data, isSuccess } = useStaticPageQuery({ slug: window.location.pathname });
  const decodedHtml = decodeHtml(data?.description);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [window.location.pathname, isSuccess]);

  return (
    <div className="policy-container">
      {data?.title || data?.description ? (
        <div className="content-wrapper">
          <h1 className="policy-title">{data?.title}</h1>
          <div className="policy-content">
            {parse(decodedHtml)}
          </div>
        </div>
      ) : (
        <div className="no-data">
          <h1>No Data Found</h1>
        </div>
      )}
    </div>
  );
};

export default PrivacyPolicy;
