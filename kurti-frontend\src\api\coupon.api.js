import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./api-client";

export const useValidateCouponMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      const response = await apiClient.post(
        API_ENDPOINTS.VALIDATE_COUPON,
        payload
      );
      return response;
    },
  });

export const useCouponsQuery = ({params}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_COUPONS, {params});
      if (response?.success) {
        return response.data;
      }
    },
    queryKey: ["coupons"],
  });
