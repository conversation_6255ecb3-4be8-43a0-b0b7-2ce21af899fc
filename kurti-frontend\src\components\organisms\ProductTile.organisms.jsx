import React from "react";
import { Link } from "react-router-dom";
import ProductTileTitleAtom from "../atoms/ProductTileTitle.atom.";
import PriceWithStrikeAtom from "../atoms/PriceWithStrike.atom";
import ProductTileBtnsMolecule from "../molecules/ProductTileBtns.molecule";

const ProductTileOrganisms = ({
  pId = "",
  mainImage = "",
  secondImage = "",
  plink = "",
  pname = "",
  price = 0,
  sellingPrice = 0,
  tag = "",
  brand = "",
  colors = "",
  size = "",
  productInfo = {},
}) => {
  return (
    <div className="gc-loop-product">
      <div className="gc-product">
        <div className="gc-thumb-wrapper">
          <Link
            to={`/product-details/${pId}`}
            className="product-link has-images"
          >
            <img
              width="450"
              height="600"
              src={mainImage}
              className="attachment-woocommerce_thumbnail size-woocommerce_thumbnail"
              alt=""
              decoding="async"
            />
            <img
              width="450"
              height="600"
              src={secondImage}
              className="overlay-thumb  is-shop"
              alt=""
              decoding="async"
            />
          </Link>
          <div className="gc-product-labels">
            <span className="gc-label gc-badge badge-def">{tag}</span>
            <span className="gc-label gc-discount">{brand}</span>
          </div>

          <div className="gc-loop-product-buttons-hover">
            <ProductTileBtnsMolecule
              pId={pId}
              plink={`/product-details/${pId}`}
              productInfo={productInfo}
            />
          </div>
        </div>

        <div className="gc-loop-product-buttons-mobile gc-mini-icon">
          <ProductTileBtnsMolecule pId={pId} plink={`/product-details/${pId}`} productInfo={productInfo} />
        </div>

        <ProductTileTitleAtom plink={`/product-details/${pId}`} pname={pname} />

        <PriceWithStrikeAtom price={price} sellingprice={sellingPrice} />
      </div>
    </div>
  );
};

export default ProductTileOrganisms;
