import { Visibility, VisibilityOff } from "@mui/icons-material";
import { <PERSON>rid, IconButton, Typography } from "@mui/material";
import { useEmailPasswordSignUp } from "api/auth.api";
import GoogleAuth from "components/atoms/GoogleAuth";
import { ErrorMessage, Field, Form, Formik } from "formik";
import { signupInitialValues, signupValidationSchema } from "globals/schema-validations";
import { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import { setUserInfo } from "stores/user";

const Signup = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const redirectUrl = searchParams.get("redirect") || "/";
  const [showPassword, setShowPassword] = useState({
    password: false,
    confirmPassword: false,
  });

  const { mutateAsync: emailSignUp, isLoading: emailSignUpLoading } = useEmailPasswordSignUp();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleEmailPasswordSignUp = async (values, { setSubmitting, resetForm }) => {
    try {
      const result = await emailSignUp({
        email: values.email,
        password: values.password
      });

      if (result) {
        setUserInfo(result);
        toast.success("Account created successfully!");

        if (!result.user.emailVerified) {
          toast.info("A verification email has been sent to your email address. Please verify your email.");
        }

        navigate(redirectUrl);
      }
    } catch (error) {
      toast.error(error.message || "Failed to create account. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="auth-pages">
      <Grid container>
        <Grid item lg={12} md={12} sm={12} xs>
          <div className="box">
            <h3>Create Account</h3>

            <Formik
              initialValues={signupInitialValues}
              validationSchema={signupValidationSchema}
              onSubmit={handleEmailPasswordSignUp}
            >
              {({ isSubmitting }) => (
                <Form>
                  <div className="form-group">
                    <label htmlFor="email">Email</label>
                    <Field
                      className="form-control"
                      type="email"
                      id="email"
                      name="email"
                      placeholder="Enter your email"
                    />
                    <ErrorMessage
                      name="email"
                      component="div"
                      className="error"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="password">Password</label>
                    <div style={{ position: "relative" }}>
                      <Field
                        className="form-control"
                        type={showPassword?.password ? "text" : "password"}
                        id="password"
                        name="password"
                        placeholder="Create a password"
                      />
                      <IconButton
                        style={{
                          position: "absolute",
                          right: "10px",
                          top: "50%",
                          transform: "translateY(-50%)",
                        }}
                        onClick={() =>
                          setShowPassword({
                            ...showPassword,
                            password: !showPassword?.password,
                          })
                        }
                      >
                        {showPassword?.password ? (
                          <VisibilityOff />
                        ) : (
                          <Visibility />
                        )}
                      </IconButton>
                    </div>
                    <ErrorMessage
                      name="password"
                      component="div"
                      className="error"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="confirmPassword">Confirm Password</label>
                    <div style={{ position: "relative" }}>
                      <Field
                        type={
                          showPassword?.confirmPassword ? "text" : "password"
                        }
                        id="confirmPassword"
                        name="confirmPassword"
                        className="form-control"
                        placeholder="Confirm your password"
                      />
                      <IconButton
                        style={{
                          position: "absolute",
                          right: "10px",
                          top: "50%",
                          transform: "translateY(-50%)",
                        }}
                        onClick={() =>
                          setShowPassword({
                            ...showPassword,
                            confirmPassword: !showPassword?.confirmPassword,
                          })
                        }
                      >
                        {showPassword?.confirmPassword ? (
                          <VisibilityOff />
                        ) : (
                          <Visibility />
                        )}
                      </IconButton>
                    </div>
                    <ErrorMessage
                      name="confirmPassword"
                      component="div"
                      className="error"
                    />
                  </div>

                  <div className="form-group">
                    <button
                      type="submit"
                      disabled={isSubmitting || emailSignUpLoading}
                      className="btn btn-black"
                      style={{
                        backgroundColor: "#6c5ebc",
                        color: "white",
                        border: "none",
                        width: "100%"
                      }}
                    >
                      {isSubmitting || emailSignUpLoading ? "Creating Account..." : "Create Account"}
                    </button>
                  </div>

                  <div className="form-group text-center">
                    <Typography variant="body2" sx={{ my: 2 }}>
                      OR
                    </Typography>
                    <GoogleAuth type="signup" />
                  </div>
                </Form>
              )}
            </Formik>

            <div className="text-center mt-3">
              <p>
                Already have an account?{" "}
                <Link to="/sign-in" className="fw-bold" style={{ color: "#6c5ebc" }}>
                  Sign In
                </Link>
              </p>
            </div>
          </div>
        </Grid>
      </Grid>
    </div>
  );
};

export default Signup;
