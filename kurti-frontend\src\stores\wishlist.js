import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

const initialState = {
  wishlistItems: [],
};

const wishlistStore = (set) => ({
  ...initialState,
  setWishlistItems: (data) => set((state) => ({ ...state, wishlistItems: data })),
});

const useWishlistStore = create(
  devtools(
    persist(wishlistStore, {
      name: "wishlist",
    })
  )
);

export const { setWishlistItems } = useWishlistStore.getState();

export default useWishlistStore;
