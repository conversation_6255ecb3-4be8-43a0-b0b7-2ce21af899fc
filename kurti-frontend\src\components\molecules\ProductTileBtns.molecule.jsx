import { useUpdateCartMutation } from "api/cart.api";
import {
  useRemoveWishlistMutation,
  useUpdateWishlistMutation,
} from "api/wishlist.api";
import useCustomDeviceDetect from "../../hooks/useCustomDeviceDetect";
import { useMemo, useState } from "react";
import { toast } from "react-toastify";
import useCartStore, { setCartItems } from "stores/cart";
import useUserStore from "stores/user";
import useWishlistStore, { setWishlistItems } from "stores/wishlist";
import {
  IconCart,
  IconWishList,
  IconWishListEmpty,
  IconWishListFilled,
  IconQuickView,
} from "utils/icons";
import QuickLookModal from "components/modals/QuickLookModal";
import { isMobileOnly } from "react-device-detect";

const ProductTileBtnsMolecule = ({
  pId = 0,
  pLink = "",
  isFavourite = false,
  productInfo = {},
}) => {
  const [showQuickLook, setShowQuickLook] = useState(false);
  const { mutateAsync: updateCart } = useUpdateCartMutation();

  const cartItems = useCartStore((state) => state.cartItems);
  const wishlistItems = useWishlistStore((state) => state.wishlistItems);
  const token = useUserStore((state) => state.userInfo.token);
  const { mutateAsync: removeWishlist } = useRemoveWishlistMutation();
  const { mutateAsync: updateWishlist } = useUpdateWishlistMutation();

  const handleQuickLook = (e) => {
    e.stopPropagation();
    setShowQuickLook(true);
  };

  const handleCloseQuickLook = () => {
    setShowQuickLook(false);
  };

  const isProductInCart = useMemo(
    () => cartItems.some((item) => item._id === productInfo._id),
    [cartItems, productInfo]
  );

  const isProductInWishlist = useMemo(
    () => wishlistItems.some((item) => item._id === productInfo._id),
    [wishlistItems, productInfo]
  );

  const handleAddToCart = () => {
    if (isProductInCart) {
      const updatedItems = cartItems.map((item) => {
        if (item?._id === productInfo?._id) {
          return {
            ...item,
            quantity: item?.quantity + 1,
          };
        }
        return item;
      });
      updateCart({
        data: {
          productId: productInfo._id,
          quantity: 1,
          selectedSize: productInfo?.stock?.[0]?.size,
          selectedColor: productInfo?.attributes?.color?.[0],
        },
        token,
      });
      setCartItems(updatedItems);
      toast.success("Item added to cart");
      return;
    }
    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productInfo?.attributes?.color?.[0]?.images?.mainImage,
      price: productInfo.prices.b2cPrice.sellingPrice,
      originalPrice: productInfo.prices.b2cPrice.originalPrice,
      discount: productInfo.prices.b2cPrice.discount,
      quantity: 1,
      selectedSize: productInfo?.stock?.[0]?.size,
      selectedColor: productInfo?.attributes?.color?.[0],
    };
    if (token) {
      updateCart({
        data: {
          productId: productInfo._id,
          quantity: 1,
          selectedSize: productInfo?.stock?.[0]?.size,
          selectedColor: productInfo?.attributes?.color?.[0],
        },
        token,
      });
    }
    setCartItems([...cartItems, itemInfo]);
    toast.success("Item added to cart");
  };

  const handleAddToWishlist = () => {
    if (isProductInWishlist) {
      setWishlistItems(
        wishlistItems.filter((item) => item._id !== productInfo._id)
      );
      removeWishlist(productInfo?._id);
      return;
    }
    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productInfo?.attributes?.color?.[0]?.images?.mainImage,
      prices: productInfo?.prices,
    };
    if (token) {
      updateWishlist({
        data: {
          productId: productInfo._id,
        },
        token,
      });
    }
    setWishlistItems([...wishlistItems, itemInfo]);
  };

  const { isMobile, isTablet } = useCustomDeviceDetect();
  return (
    <>
      <div
        className="gc-quickview-btn gc-product-button"
        data-id={pId}
        data-label="Quick View"
        onClick={handleQuickLook}
      >
        <IconQuickView />
      </div>

      {!isFavourite && (
        <div
          className={`gc-wishlist-btn gc-product-button ${
            isMobile || isTablet ? "" : "p-2"
          } cursor-pointer`}
          data-id={pId}
          data-label="Add to Wishlist"
          onClick={handleAddToWishlist}
        >
          {isProductInWishlist ? <IconWishListFilled /> : <IconWishListEmpty />}
        </div>
      )}

      <div
        className="gc-compare-btn gc-product-button"
        data-id={pId}
        data-label="Add to cart"
        onClick={handleAddToCart}
      >
        <IconCart />
      </div>

      {/* Quick Look Modal */}
      <QuickLookModal
        show={showQuickLook}
        handleClose={handleCloseQuickLook}
        productId={productInfo._id}
      />
    </>
  );
};

export default ProductTileBtnsMolecule;
