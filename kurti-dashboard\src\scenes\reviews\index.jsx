import { Box, Icon<PERSON><PERSON>on, <PERSON>lt<PERSON>, useTheme, TextField, Rating, Dialog, DialogTitle, DialogContent, DialogActions, Button } from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import Header from "components/Header";
import StyledDataGrid from "components/datagrids/StyledDataGrid";
import { useState } from "react";
import { useGetAllReviewsQuery, useUpdateReviewMutation, useDeleteReviewMutation } from "services/review.service";
import { tokens } from "theme";
import dataGridStyles from "../../styles/dataGridStyles";
import { useDispatch } from "react-redux";
import { setConfirmModalConfig } from "store/slices/utilSlice";
import { toast } from "react-toastify";

const Reviews = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const styles = dataGridStyles(theme.palette.mode);
  const dispatch = useDispatch();

  // State
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [currentReview, setCurrentReview] = useState(null);
  const [editRating, setEditRating] = useState(0);
  const [editComment, setEditComment] = useState("");

  // Queries
  const { data = {}, isLoading, refetch } = useGetAllReviewsQuery({
    params: {
      page: page + 1,
      limit: pageSize,
      search: searchTerm || undefined,
    }
  });

  // Mutations
  const [updateReview, { isLoading: isUpdating }] = useUpdateReviewMutation();
  const [deleteReview, { isLoading: isDeleting }] = useDeleteReviewMutation();

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setPage(0); // Reset to first page when searching
  };

  const handleEdit = (review) => {
    setCurrentReview(review);
    setEditRating(review.rating);
    setEditComment(review.comment);
    setEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
    setCurrentReview(null);
  };

  const handleSaveEdit = async () => {
    if (!currentReview) return;

    try {
      await updateReview({
        productId: currentReview.productId,
        reviewId: currentReview._id,
        rating: editRating,
        comment: editComment
      }).unwrap();

      toast.success("Review updated successfully");
      handleCloseEditDialog();
      refetch();
    } catch (error) {
      toast.error("Failed to update review");
    }
  };

  const handleDelete = (review) => {
    dispatch(
      setConfirmModalConfig({
        visible: true,
        data: {
          onSubmit: async () => {
            try {
              await deleteReview({
                productId: review.productId,
                reviewId: review._id
              }).unwrap();
              toast.success("Review deleted successfully");
              refetch();
            } catch (error) {
              toast.error("Failed to delete review");
            }
          },
          content: {
            heading: "Delete Review?",
            description: "Are you sure you want to delete this review? This action cannot be undone.",
          },
        },
      })
    );
  };

  // Column definitions
  const columns = [
    {
      field: "productName",
      headerName: "Product",
      flex: 1.5,
      renderCell: ({ row }) => {
        return (
          <Tooltip title={row.productName} arrow>
            <span>{row.productName}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "user",
      headerName: "User",
      flex: 1,
      renderCell: ({ row }) => {
        return (
          <Tooltip title={`${row.user?.fullName} (${row.user?.email})`} arrow>
            <span>{row.user?.fullName || "Anonymous"}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "rating",
      headerName: "Rating",
      flex: 0.8,
      renderCell: ({ row }) => {
        return (
          <Rating value={row.rating} readOnly precision={0.5} size="small" />
        );
      },
    },
    {
      field: "comment",
      headerName: "Comment",
      flex: 2,
      renderCell: ({ row }) => {
        return (
          <Tooltip title={row.comment} arrow>
            <span>{row.comment.length > 50 ? `${row.comment.substring(0, 50)}...` : row.comment}</span>
          </Tooltip>
        );
      },
    },
    {
      field: "createdAt",
      headerName: "Date",
      flex: 1,
      renderCell: ({ row }) => {
        // Format date manually without date-fns
        const date = new Date(row.createdAt);
        const options = { year: 'numeric', month: 'short', day: 'numeric' };
        const formattedDate = date.toLocaleDateString('en-US', options);
        return (
          <span>{formattedDate}</span>
        );
      },
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.8,
      renderCell: ({ row }) => {
        return (
          <Box>
            <Tooltip title="Edit">
              <IconButton onClick={() => handleEdit(row)}>
                <EditIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton onClick={() => handleDelete(row)}>
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  return (
    <Box m="20px">
      <Box sx={styles.mainHeadings}>
        <Header title="REVIEWS" subtitle="Managing Product Reviews and Ratings" />
      </Box>

      <Box
        sx={{
          ...styles.formContainer,
          mb: 2,
        }}
      >
        <TextField
          fullWidth
          variant="outlined"
          label="Search Reviews"
          placeholder="Search by product name, user name, or comment"
          value={searchTerm}
          onChange={handleSearchChange}
          sx={{ mb: 2 }}
        />

        <StyledDataGrid
          rows={data.reviews || []}
          columns={columns}
          getRowId={(row) => row._id}
          loading={isLoading}
          pagination
          page={page}
          pageSize={pageSize}
          rowCount={data.totalReviews || 0}
          paginationMode="server"
          onPageChange={(newPage) => setPage(newPage)}
          onPageSizeChange={(newPageSize) => setPageSize(newPageSize)}
          className="no-shaddow"
          noResultText="No reviews found."
        />
      </Box>

      {/* Edit Review Dialog */}
      <Dialog open={editDialogOpen} onClose={handleCloseEditDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Review</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            {currentReview && (
              <>
                <Box sx={{ mb: 2 }}>
                  <strong>Product:</strong> {currentReview.productName}
                </Box>
                <Box sx={{ mb: 2 }}>
                  <strong>User:</strong> {currentReview.user?.fullName || "Anonymous"}
                </Box>
                <Box sx={{ mb: 2 }}>
                  <strong>Rating:</strong>
                  <Rating
                    value={editRating}
                    onChange={(event, newValue) => {
                      setEditRating(newValue);
                    }}
                    precision={1}
                  />
                </Box>
                <TextField
                  label="Comment"
                  multiline
                  rows={4}
                  value={editComment}
                  onChange={(e) => setEditComment(e.target.value)}
                  fullWidth
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseEditDialog} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleSaveEdit}
            color="secondary"
            variant="contained"
            disabled={isUpdating}
            sx={styles.buttonxs}
          >
            {isUpdating ? "Saving..." : "Save Changes"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Reviews;
