const ShipRocketStatusCodes = Object.freeze({
    PENDING: 1, //manually inserted
    CANCELED_BEFORE_SHIP: 2, //manually inserted
    SHIPPED: 6,
    DELIVERED: 7,
    CANCELED: 8,
    RTO_INITIATED: 9,
    RTO_DELIVERED: 10,
    LOST: 12,
    P<PERSON><PERSON><PERSON>_ERROR: 13,
    R<PERSON>_ACKNOWLEDGED: 14,
    <PERSON><PERSON><PERSON><PERSON>_RESCHEDULED: 15,
    CANCELLATION_REQUESTED: 16,
    OUT_FOR_DELIVERY: 17,
    IN_TRANSIT: 18,
    OUT_FOR_PICKUP: 19,
    PICKUP_EXCEPTION: 20,
    UNDELIVERED: 21,
    DELAYED: 22,
    PARTIAL_DELIVERED: 23,
    DESTROYED: 24,
    DAMAGED: 25,
    FULFILLED: 26,
    PICKUP_BOOKED: 27,
    REACHED_AT_DESTINATION_HUB: 38,
    MISROUTED: 39,
    RTO_NDR: 40,
    RTO_OFD: 41,
    PICKED_UP: 42,
    SE<PERSON>_FULFILLED: 43,
    DISPOSED_OFF: 44,
    CA<PERSON><PERSON>LED_BEFORE_DISPATCHED: 45,
    RTO_IN_INTRANSIT: 46,
    QC_FAILED: 47,
    REACHED_WAREHOUSE: 48,
    CUSTOM_CLEARED: 49,
    IN_FLIGHT: 50,
    HANDOVER_TO_COURIER: 51,
    SHIPMENT_BOOKED: 52,
    IN_TRANSIT_OVERSEAS: 54,
    CONNECTION_ALIGNED: 55,
    REACHED_OVERSEAS_WAREHOUSE: 56,
    CUSTOM_CLEARED_OVERSEAS: 57,
    BOX_PACKING: 59,
    PROCESSED_AT_WAREHOUSE: 68,
    FC_ALLOCATED: 60,
    PICKLIST_GENERATED: 61,
    READY_TO_PACK: 62,
    PACKED: 63,
    FC_MANIFEST_GENERATED: 67,
    HANDOVER_EXCEPTION: 71,
    PACKED_EXCEPTION: 72,
    RTO_LOCK: 75,
    UNTRACEABLE: 76,
    ISSUE_RELATED_TO_THE_RECIPIENT: 77,
    REACHED_BACK_AT_SELLER_CITY: 78,
});

module.exports = ShipRocketStatusCodes;
