import { Box, Button, Grid } from "@mui/material";
import {
  useRequestOTPMutation,
  useSignupMutation
} from "api";
import { useUpdateCartMutation } from "api/cart.api";
import { useUpdateWishlistMutation } from "api/wishlist.api";
import { useState } from "react";
import OTPInput from "react-otp-input";
import { useNavigate, useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import useCartStore from "stores/cart";
import useUserStore, { setUserInfo } from "stores/user";
import useWishlistStore from "stores/wishlist";

const VerifyOTP = () => {
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const [searchParams] = useSearchParams();
  const phone = searchParams.get("phone");
  const navigate = useNavigate();

  const userInfo = useUserStore((state) => state.userInfo);
  const redirectUrl = searchParams.get("redirect") || "/";
  const cartItems = useCartStore((state) => state.cartItems);
  const wishlistItems = useWishlistStore((state) => state.wishlistItems);
  const { mutateAsync: updateCart } = useUpdateCartMutation();
  const { mutateAsync: updateWishlist } = useUpdateWishlistMutation();

  const { mutateAsync: requestOtp } = useRequestOTPMutation();
  const { mutateAsync: signup } = useSignupMutation();

  const handleVerify = async () => {
    if (otp.length < 4) {
      setError("Please enter a valid OTP");
      return;
    }
    setLoading(true);
    try {
      const result = await userInfo?.confirm(otp);
      if (result?.user?.accessToken) {
        setUserInfo({
          token: result?.user?.accessToken,
          user: result?.user,
        });
        const payload = {
          phone: result?.user?.phoneNumber,
          fcmId: result?.user?.uid,
          isSocial: true,
        };
        const signupResult = await signup(payload);
        const updatePromises = cartItems.map((item) =>
          updateCart({
            data: {
              productId: item?._id,
              quantity: item?.quantity,
              selectedSize: item?.selectedSize,
              selectedColor: item?.selectedColor,
            },
            token: result?.accessToken,
          })
        );

        const wishlistPromises = wishlistItems.map((item) =>
          updateWishlist({
            data: {
              productId: item?._id,
            },
            token: result?.accessToken,
          })
        );
        const allPromises = updatePromises.concat(wishlistPromises);
        Promise.all(allPromises)
          .then(() => {
            navigate(redirectUrl);
            resolve();
          })
          .catch((error) => {
            console.error(error);
          });
        navigate("/");
      }
    } catch (error) {
      console.error("Error while verifying OTP", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  const handleResend = () => {
    try {
      const payload = {
        type: "SIGN_UP",
        countryCode: "+91",
        phone,
      };
      requestOtp(payload);
    } catch (error) {
      console.error("Error while requesting OTP", error);
    }
  };

  return (
    <div className="auth-pages">
      <Grid container>
        <Grid item lg={12} md={12} sm={12} >
          <Box className="box" sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
            <Box>
              <OTPInput
                value={otp}
                onChange={(otp) => {
                  setOtp(otp);
                  setError(null);
                }}
                numInputs={6}
                renderSeparator={<span>-</span>}
                renderInput={(props) => <input {...props} />}
                containerStyle={{ justifyContent: "center" }}
                inputStyle={{
                  width: "40px",
                  height: "40px",
                  fontSize: "20px",
                  margin: "4px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                }}
              />
              {error && <p style={{ color: "red" }}>{error}</p>}
              <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", marginTop: "20px" }}>
                <Button
                  // variant="contained"
                  // color="primary"
                  onClick={handleVerify}
                  disabled={loading}
                  style={{
                    backgroundColor: "#6c5ebc",
                    color: "white",
                    border: "none"
                  }}
                >
                  {loading ? "Verifying..." : "Verify OTP"}
                </Button>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", marginTop: "20px" }}>
                <Box sx={{ textAlign: "center" }}>
                  <p>Having trouble?</p>
                  <Button style={{
                    backgroundColor: "#6c5ebc",
                    color: "white",
                    border: "none"
                  }}
                    onClick={() => navigate("/sign-in")}>
                    Back to Signin
                  </Button>
                </Box>
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </div>
  );
};

export default VerifyOTP;
