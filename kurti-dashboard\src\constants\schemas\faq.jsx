import * as Yup from "yup";

// INITIAL VALUES
export const faqInitialValues = {
  question: "",
  answer: "",
  category: "General",
  sortOrder: 0,
};

// VALIDATION SCHEMA
export const faqValidationSchema = Yup.object().shape({
  question: Yup.string().required("Question is required"),
  answer: Yup.string().required("Answer is required"),
  category: Yup.string().required("Category is required"),
  sortOrder: Yup.number().required("Sort Order is required"),
});
