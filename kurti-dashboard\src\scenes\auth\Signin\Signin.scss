@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap');

// Extracted colors from the logo
$primary-color: #f8f8f8; // Gradient start (left color)
$secondary-color: #0a0a0a; // Gradient end (right color)
$text-color: #ffffff;
$input-border-color: #cccccc;
$error-color: #ff8080;
$primary-btn-color:#0a0a0a;

#login-page {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(180deg, #6c5ce7, #a86ee6); // Gradient background with purple color theme

  .signin-container {
    background-color: rgba(0, 0, 0, 0.5); // Transparent black background for contrast
    padding: 2.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
    text-align: center;
    max-width: 400px;
    width: 100%;
  }

  .logo-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;

    .signin-logo {
      width: 100px; // Adjust the size to fit nicely
      height: auto;
    }
  }

  .signin-title {
    font-weight: 600;
    font-size: 1.8rem;
    color: $text-color;
    margin-bottom: 2rem;
  }

  .signin-input {
    width: 100%;
    margin-bottom: 1.5rem;

    .MuiInputBase-root {
      color: $text-color;
    }

    .MuiInputLabel-root {
      color: $input-border-color;
    }

    .MuiOutlinedInput-root {
      & fieldset {
        border-color: $input-border-color;
      }
      &:hover fieldset {
        border-color: $text-color;
      }
      &.Mui-focused fieldset {
        border-color: $text-color;
      }
    }

    .MuiFormHelperText-root {
      color: $error-color;
    }
  }

  .signin-button {
    background-color: $primary-btn-color;
    color: $text-color;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    width: 100%;
    padding: 0.75rem;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: darken($primary-color, 10%);
      color: #0a0a0a;
    }
  }
}
