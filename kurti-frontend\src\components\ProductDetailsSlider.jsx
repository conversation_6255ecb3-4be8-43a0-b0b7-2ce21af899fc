import React, { useRef, useEffect, useState } from "react";
import Swiper from "swiper";
import { Pagination, Navigation, Thumbs } from "swiper/modules";
import { Gallery, Item } from "react-photoswipe-gallery";
import "../styles/photoswipe.css";
import { Placeholder } from "react-bootstrap";
import CustomLoadingOverlay from "./molecules/CustomLoadingOverlay";
import VideoPlayer from "./organisms/VideoPlayer";
import InnerImageZoom from "react-inner-image-zoom";

Swiper.use([Navigation, Pagination, Thumbs]);

const ProductDetailsSlider = ({ images }) => {
  const mainSwiperRef = useRef(null);
  const thumbsSwiperRef = useRef(null);
  const videoRefs = useRef({});

  const [newImages, setNewImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    setLoading(true);
    const loadMedia = (media) => {
      return new Promise((resolve) => {
        if (media?.mediaType === 'image') {
          const img = new Image();
          img.src = media.url;
          img.onload = () =>
            resolve({ ...media, width: img.width, height: img.height });
        } else {
          // For videos, we don't need to preload
          resolve(media);
        }
      });
    };

    const loadMediaItems = async () => {
      const loadedMedia = await Promise.all(
        images.map((media) => loadMedia(media))
      );
      setNewImages(loadedMedia);
      setLoading(false);
    };

    loadMediaItems();
  }, [images]);

  useEffect(() => {
    if (thumbsSwiperRef.current) {
      const thumbsSwiper = new Swiper(thumbsSwiperRef.current, {
        spaceBetween: 10,
        slidesPerView: 8,
        watchSlidesVisibility: true,
        watchSlidesProgress: true,
      });

      const mainSwiper = new Swiper(mainSwiperRef.current, {
        spaceBetween: 10,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        pagination: false,
        thumbs: {
          swiper: thumbsSwiper,
        },
        on: {
          slideChange: () => {
            setActiveIndex(mainSwiper.activeIndex);
          },
        },
      });
    }
  }, [newImages]);

  const handleSlideChange = (index) => {
    // Pause all videos
    Object.values(videoRefs.current).forEach(videoRef => {
      if (videoRef && !videoRef.paused) {
        videoRef.pause();
      }
    });

    // Play the video if it's a video slide
    const currentMedia = newImages[index];
    if (currentMedia?.mediaType === 'video') {
      const videoRef = videoRefs.current[index];
      if (videoRef) {
        videoRef.play();
      }
    }
  };

  useEffect(() => {
    handleSlideChange(activeIndex);
  }, [activeIndex]);

  return (
    <div className="position-relative">
      <CustomLoadingOverlay active={loading} />
      <div className="swiper-container main-slider" ref={mainSwiperRef}>
        <div className="swiper-wrapper">
          {newImages.map((media, index) => (
            <>
              {media?.url && (
                <div className="swiper-slide" style={{height:"auto"}} key={index}>
                  {media?.mediaType === 'image' ? (
                    // <img src={media?.url} alt={`Slide ${index + 1}`} />
                    <InnerImageZoom src={media?.url} zoomSrc={media?.url} />
                  ) : (
                    <video
                      ref={el => videoRefs.current[index] = el}
                      src={media?.url}
                      controls
                      style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                    />
                  )}
                </div>
              )}
            </>
          ))}
        </div>
        <div className="swiper-pagination"></div>
        <div className="swiper-button-next"></div>
        <div className="swiper-button-prev"></div>

        {/* <Gallery>
          {newImages.map((media, index) => {
            return (
              <Item
                original={media?.url}
                thumbnail={media?.url}
                width={media?.width}
                height={media?.height}
                key={index}
              >
                {({ ref, open }) => (
                  <div
                    className=""
                    ref={ref}
                    onClick={() => open()}
                  />
                )}
              </Item>
            );
          })}
        </Gallery> */}
      </div>
      <div className="swiper-container swiper-thumb" ref={thumbsSwiperRef}>
        <div className="swiper-wrapper">
          {newImages.map((media, index) => (
            <>
              {media?.url && (
                <div className="swiper-slide" key={index}>
                  <div className="media-container" style={{ aspectRatio: '1 / 1', overflow: 'hidden', position: 'relative' }}>
                    {media?.mediaType === 'image' ? (
                      <img
                        src={media?.url}
                        alt={`Thumb ${index + 1}`}
                        style={{ width: '100%', height: '100%', objectFit: 'contain' }}
                      />
                    ) : (
                      <>
                        <video
                          src={media?.url}
                          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                        />
                        <div
                          style={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            width: '30%',
                            height: '30%',
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            borderRadius: '50%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                        >
                          <svg
                            viewBox="0 0 24 24"
                            style={{ width: '50%', height: '50%', fill: 'white' }}
                          >
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              )}
            </>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsSlider;
