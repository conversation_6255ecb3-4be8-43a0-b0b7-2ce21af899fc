import { useNavigate } from "react-router-dom";
import useWishlistStore from "stores/wishlist";
import { IconWishList } from "utils/icons";

const WishlistIconWithCount = ({onClick}) => {
  const wishlistItems = useWishlistStore((state) => state.wishlistItems);
  const wishlistCount = wishlistItems.length ?? 0;

  const navigate = useNavigate();

  const handleClick = (path) => {
    navigate(path);
    onClick();
  };
  return (
    <div
      className="top-action-btn"
      data-name="wishlist"
      onClick={() => handleClick("/wishlist")}
    >
      <span className="gc-wishlist-count gc-wc-count">{wishlistCount}</span>
      <IconWishList className="svgLove gc-svg-icon" />
    </div>
  );
};

export default WishlistIconWithCount;
