import { useQuery } from "@tanstack/react-query";
import { apiClient } from "api/api-client";
import { API_ENDPOINTS } from "globals/endpoints";

export const useGetAdminSettingsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_ADMIN_SETTINGS);
      if (response?.success) {
        return response.data;
      }
      return {};
    },
    queryKey: ["admin-settings"],
    staleTime: Infinity, // Cache the result indefinitely until manually invalidated
    cacheTime: 1000 * 60 * 60, // Cache for 1 hour
    retry: 1, // Only retry once if the request fails
  });
