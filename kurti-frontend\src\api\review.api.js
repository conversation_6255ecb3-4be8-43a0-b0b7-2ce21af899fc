import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import axios from "axios";
import useUserStore from "stores/user";
import { apiClient } from "./api-client";

// Create a custom client that doesn't use the auth token
// but includes the fcmId in the headers for review operations
const createReviewClient = () => {
  const userInfo = useUserStore.getState().userInfo;
  const fcmId = userInfo?.user?._id;

  return axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    headers: {
      "X-ESHOP-Platform": "ios",
      "X-ESHOP-Version": "1.0.0",
      "Accept-Language": "en",
      "Content-Type": "application/json",
      "X-FCMID": fcmId || "" // Include fcmId in a custom header
    }
  });
};

export const useProductReviewsQuery = (productId, params = {}) =>
  useQuery({
    queryFn: async () => {
      const customClient = createReviewClient();

      const response = await customClient.get(
        `${API_ENDPOINTS.GET_PRODUCT_REVIEWS}/${productId}`,
        { params }
      );

      console.log("Get product reviews response:", response.data);
      if (response.data?.success) {
        return response.data.data;
      }
      return { items: [], count: 0 };
    },
    queryKey: ["product-reviews", productId, params],
    enabled: !!productId,
  });

export const useAddReviewMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      console.log("Adding review with payload:", payload);

      // const customClient = createReviewClient();
      // const customClient = createReviewClient();

      // Make the request without the auth header
      const response = await apiClient.post(
        API_ENDPOINTS.ADD_REVIEW,
        payload
      );

      console.log("Add review response:", response.data);
      return response.data;
    },
  });

export const useUpdateReviewMutation = () =>
  useMutation({
    mutationFn: async (payload) => {
      console.log("Updating review with payload:", payload);

      const customClient = createReviewClient();

      // Make the request without the auth header
      const response = await apiClient.put(
        API_ENDPOINTS.UPDATE_REVIEW,
        payload
      );

      console.log("Update review response:", response.data);
      return response.data;
    },
  });

export const useDeleteReviewMutation = () =>
  useMutation({
    mutationFn: async ({ productId, reviewId, fcmId }) => {
      console.log("Deleting review:", { productId, reviewId, fcmId });

      const customClient = createReviewClient();

      // Include fcmId as a query parameter
      const response = await apiClient.delete(
        `${API_ENDPOINTS.DELETE_REVIEW}/${productId}/${reviewId}?fcmId=${fcmId}`
      );

      console.log("Delete review response:", response.data);
      return response.data;
    },
  });
