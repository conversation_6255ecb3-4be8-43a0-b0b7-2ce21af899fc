@import url("https://fonts.googleapis.com/css2?family=Jost:ital,wght@0,100..900;1,100..900&display=swap");
@import "@fortawesome/fontawesome-free/css/all.css";
@import "./product-reviews.scss";

:root {
  --gc-font: "Jost", Sans-serif;
  --gc-font-heading: "Jost", sans-serif;
  --gc-base: #bb9b1f;
  --gc-purple: #9462dd;
  --gc-pink: var(--gc-purple);
  --gc-pink50: rgba(148, 98, 221, 0.5);
  --gc-purple-dark: #4a30a0;
  --gc-purple-soft: #c2a3f1;
  --gc-purple-softer: #eae0fc;
  --gc-purple-text: #4a30a0;
  --gc-purple-bg: #c2a3f1;
  --gc-primary: #c2a3f1;
  --gc-purple: #ed4b4b;
  --gc-purple-dark: #b20808;
  --gc-purple-soft: #ffb1b1;
  --gc-purple-softer: #fff5f5;
  --gc-purple-text: #b20808;
  --gc-purple-bg: #ffd9d9;

  --gc-text: #4e4e4e;
  --gc-text-soft: #949494;
  --gc-dark-zero: #000000;
  --gc-dark: #1b1b1b;
  --gc-dark-soft: #343131;
  --gc-black: #1b1b1b;
  --gc-black-soft: #808080;
  --gc-black-border: #363636;
  --gc-bg-soft: #f9f9f9;
  --gc-light: #ffffff;
  --gc-light-soft: #bdbdbd;
  --e-global-color-text: #7a7a7a;
  --gc-gray: #e1e2e3;
  --gc-gray-dark: #595959;
  --gc-gray-soft: #f7f7f8;
  --gc-gray-softer: #f7f7f8;
  --gc-border: #eaebed;
  --gc-dark-border: #060606;
  --gc-trans-border: #f7f7f833;
  --gc-success: #398f14;
  --gc-success-bg: #d4ffe7;
  --gc-success-border: #b5fdb0;
  --gc-warning: #c28e00;
  --gc-warning-bg: #fffcf2;
  --gc-warning-border: #faeecf;
  --gc-info: #1e73be;
  --gc-info-bg: #daedfd;
  --gc-info-border: #b0daff;

  --gc-purple-border: #ffbfc1;
  --gc-green: #019267;
  --gc-green-soft: #0ecea6;
  --gc-green-bg: #9be8d8;
  --gc-purple: #6c5ebc;
  --gc-purple-soft: #bdb3ff;
  --gc-purple-bg: #edebfb;
  --gc-yellow: #ffdc00;
  --gc-yellow-soft: #ffe331;
  --gc-yellow-bg: #fffabc;
  --gc-brown: #6c3428;
  --gc-cream: #f4ead5;
  --gc-blue-dark: #011962;
  --gc-blue: #072ac8;
  --gc-blue-soft: #1e96fc;
  --gc-blue-bg: #cee8ff;
  --gc-border-radius: 5px;
}

html {
  margin-right: 0 !important;
}

body {
  font-family: var(--gc-font);
  font-weight: 400;
  font-style: normal;
  font-size: 14.6px;
  color: var(--gc-text);
  line-height: 1.6;
  overflow-x: hidden;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  overflow-y: scroll;
}

a,
.button {
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  color: var(--gc-dark);
  outline: medium none;
}

a {
  background-color: transparent;
  text-decoration: none;
}

a:focus,
.btn:focus,
.button:focus {
  text-decoration: none;
  outline: none;
  box-shadow: none;
}

a:hover {
  color: var(--gc-dark);
  text-decoration: none;
  opacity: 0.8;
}

button:focus,
input:focus,
input:focus,
textarea,
textarea:focus {
  outline: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--gc-dark);
  margin-top: 0;
  font-style: normal;
  font-weight: 500;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: inherit;
}

h1,
h1.elementor-heading-title {
  font-size: 40px;
  line-height: 1.2;
}

h2,
h2.elementor-heading-title {
  font-size: 32px;
  line-height: 1.2;
}

h3,
h3.elementor-heading-title {
  font-size: 25px;
  line-height: 1.3;
}

h4,
h4.elementor-heading-title {
  font-size: 21px;
  line-height: 1.4;
}

h5,
h5.elementor-heading-title {
  font-size: 16px;
  line-height: 1.6;
}

h6,
h6.elementor-heading-title {
  font-size: 14px;
  line-height: 1.6;
}

ul {
  margin: 0;
  padding: 0;
}

p {
  margin-bottom: 15px;
}

p.elementor-heading-title {
  line-height: 1.6;
}

hr {
  border-bottom: 1px solid var(--gc-dark);
  border-top: 0 none;
  margin: 30px 0;
  padding: 0;
}

*::-moz-selection {
  background: var(--gc-gray-dark);
  color: var(--gc-light);
  text-shadow: none;
}

::-moz-selection {
  background: var(--gc-dark);
  color: var(--gc-light);
  text-shadow: none;
}

::selection {
  background: var(--gc-dark);
  color: var(--gc-light);
  text-shadow: none;
}

*::-moz-placeholder {
  color: var(--gc-gray-dark);
  font-size: 14px;
  opacity: 1;
}

*::placeholder {
  color: var(--gc-gray-dark);
  font-size: 14px;
  opacity: 1;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px #fff inset !important;
}

.gc-small-title,
.gc-small-title a {
  color: var(--gc-dark);
}

.gc-hidden {
  display: none;
}

header.gc-header-default {
  position: absolute;
  width: 100%;
  background: var(--gc-light);
  z-index: 100;
  -moz-transition: box-shadow 0.15s linear;
  -o-transition: box-shadow 0.15s linear;
  -webkit-transition: box-shadow 0.15s linear;
  transition: box-shadow 0.18s linear;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  min-height: 80px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  border-bottom: 1px solid;
  border-color: var(--gc-border);
}

.has-sticky-header.scroll-start header.gc-header-default {
  position: fixed;
  top: 0;
  -webkit-box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
  box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
  transition-delay: 0.2s;
}

body {
  &:not(.scroll-start) {
    .header-width-stretch {
      .gc-container {

        &.container,
        &.container-xl {
          max-width: 100%;
        }
      }
    }
  }

  &.admin-bar.has-sticky-header.scroll-start {

    header.gc-header-default,
    .gc-header-mobile-top {
      top: 32px;
    }
  }
}

@media (max-width: 768px) {
  body.admin-bar.has-sticky-header.scroll-start {
    .gc-header-mobile-top {
      top: 46px;
    }
  }
}

@media (max-width: 600px) {
  body.admin-bar.has-sticky-header.scroll-start {
    .gc-header-mobile-top {
      top: 0;
    }
  }
}

.gc-header-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.gc-header-default .mobile-toggle,
.gc-header-default .top-action-btn {
  display: flex;
}

.mobile-toggle {
  align-items: center;
  justify-content: center;
}

.mobile-toggle .menu-title {
  font-size: 18px;
  line-height: 1;
  margin: 0 5px;
  text-transform: uppercase;
  color: var(--gc-dark);
}

.has-default-header-type-dark .mobile-toggle .menu-title {
  color: var(--gc-light);
}

.gc-header-content>div {
  flex: 1;
}

.gc-header-top-center>div {
  justify-content: center;
}

.gc-header-default .header-top-buttons,
.gc-header-default-inner {
  display: flex;
  align-items: center;
}

.gc-header-top-right .gc-header-default-inner {
  justify-content: flex-end;
}

.gc-header-default .top-action-btn {
  margin-left: 15px;
  position: relative;
}

.gc-header-default-inner .header-top-buttons>.top-action-btn:last-child {
  margin-right: 0;
}

.gc-header-default-inner .header-top-buttons>.top-action-btn:first-child {
  margin-left: 0;
}

.gc-header-top-right .gc-header-default-inner>div:not(:first-child) {
  margin-left: 30px;
}

.gc-header-top-left .gc-header-default-inner>div:not(:last-child) {
  margin-right: 20px;
}

.header-basic .gc-header-top-right .gc-header-default-inner>div:not(:first-child) {
  margin-left: 20px;
}

.gc-header-default-inner .top-action-btn .gc-wc-count {
  left: 14px;
  top: -6px;
}

.gc-header-default .gc-header-top-left .top-action-btn {
  margin-left: 0;
}

.gc-header-default .top-action-btn a {
  line-height: 1;
}

.gc-header-top-menu-area ul.navigation,
.gc-header-top-menu-area ul.submenu {
  display: flex;
  flex-direction: row;
  width: 100%;
}

.gc-header-top-menu-area ul.submenu {
  flex-wrap: wrap;
}

.gc-header-top-menu-area.nav-logo-center ul.navigation {
  width: auto;
}

.gc-default-menu-item-empty,
.gc-header-top-menu-area>ul>li.menu-item {
  display: flex;
  position: relative;
  height: 80px;
  align-items: center;
}

.gc-header-top-menu-area a {
  -webkit-transition: none;
  -ms-transition: none;
  -o-transition: none;
  transition: none;
}

.gc-header-top-menu-area>ul>li.menu-item>a {
  font-size: 14px;
  font-weight: 500;
  color: var(--gc-dark);
  text-transform: uppercase;
  padding: 0;
  margin-right: 40px;
  display: block;
  line-height: 1;
  position: relative;
  z-index: 1;
}

.gc-header-top-menu-area>ul>li.menu-item:last-child>a {
  margin-right: 0;
}

.gc-header-top-menu-area ul li .submenu>li.menu-item>a {
  padding: 0;
  line-height: 33px;
  font-weight: 400;
  color: var(--gc-black-soft);
  text-transform: capitalize;
  position: relative;
  display: block;
}

.submenu .menu-label {
  font-size: 9px;
  padding: 3px 5px;
  margin-left: 0;
  position: relative;
  top: -17px;
  right: 0;
  left: auto;
  color: var(--gc-light);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: 0;
  border-color: var(--gc-dark);
  height: 15px;
  line-height: 1;
  display: inline-block;
  align-items: center;
  justify-content: center;
}

.submenu span.menu-label:before {
  content: "";
  position: absolute;
  top: 100%;
  left: 10px;
  border: 4px solid;
  border-right-color: transparent !important;
  border-bottom-color: transparent !important;
  border-right-width: 7px;
  border-left-width: 0;
  border-color: inherit;
}

.current-menu-parent>a,
.current-menu-item>a,
.gc-header-top-menu-area>ul>li.menu-item>a:hover,
.gc-header-top-menu-area ul li .submenu>li.menu-item>a:hover {
  color: var(--gc-dark);
  -webkit-transition: all 0.3s ease-out 0s;
  -moz-transition: all 0.3s ease-out 0s;
  -ms-transition: all 0.3s ease-out 0s;
  -o-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

.gc-header-top-menu-area>ul>li.menu-item.active>a,
.gc-header-top-menu-area ul li .submenu>li.menu-item.active>a {
  color: var(--gc-dark);
}

.gc-header-top-menu-area>ul>li.menu-item.has-dropdown>a,
.gc-header-top-menu-area>ul>li.menu-item-has-shortcode-parent>a {
  padding-right: 15px;
}

.gc-header-top-menu-area li.has-dropdown .dropdown-btn,
.menu-item-has-shortcode-parent .dropdown-btn {
  display: block;
  position: absolute;
  top: 57%;
  transform: translateY(-50%);
  right: 0;
  font-size: 12px;
}

.gc-header-top-menu-area {
  .submenu {
    li {
      &.has-dropdown {
        .dropdown-btn {
          transform: translateY(-50%) rotate(-90deg);
          right: 0;
        }
      }
    }
  }

  &>ul {
    &>li {
      &.has-dropdown {
        &>.submenu {
          .submenu {
            right: auto;
            left: -100%;
            top: -20px;
          }
        }
      }

      &>.submenu {
        .submenu {
          left: 100%;
          top: -15px;
        }
      }

      &.menu-item-mega-parent {
        position: static;

        &>.submenu {
          &>li {
            &.menu-item {
              position: relative;
              display: block;

              &>a {
                text-transform: uppercase;
                position: relative;
                font-weight: 500;
                color: var(--gc-dark);
                margin-bottom: 10px;
                display: block;
                font-size: 14px;

                &>.dropdown-btn {
                  display: none;
                  transition: none;
                }
              }
            }

            &.menu-item-has-children {
              &>.submenu {
                display: block;
                position: static;
                border: 0;
                box-shadow: none;
                min-width: 100%;
                background: none;
                animation: none;
                left: auto;
                top: auto;
                opacity: 1;
                transition-delay: 0s;
              }
            }
          }
        }

        ul {
          &.submenu {
            &.depth_1 {
              padding: 0;
            }
          }
        }

        &.menu-item-has-shortcode-parent {
          &>ul {
            &.submenu {
              &.depth_0 {
                padding: 0;
              }
            }
          }
        }

        &:hover {
          &>.submenu {
            &>li {
              &.menu-item-has-children {
                .submenu {
                  opacity: 1;
                  visibility: visible;
                  transform: none;
                }
              }
            }

            &.depth_0 {
              &>li {
                opacity: 1;
                transform: none;
                padding: 0 20px;

                .submenu {
                  li {
                    padding-left: 0;
                    padding-right: 0;
                  }
                }

                &:nth-child(1) {
                  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.075s,
                    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.15s;
                }

                &:nth-child(2) {
                  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.195s,
                    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.27s;
                }

                &:nth-child(3) {
                  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.255s,
                    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.33s;
                }

                &:nth-child(4) {
                  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.315s,
                    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.39s;
                }

                &:nth-child(5) {
                  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.375s,
                    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.45s;
                }

                &:nth-child(6) {
                  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.435s,
                    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.51s;
                }
              }
            }
          }
        }

        &:not(.menu-item-has-shortcode-parent) {
          &>.submenu {
            &.depth_0 {
              &>li {
                opacity: 0;
                transform: translateY(30px);
              }
            }
          }
        }
      }

      &.menu-item-has-shortcode-parent {
        &>ul {
          &.submenu {
            padding: 0;
          }
        }
      }
    }
  }

  ul {
    li {
      .submenu {
        &>li {
          margin-left: 0;
          text-align: left;
          display: block;
          position: relative;
          padding: 0 25px;
        }
      }
    }

    &.submenu {
      &>li {
        &>a {
          .item-thumb {
            display: block;
            margin-bottom: 10px;
            overflow: hidden;
          }
        }
      }
    }
  }

  li {
    &.menu-item-mega-parent {
      &.menu-item-mega-column-11 {
        &>ul {
          &.submenu {
            &>li {
              width: 8.3333%;
              flex: 0 0 8.3333%;
              width: 9.0909%;
              flex: 0 0 9.0909%;
            }
          }
        }
      }

      &.menu-item-mega-column-10 {
        &>ul {
          &.submenu {
            &>li {
              width: 10%;
              flex: 0 0 10%;
            }
          }
        }
      }

      &.menu-item-mega-column-9 {
        &>ul {
          &.submenu {
            &>li {
              width: 11.1111%;
              flex: 0 0 11.1111%;
            }
          }
        }
      }

      &.menu-item-mega-column-8 {
        &>ul {
          &.submenu {
            &>li {
              width: 12.5%;
              flex: 0 0 12.5%;
            }
          }
        }
      }

      &.menu-item-mega-column-7 {
        &>ul {
          &.submenu {
            &>li {
              width: 14.285%;
              flex: 0 0 14.285%;
            }
          }
        }
      }

      &.menu-item-mega-column-6 {
        &>ul {
          &.submenu {
            &>li {
              width: 16.666667%;
              flex: 0 0 16.666667%;
            }
          }
        }
      }

      &.menu-item-mega-column-5 {
        &>ul {
          &.submenu {
            &>li {
              width: 20%;
              flex: 0 0 20%;
            }
          }
        }
      }

      &.menu-item-mega-column-4 {
        &>ul {
          &.submenu {
            &>li {
              width: 25%;
              flex: 0 0 25%;
            }
          }
        }
      }

      &.menu-item-mega-column-3 {
        &>ul {
          &.submenu {
            &>li {
              width: 33.3333%;
              flex: 0 0 33.3333%;
            }
          }
        }
      }

      &.menu-item-mega-column-2 {
        &>ul {
          &.submenu {
            &>li {
              width: 50%;
              flex: 0 0 50%;
            }
          }
        }
      }

      &.menu-item-mega-column-1 {
        &>ul {
          &.submenu {
            &>li {
              width: 100%;
              flex: 0 0 100%;
            }
          }
        }
      }
    }
  }
}

.gc-header-top-menu-area ul li .submenu,
.gc-header-top-menu-area ul li>.item-shortcode-wrapper {
  min-width: 270px;
  position: absolute;
  padding: 20px 0;
  background-color: var(--gc-light);
  border-radius: 0;
  border: none;
  display: block;
  opacity: 0;
  visibility: hidden;
  //-webkit-box-shadow: 0 15px 30px -4px rgb(0 0 0 / 9%);
  //-moz-box-shadow: 0 15px 30px -4px rgba(0, 0, 0, .09);
  //box-shadow: 0 15px 30px -4px rgb(0 0 0 / 9%);
}

.gc-header-top-menu-area>ul>li>.submenu,
.gc-header-top-menu-area>ul>li>.item-shortcode-wrapper {
  left: 0;
  top: 100%;
}

.gc-header-top-left {
  .gc-header-top-menu-area {
    &>ul {
      &>li {
        &.has-dropdown {
          &>.submenu {
            .submenu {
              right: auto;
              left: 100%;
              top: -20px;
            }
          }
        }
      }
    }
  }
}

.gc-header-top-right {
  .gc-header-top-double-menu {
    ul {
      &>li {
        &>.submenu {
          margin-top: 30px;
        }
      }
    }
  }
}

.gc-header-top-double-menu {
  ul {
    &>li {
      &.menu-item-mega-parent {
        &>.submenu {
          margin-top: 0;
        }
      }
    }
  }
}

.gc-header-top-menu-area ul>li.menu-item:hover>ul.submenu,
.gc-header-top-menu-area ul>li.menu-item.on-hover>ul.submenu,
.gc-header-top-menu-area ul>li.menu-item:hover>.item-shortcode-wrapper {
  opacity: 1;
  visibility: visible;
}

.gc-header-default {
  .gc-header-content {
    &>div {
      &:last-child {
        .navigation {
          &.primary-menu {
            &>li {
              &:last-child {
                .submenu {
                  left: auto;
                }
              }
            }
          }
        }
      }
    }
  }
}

.gc-header-top-menu-area>ul>li.menu-item-mega-parent>ul.submenu.depth_0,
.gc-header-top-menu-area>ul>li.menu-item-mega-parent.menu-item-has-shortcode>.item-shortcode-wrapper {
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 40px 1%;
  border-top: 1px solid var(--gc-border);
  justify-content: space-evenly;
  transition-delay: 0.2s;
  width: 100vw;
  margin-left: -50vw;
  left: 50%;
  right: -15%;
}

.gc-header-top-menu-area>ul>li>.submenu,
.gc-header-top-menu-area>ul>li>.item-shortcode-wrapper,
.gc-header-top-menu-area>ul>li.menu-item-mega-parent>ul.submenu.depth_0,
.gc-header-top-menu-area>ul>li.menu-item-mega-parent.menu-item-has-shortcode>.item-shortcode-wrapper {
  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.075s,
    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.15s;
}

.nav-logo-center .center-logo-wrapper {
  margin: 0 20px;
}

.gc-header-top-mini-menu-area ul.navigation-mini {
  list-style: none;
  display: flex;
  align-items: center;
}

.gc-header-top-mini-menu-area ul.navigation-mini li a {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--gc-gray-dark);
}

.gc-header-top-mini-menu-area ul.navigation-mini li.menu-item-circle-icon a {
  font-size: 25px;
}

.gc-header-default .menu-item-phone-number {
  font-size: 14px;
  font-weight: 400;
  display: flex;
}

.gc-header-default .menu-item-phone-number span.phone-text {
  font-size: 10px;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.gc-header-default .menu-item-phone-number i {
  font-size: 25px;
  margin-right: 10px;
}

.gc-header-top-right ul.navigation-mini li+li {
  margin-left: 15px;
}

.gc-header-top-left ul.navigation-mini li+li {
  margin-right: 15px;
}

.gc-header-top-double-menu {
  display: flex;
  flex-direction: column;
}

.gc-header-top-double-menu:hover .gc-header-top-menu-area ul>li.menu-item:hover>ul.submenu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: transform 0.35s cubic-bezier(0.165, 0.84, 0.44, 1) 0.15s,
    opacity 0.35s cubic-bezier(0.165, 0.84, 0.44, 1) 0.15s;
}

.gc-header-top-left .gc-header-top-double-menu {
  align-items: flex-start;
}

.gc-header-top-right .gc-header-top-double-menu {
  align-items: flex-end;
}

.gc-header-top-center .gc-header-top-double-menu {
  align-items: center;
}

.gc-header-top-right .gc-header-default-inner .gc-header-top-double-menu+.header-top-buttons {
  margin-left: 40px;
}

.gc-header-top-double-menu .gc-header-top-menu-area>ul>li.menu-item {
  height: 40px;
}

.gc-header-top-double-menu .gc-header-top-menu-area>ul>li.menu-item-has-children:hover:after {
  content: "";
  position: absolute;
  height: 40px;
  width: 100%;
  transform: translateY(40px);
}

.gc-header-bottom-bar .gc-before-loop {
  margin-bottom: 0;
}

.gc-header-bottom-bar:not(.gc-elementor-template) {
  border-top: 1px solid var(--gc-border);
  padding: 10px 0;
}

.gc-header-bottom-bar.show-on-scroll:not(.sticky-filter-active) {
  height: 0;
  opacity: 0;
  padding: 0;
  transform: translateY(50px);
  transition: none;
  visibility: hidden;
}

.gc-header-bottom-bar.show-on-scroll.sticky-filter-active {
  height: auto;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.075s,
    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.15s;
}

.gc-header-default .gc-header-bottom-bar .top-action-btn {
  margin-left: 0;
}

.has-default-header-type-dark .gc-header-default,
.has-default-header-type-dark .sidebar-header-bg-type-dark,
.has-default-header-type-dark .mobile-header-bg-type-dark,
.has-default-header-type-dark .autocomplete-suggestions,
.has-default-header-type-dark .gc-side-panel,
.has-default-header-type-dark .gc-header-mobile-top,
.has-default-header-type-dark .gc-header-top-menu-area ul li .submenu,
.has-default-header-type-dark .popup-search-style+.gc-product-categories ul.gc-wc-category-list li>a {
  background: var(--gc-dark);
}

.has-default-header-type-dark .gc-header-mobile-top .gc-svg-icon {
  fill: var(--gc-light);
  color: var(--gc-light);
}

.has-default-header-type-dark .gc-header-top-menu-area>ul>li.menu-item-mega-parent>ul.submenu.depth_0,
.has-default-header-type-dark .gc-header-top-menu-area>ul>li.menu-item-mega-parent.menu-item-has-shortcode>.item-shortcode-wrapper {
  border-top: 1px solid var(--gc-black-border);
}

.has-default-header-type-dark .gc-header-top-menu-area>ul>li.menu-item-mega-parent>ul.submenu.depth_0,
.has-default-header-type-dark .gc-header-top-menu-area>ul>li.menu-item-mega-parent.menu-item-has-shortcode>.item-shortcode-wrapper,
.has-default-header-type-dark .search-area-top,
.has-default-header-type-dark .gc-sidemenu-lang-switcher,
.has-default-header-type-dark .panel-top-title:after,
.has-default-header-type-dark .cart-area .cart-total-price,
.has-default-header-type-dark .gc-header-default input:not([type="checkbox"]):not([type="radio"]),
.has-default-header-type-dark .contact-area.action-content input,
.has-default-header-type-dark .contact-area.action-content select,
.has-default-header-type-dark .contact-area.action-content textarea,
.has-default-header-type-dark .panel-header,
.has-default-header-type-dark .gc-checkout-footer-item.order-total,
.has-default-header-type-dark .gc-order-review,
.has-default-header-type-dark .woocommerce-privacy-policy-text p,
.has-default-header-type-dark .woocommerce-input-wrapper input,
.woocommerce-input-wrapper select,
.has-default-header-type-dark .woocommerce-input-wrapper textarea,
.has-default-header-type-dark .gc-popup-search-panel .no-result+.autocomplete-suggestions .autocomplete-suggestion,
.has-default-header-type-dark .gc-ajax-product-search+.gc-product-categories.category-area {
  border-color: var(--gc-black-border);
}

.has-default-header-type-dark .woocommerce-invalid-required-field .woocommerce-input-wrapper select,
.has-default-header-type-dark .woocommerce-invalid-required-field .woocommerce-input-wrapper input,
.has-default-header-type-dark .woocommerce-invalid-required-field select,
.has-default-header-type-dark .woocommerce-invalid-required-field input {
  border-color: var(--gc-purple);
}

#nt-woo-single .gc-product-showcase.gc-bg-dark #cr_qna.cr-qna-block div.cr-qna-list-block .cr-qna-list-q-cont button.cr-qna-ans-button {
  background: none;
}

.has-default-header-type-dark .search-area-top svg {
  color: var(--gc-black-border);
  fill: var(--gc-black-border);
}

.has-default-header-type-dark .cart-area .quantity-button.plus,
.has-default-header-type-dark .cart-area .quantity-button.minus,
.has-default-header-type-dark .panel-content .quantity input.qty,
.has-default-header-type-dark .action-content .quantity input.qty,
.has-default-header-type-dark .gc-header-mobile-sidebar-bottom {
  color: var(--gc-light);
  background: var(--gc-dark);
}

body:not(.scroll-start) .gc-header-default .gc-header-content {
  min-height: 60px;
}

.has-default-header-type-dark .gc-header-default .gc-svg-icon,
.has-default-header-type-dark .gc-side-panel .gc-svg-icon,
.has-default-header-type-dark .gc-header-mobile .gc-svg-icon,
.has-default-header-type-dark .cart-area .del-icon a svg,
.has-default-header-type-dark .wishlist-area .gc-svg-icon.mini-icon,
.has-default-header-type-dark .compare-area .gc-svg-icon.mini-icon {
  fill: var(--gc-light);
  color: var(--gc-light);
}

.has-default-header-type-dark a:hover {
  color: var(--gc-gray);
  text-decoration: none;
  opacity: 0.8;
}

.has-default-header-type-dark .header-text-logo,
.has-default-header-type-dark .header-text-logo,
.has-default-header-type-dark .gc-header-top-menu-area>ul>li.menu-item>a,
.has-default-header-type-dark .gc-header-top-mini-menu-area ul.navigation-mini li a,
.has-default-header-type-dark .gc-header-top-menu-area ul li .submenu>li.menu-item>a,
.has-default-header-type-dark .sliding-menu li a,
.has-default-header-type-dark .sliding-menu li .sliding-menu__nav,
.has-default-header-type-dark .sliding-menu li .sliding-menu__nav.sliding-menu__back,
.has-default-header-type-dark .gc-header-mobile-sidebar a,
.has-default-header-type-dark .panel-top-title,
.has-default-header-type-dark .panel-top-title .nt-strong-sfot,
.has-default-header-type-dark .account-area li.menu-item a,
.has-default-header-type-dark .contact-area label,
.has-default-header-type-dark .contact-area input,
.has-default-header-type-dark .contact-area input[type="submit"],
.has-default-header-type-dark .contact-area texyarea,
.has-default-header-type-dark .contact-area input:focus,
.has-default-header-type-dark .contact-area textarea:focus,
.has-default-header-type-dark .search-area-top input,
.has-default-header-type-dark .category-area .category-title,
.has-default-header-type-dark .header-left-side-menu-form-container-title .gc-meta-title,
.has-default-header-type-dark .header-left-side-menu-form-container-title h6,
.has-default-header-type-dark .search-area-top input:focus,
.has-default-header-type-dark .contact-area .wpcf7 form .wpcf7-response-output,
.has-default-header-type-dark .cart-item-title .cart-name,
.has-default-header-type-dark .gc-content-info .product-name,
.has-default-header-type-dark .cart-item-title .cart-name,
.has-default-header-type-dark .gc-content-info .product-name,
.has-default-header-type-dark .gc-content-item .gc-content-info .gc-btn-small,
.has-default-header-type-dark .cart-area .cart-total-price,
.has-default-header-type-dark .cart-area .quantity input.qty,
.has-default-header-type-dark .panel-content .quantity input.qty,
.has-default-header-type-dark .gc-side-panel .gc-small-title,
.has-default-header-type-dark .gc-side-panel .gc-small-title a,
.has-default-header-type-dark .gc-header-mobile .gc-small-title,
.has-default-header-type-dark .gc-header-mobile .gc-small-title a,
.has-default-header-type-dark .autocomplete-suggestion .gc-small-title,
.has-default-header-type-dark .autocomplete-suggestion .gc-small-title a,
.has-default-header-type-dark .gc-header-mobile .gc-btn-small,
.has-default-header-type-dark .gc-side-panel .gc-btn-small,
.has-default-header-type-dark .gc-header-top-mini-menu-area ul.navigation-mini li.menu-item-circle-icon a,
.has-default-header-type-dark .gc-header-default .menu-item-phone-number,
.has-default-header-type-dark .gc-popup-search-panel .no-result+.autocomplete-suggestions .autocomplete-suggestion strong,
.has-default-header-type-dark .popup-search-style+.gc-product-categories ul.gc-wc-category-list li>a,
.has-default-header-type-dark .gc-ajax-product-title {
  color: var(--gc-light);
}

.has-default-header-type-dark .gc-panel-close-button:before,
.has-default-header-type-dark .gc-panel-close-button:after {
  background-color: var(--gc-light);
}

.has-default-header-type-dark .gc-cart-total {
  color: #bbb;
}

.has-default-header-type-dark .contact-area input:focus,
.has-default-header-type-dark .contact-area textarea:focus {
  border-color: var(--gc-primary);
}

.has-default-header-type-dark .panel-top-title strong,
#nt-woo-single .gc-product-showcase.gc-bg-dark button.cr-qna-ans-button,
.has-default-header-type-dark .gc-ajax-product-search.style-inline .gc-ajax-search-results.active {
  background-color: var(--gc-dark);
}

.has-default-header-type-dark .gc-header-default input[type="checkbox"]:after,
.has-default-header-type-dark .gc-header-default input[type="radio"]:checked:after {
  border-color: var(--gc-black-soft);
}

.has-default-header-type-dark .gc-header-default input[type="checkbox"]:after,
.has-default-header-type-dark .gc-header-default input[type="radio"]:checked:after {
  background-color: var(--gc-dark);
}

.has-default-header-type-dark .gc-header-mobile-content {
  background-color: var(--gc-dark);
}

.has-default-header-type-dark .action-content,
.has-default-header-type-dark .sliding-menu {
  background-color: transparent;
}

.has-default-header-type-dark .sliding-menu li.current-menu-parent>.sliding-menu__nav,
.has-default-header-type-dark .sliding-menu li.current-menu-item>.sliding-menu__nav,
.has-default-header-type-dark .sliding-menu li.current-menu-item>a,
.has-default-header-type-dark .sliding-menu li a:hover,
.has-default-header-type-dark .sliding-menu li.active a,
.has-default-header-type-dark .sliding-menu li .sliding-menu__nav:hover,
.has-default-header-type-dark .account-area li.menu-item a:hover,
.has-default-header-type-dark .gc-header-top-menu-area ul li .submenu>li.menu-item>a:hover,
.has-default-header-type-dark .gc-header-top-menu-area ul li .submenu>li.menu-item.active>a,
.has-default-header-type-dark .gc-sidemenu-copyright a {
  color: var(--gc-primary);
}

.has-default-header-type-dark .gc-content-item .gc-content-del-icon:hover svg,
.has-default-header-type-dark .cart-area .del-icon:hover svg {
  fill: var(--gc-primary);
}

.has-default-header-type-dark .panel-header-btn.active {
  fill: var(--gc-dark);
}

.has-default-header-type-dark .sliding-menu .sliding-menu__back:after {
  border-bottom-color: var(--gc-gray);
}

.has-default-header-type-dark .gc-header-mobile-sidebar {
  background: #222;
}

.has-default-header-type-dark .sidebar-top-action .top-action-btn.active,
.has-default-header-type-dark .panel-header-btn.active,
.has-default-header-type-dark .cart-area .quantity,
.has-default-header-type-dark .panel-header .gc-panel-close-button,
.has-default-header-type-dark .gc-panel-close-button,
.has-default-header-type-dark .gc-filter-close {
  background: var(--gc-dark);
}

.has-default-header-type-dark .gc-header-mobile .gc-btn,
.has-default-header-type-dark .gc-side-panel .gc-btn,
.has-default-header-type-dark .cart-bottom-btn .gc-btn,
.has-default-header-type-dark .woocommerce-form-login-toggle,
.has-default-header-type-dark .woocommerce-form-coupon-toggle {
  border: 1px solid var(--gc-dark);
  background-color: var(--gc-dark);
}

.has-default-header-type-dark .woocommerce-form-coupon-toggle {
  border: 0;
  padding: 0;
  background: none;
  background-color: transparent;
  border: 0;
}

.has-default-header-type-dark .gc-header-mobile .gc-btn-small,
.has-default-header-type-dark .gc-side-panel .gc-btn-small {
  margin-top: 10px;
}

.has-default-header-type-trans .header-spacer {
  display: none;
}

.single-product.has-default-header-type-trans:not(.shop-single-layout-stretch) .header-spacer {
  display: block;
}

.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-mobile-top,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-mobile-top,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default {
  background: #00000014;
  border-color: #ffffff2e;
}

.has-default-header-type-trans:not(.scroll-start) .gc-header-mobile-top .gc-svg-icon,
.has-default-header-type-trans:not(.scroll-start) .gc-header-default .gc-svg-icon {
  fill: var(--gc-light);
  color: var(--gc-light);
}

.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default .gc-svg-icon,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-mobile-top .gc-svg-icon {
  fill: var(--gc-dark);
  color: var(--gc-dark);
}

.has-default-header-type-trans:not(.scroll-start) .gc-header-default .header-text-logo,
.has-default-header-type-trans:not(.scroll-start) .gc-header-mobile-top .header-text-logo,
.has-default-header-type-trans:not(.scroll-start) .gc-header-top-menu-area>ul>li.menu-item>a,
.has-default-header-type-trans:not(.scroll-start) .gc-header-default .menu-item-phone-number,
.has-default-header-type-trans:not(.scroll-start) .gc-header-top-mini-menu-area ul.navigation-mini li a {
  color: var(--gc-light);
}

.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default .header-text-logo,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-mobile-top .header-text-logo,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-top-menu-area>ul>li.menu-item>a,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default .menu-item-phone-number,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-top-mini-menu-area ul.navigation-mini li a {
  color: var(--gc-dark);
}

.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover {
  background: var(--gc-light);
}

.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .header-text-logo,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .gc-svg-icon,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .gc-header-top-menu-area>ul>li.menu-item.active>a,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .menu-item-phone-number,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .gc-header-top-mini-menu-area ul.navigation-mini li.active a {
  color: var(--gc-dark);
}

.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .gc-header-top-menu-area>ul>li.menu-item>a,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .gc-header-top-mini-menu-area ul.navigation-mini li a {
  color: var(--gc-gray-dark);
}

.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover .header-text-logo,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover .gc-svg-icon,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover .gc-header-top-menu-area>ul>li.menu-item>a,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover .menu-item-phone-number,
.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover .gc-header-top-mini-menu-area ul.navigation-mini li a {
  color: var(--gc-gray-dark);
}

.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover .gc-svg-icon {
  fill: var(--gc-dark);
  color: var(--gc-dark);
}

.has-default-header-type-trans-dark:not(.scroll-start) .gc-header-default.trans-hover .gc-svg-icon {
  fill: var(--gc-dark);
  color: var(--gc-dark);
}

.has-default-header-type-trans.header-trans-dark:not(.scroll-start) .gc-header-default.trans-hover,
.has-default-header-type-trans.header-trans-light:not(.scroll-start) .gc-header-default.trans-hover {
  background: var(--gc-light);
  transition-delay: 0.2s;
  transition: transform 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.075s,
    opacity 0.65s cubic-bezier(0.165, 0.84, 0.44, 1) 0.15s;
  transition-delay: 0.2s;
}

.has-default-header-type-trans.header-trans-light:not(.scroll-start) .mobile-toggle .menu-title {
  color: var(--gc-light);
}

@media (max-width: 1280px) {
  header.gc-header-default {
    display: none;
  }
}

.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
  border-top-right-radius: 0;
}

.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
  border-top-left-radius: 0;
}

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
  border-bottom-right-radius: 0;
}

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
  border-bottom-left-radius: 0;
}

.ui-spinner {
  height: 54px;
}

.ui-spinner-input {
  height: 52px;
  padding: 0;
  margin: 0;
  padding-right: 20px;
  padding-left: 30px;
}

.ui-spinner-button {
  cursor: pointer;
}

.ui-tooltip {
  padding: 5px 15px;
}

.ui-tooltip.mini-tooltip {
  line-height: 1;
  padding: 7px 15px;
}

.ui-tooltip:after {
  position: absolute;
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
}

.ui-tooltip.is-top:after {
  bottom: -10px;
  right: 50%;
  margin-right: -10px;
  border-width: 10px 10px 0 10px;
  border-color: var(--gc-black) transparent transparent transparent;
}

.ui-tooltip.is-right:after {
  top: 50%;
  right: -10px;
  margin-top: -10px;
  border-width: 10px 0 10px 10px;
  border-color: transparent transparent transparent var(--gc-dark);
}

.ui-tooltip.is-left:after {
  top: 50%;
  left: -10px;
  margin-top: -10px;
  border-width: 10px 10px 10px 0;
  border-color: transparent var(--gc-black) transparent transparent;
}

.ui-tooltip.is-bottom:after {
  top: -10px;
  right: 50%;
  margin-right: -10px;
  border-width: 0 10px 10px 10px;
  border-color: transparent transparent var(--gc-black) transparent;
}

.ui-tooltip.is-top.mini-tooltip:after {
  bottom: -5px;
  right: 50%;
  margin-right: -5px;
  border-width: 5px 5px 0 5px;
  border-color: var(--gc-black) transparent transparent transparent;
}

.ui-tooltip.is-right.mini-tooltip:after {
  top: 50%;
  right: -5px;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-color: transparent transparent transparent var(--gc-dark);
}

.ui-tooltip.is-left.mini-tooltip:after {
  top: 50%;
  left: -5px;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-color: transparent var(--gc-black) transparent transparent;
}

.ui-tooltip.is-bottom.mini-tooltip:after {
  top: -5px;
  right: 50%;
  margin-right: -5px;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent var(--gc-black) transparent;
}

.ui-tooltip.ui-widget-shadow {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.ui-tooltip.ui-widget-content {
  color: var(--gc-light);
  background-color: var(--gc-dark);
}

.ui-tooltip.ui-widget.ui-widget-content {
  border: 0;
}

.ui-widget.ui-widget-content {
  border-color: var(--gc-dark);
}

.ui-widget-content {
  border-color: var(--gc-dark);
  color: var(--gc-light);
}

.ui-widget-content a {
  color: var(--gc-light);
}

.ui-widget-header {
  border-color: var(--gc-dark);
  background: var(--gc-dark);
  color: var(--gc-light);
  font-weight: 400;
}

.ui-widget-header a {
  color: var(--gc-light);
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default,
.ui-button,
html .ui-button.ui-state-disabled:hover,
html .ui-button.ui-state-disabled:active {
  color: var(--gc-light);
  border-color: var(--gc-dark);
  background: var(--gc-dark);
}

.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited,
a.ui-button,
a:link.ui-button,
a:visited.ui-button,
.ui-button {
  color: var(--gc-dark);
}

.ui-state-hover,
.ui-widget-content .ui-state-hover,
.ui-widget-header .ui-state-hover,
.ui-state-focus,
.ui-widget-content .ui-state-focus,
.ui-widget-header .ui-state-focus,
.ui-button:hover,
.ui-button:focus {
  color: var(--gc-light);
  border-color: var(--gc-dark);
  background: var(--gc-dark);
}

.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited,
a.ui-button:hover,
a.ui-button:focus {
  color: var(--gc-light);
}

.ui-state-active,
.ui-widget-content .ui-state-active,
.ui-widget-header .ui-state-active,
a.ui-button:active,
.ui-button:active,
.ui-button.ui-state-active:hover {
  border-color: var(--gc-dark);
  background: var(--gc-dark);
}

.ui-icon-background,
.ui-state-active .ui-icon-background {
  border-color: var(--gc-dark);
}

.gc-button-wrapper {
  display: flex;
}

.gc-button-wrapper.is-full .gc-btn {
  width: 100%;
}

.gc-fullwidth {
  width: 100%;
}

.gc-btn {
  height: 42px;
  font-size: 13px;
  font-weight: 400;
  color: var(--gc-light);
  padding: 0 15px;
  min-width: 160px;
  text-transform: capitalize;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  border: 0;
  border-radius: 5px;

  &.min-inherit {
    min-width: inherit;
    border-radius: 1px;
  }
}

.gc-btn-text .gc-btn,
.gc-btn-text.gc-btn,
.gc-btn-text .gc-btn:hover,
.gc-btn-text.gc-btn:hover {
  font-size: 13px;
  font-weight: 500;
  color: var(--gc-dark);
  padding: 0;
  min-width: auto;
  text-transform: capitalize;
  letter-spacing: 1px;
  display: inline-block;
  background: transparent;
  border: none;
  height: auto;
}

.gc-btn-text .gc-btn:hover,
.gc-btn-text.gc-btn:hover {
  color: var(--gc-primary);
}

.gc-input-large,
.gc-btn-large {
  height: 45px;
  font-size: 16px;
}

.gc-input-medium,
.gc-btn-medium {
  height: 38px;
  font-size: 14px;
}

.gc-input-small,
.gc-btn-small {
  height: 33px;
  font-size: 14px;
  letter-spacing: 0;
  text-align: left;
}

.gc-input {
  padding: 0 15px;
}

.gc-input-large {
  height: 48px;
}

.gc-input-medium {
  height: 38px;
  font-size: 14px;
}

.gc-input-small {
  height: 33px;
  font-size: 13px;
  min-width: 160px;
}

.gc-bg {
  background: var(--gc-bg-soft);
}

.gc-bg-primary {
  //color: var(--gc-purple-softer);
  border-color: var(--gc-purple);
  background-color: var(--gc-purple);
}

.gc-bg-primary:hover {
  color: var(--gc-light);
}

.gc-btn-black {
  color: var(--gc-light);
  border-color: var(--gc-dark);
  background-color: var(--gc-dark);
}

.gc-btn-black:hover {
  color: var(--gc-light);
  background-color: var(--gc-primary);
}

.gc-bg-gray {
  color: var(--gc-dark);
  border-color: var(--gc-dark);
  background-color: var(--gc-dark);
}

.gc-bg-gray:hover {
  color: var(--gc-dark);
}

.gc-bg-gray-soft {
  color: var(--gc-dark);
  border-color: var(--gc-gray-soft);
  background-color: var(--gc-gray-soft);
}

.gc-bg-gray-soft:hover {
  color: var(--gc-dark);
}

.gc-bg-gray-softer {
  color: var(--gc-dark);
  border-color: var(--gc-gray-softer);
  background-color: var(--gc-gray-softer);
}

.gc-bg-gray-softer:hover {
  color: var(--gc-dark);
}

.gc-btn-border {
  background-color: var(--gc-light);
  border: 2px solid var(--gc-dark);
  color: var(--gc-dark);
}

.gc-btn-border-primary {
  background-color: var(--gc-light);
  color: var(--gc-dark);
  border: 2px solid var(--gc-primary);
}

.gc-btn-border-primary:hover {
  color: var(--gc-light);
  background-color: var(--gc-primary);
}

.gc-btn-border-black {
  background-color: var(--gc-light);
  color: var(--gc-dark);
  border: 0;
  display: block;
  padding: 0;
}

.gc-btn-border-black:hover {
  color: var(--gc-light);
  background-color: var(--gc-dark);
}

.gc-btn-border-gray {
  background-color: var(--gc-light);
  color: var(--gc-dark);
  border: 2px solid var(--gc-dark);
}

.gc-btn-border-gray:hover {
  color: var(--gc-light);
  background-color: var(--gc-dark);
}

.gc-btn-border-gray-soft {
  background-color: var(--gc-light);
  color: var(--gc-dark);
  border: 2px solid var(--gc-soft);
}

.gc-btn-border-gray-soft:hover {
  background-color: var(--gc-soft);
}

.gc-btn-border-gray-softer {
  background-color: var(--gc-light);
  color: var(--gc-dark);
  border: 2px solid var(--gc-gray-softer);
}

.gc-btn-border-gray-softer:hover {
  background-color: var(--gc-gray-softer);
}

.gc-list-disc ul,
ul.gc-list-disc {
  list-style: none;
}

.gc-list-disc li,
ul.gc-list-disc li {
  position: relative;
}

.gc-list-disc li:before,
ul.gc-list-disc li:before {
  position: absolute;
  top: 10px;
  left: -20px;
  padding: 0;
  border: 1px solid var(--gc-border);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  line-height: 1;
  width: 8px;
  height: 8px;
  content: "";
}

.gc-list-disc li:after,
ul.gc-list-disc li:after {
  position: absolute;
  top: 13px;
  left: -17px;
  padding: 0;
  background: var(--gc-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  line-height: 1;
  width: 2px;
  height: 2px;
  content: "";
}

.nt-logo img {
  max-width: 200px;
}

.has-default-header-type-trans.header-trans-light .main-logo {
  background: #fff;
  border-radius: 3px;
}

.gc-header-mobile-sidebar-logo .nt-logo img {
  max-width: 80px;
}

a.nt-logo.logo-type-text {
  position: relative;
  display: block;
  line-height: 1;
}

.header-text-logo {
  font-size: 24px;
  text-transform: uppercase;
  font-weight: 600;
  line-height: 1;
  display: block;
  color: var(--gc-dark);
}

.gc-header-mobile-sidebar-logo .header-text-logo-mini,
.header-text-logo-mini {
  display: none;
}

.scroll-start .gc-section-fixed-yes {
  position: fixed;
  width: 100%;
  top: 0;
}

.scroll-start .has-sticky-logo .main-logo,
.has-sticky-logo .sticky-logo {
  display: none;
}

.scroll-start .has-sticky-logo .sticky-logo {
  display: block;
}

@media (max-width: 768px) {
  .header-text-logo {
    line-height: 1;
  }
}

.gc-page-hero {
  background-position: center;
  background-size: cover;
  height: 150px;
  background-color: var(--gc-gray-soft);
  display: flex;
  align-items: center;
}

.gc-page-hero-content h2 {
  text-transform: capitalize;
  margin-bottom: 0;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  font-size: 24px;
}

.gc-page-hero.page-hero-mini {
  height: 80px;
}

.gc-page-hero.page-hero-mini .gc-page-hero-content {
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .gc-page-hero.page-hero-mini .gc-page-hero-content {
    flex-direction: column;
  }

  .gc-page-hero.page-hero-mini {
    height: 130px;
  }

  .gc-page-hero.page-hero-mini .gc-page-hero-content h2 {
    margin: 0 0 10px 0;
    text-align: center;
  }
}

.gc-breadcrumb li {
  list-style: none;
}

.gc-breadcrumb li,
.gc-breadcrumb li a {
  display: inline-block;
  color: var(--gc-gray-dark);
  padding: 0;
  max-width: 100%;
}

.breadcrumb-item+.breadcrumb-item::before {
  content: "";
  display: inline-block;
  padding-right: 0;
  width: 4px;
  height: 4px;
  background-color: var(--gc-gray);
  border-radius: 50%;
  margin-bottom: 2px;
  margin-right: 10px;
  margin-left: 10px;
}

@media (max-width: 576px) {

  .gc-breadcrumb li,
  .gc-breadcrumb li a {
    font-size: 14px;
  }

  .breadcrumb-item+.breadcrumb-item::before {
    margin-right: 5px;
    margin-left: 5px;
  }
}

.gc-viewed-offer-time .offer-time-text {
  margin-bottom: 0;
  color: var(--gc-purple-dark);
  font-weight: 500;
}

.gc-viewed-offer-time .offer-time-text span {
  color: var(--gc-light);
}

.gc-product-top-nav {
  margin: 0 0 21px;
}

.gc-product-summary {
  .gc-summary-item {
    margin: 0;
    position: relative;

    &.gc-flex {}

    &+.gc-summary-item {
      margin-top: 20px;
    }

    &.pid-description {
      button {
        &.collapsed {
          &:after {
            content: "+" !important;
          }
        }

        // &:after {
        //   content: "-" !important;
        //   font-size: 16px !important;
        // }
      }
    }

    &.gc-product-title {
      margin-top: -5px !important;
      font-size: 32px;
    }

    .gc-primary-color {
      &.del {
        color: var(--gc-purple);
        text-decoration: line-through;
      }
    }

    &.gc-price.price {
      font-size: 18px;
      line-height: 1;
      position: relative;
      font-weight: 600;

      span {
        &+p {
          &.stock.gc-stock-status {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }

      p.stock.gc-stock-status {
        font-size: 12px;
        padding: 5px 10px;
        letter-spacing: 0.2px;
        background: var(--gc-bg-soft);
        border-radius: 10px;
        display: inline-block;
        border: 1px dashed var(--gc-border);
      }
    }

    .gc-summary-item .gc-coming-time {
      min-width: 26%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--gc-purple-dark);
      padding: 10px 15px;
      border-radius: 4px;
      flex-direction: row;
      flex-wrap: nowrap;
      align-content: center;
      max-width: 240px;
      margin-bottom: 0;
      box-shadow: 0 0 10px 0 var(--gc-purple-softer);
    }

    .product-details__short-description {
      color: #7a7a7a;
    }

    &.gc-viewed-offer-time {
      position: relative;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: var(--gc-purple-bg);
      padding: 15px 20px;
      border-radius: 5px;
      border: 1px solid var(--gc-purple);

      .offer-time-text {
        margin-bottom: 0;
        color: var(--gc-purple-dark);
        font-weight: 500;
      }

      .gc-coming-time {
        min-width: 26%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--gc-purple-dark);
        padding: 10px 15px;
        border-radius: 4px;
        flex-direction: row;
        flex-wrap: nowrap;
        align-content: center;
        max-width: 240px;
        margin-bottom: 0;
        box-shadow: 0 0 10px 0 var(--gc-purple-softer);
      }
    }

    &.gc-single-product-stock {
      background: var(--gc-yellow-bg);
      padding: 10px;
      border-radius: 5px;
      border: 1px solid;
      border-color: var(--gc-yellow);

      .stock-details {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;

        .stock-sold,
        .current-stock {
          display: inline-block;
          text-transform: uppercase;
          font-size: 11px;
          font-weight: 500;
          letter-spacing: 0.4px;
          color: var(--gc-green);
        }
      }

      .gc-product-stock-progress {
        background: var(--gc-green-soft);
        border-radius: 15px;
        padding: 4px 5px;

        .gc-product-stock-progressbar {
          display: block;
          height: 5px;
          min-width: 1px;
          background: var(--gc-green);
          border-radius: 2px;
          min-width: 5%;
        }
      }
    }

    &.cart {
      .screen-reader-text {
        display: none;
      }

      .quantity {
        width: 100%;
        height: 38px;
        max-width: 140px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-right: 20px;
        border: 2px solid var(--gc-border);
        min-height: 46px;
        border-radius: 5px;
        padding: 0 10px;
        max-width: 120px;

        @media (max-width: 480px) {
          max-width: 100%;
          margin-right: 0;
          margin-bottom: 10px;
        }

        .quantity-button {
          input {
            border: 0;
            font-size: 12px;
            padding: 0;
            text-align: center;
            height: 100%;
            flex: 1;
            width: auto;
            max-width: calc(100% - 60px);
          }

          &.minus {
            &:before {
              content: "";
              width: 6px;
              height: 1px;
              background: var(--gc-dark);
            }
          }

          &.plus,
          &.minus {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22px;
            flex: 0 0 22px;
            cursor: pointer;
            font-size: 14px;
            color: var(--gc-black);
            z-index: 1;
            position: relative;
          }
        }
      }

      .input-text {
        border-style: solid;
        border-radius: 5px 5px 5px 5px;
        height: 38px;
        font-size: 14px;
        border: none;
        text-align: center;
      }

      .single_add_to_cart_button {
        min-width: 52%;
        min-height: 46px;

        @media (max-width: 480px) {
          width: 100%;
          margin-bottom: 10px;
        }
      }
    }

    .gc-product-button {
      margin-left: 5px;
      display: flex;
      cursor: pointer;
      align-items: center;
      justify-content: center;
      background: var(--gc-bg-soft);
      width: 40px;
      height: 46px;
      border-radius: 5px;

      @media (max-width: 480px) {
        // min-width: 100%;
        border: 1px solid var(--gc-border);
        margin-bottom: 10px;
        margin-left: 0px;
      }

      &:hover:not(.gc-wishlist-btn) {
        background: var(--gc-dark);
        color: var(--gc-light);
        fill: var(--gc-light);

        svg {
          color: var(--gc-light);
          fill: var(--gc-light);
        }
      }
    }

    &.gc-product-popup-details {
      &>div {
        margin-bottom: 5px;
      }

      .gc-align-center {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        column-gap: 5px;

        svg {
          max-width: 20px;
        }
      }
    }
  }
}

.gc-product-summary .width-full .gc-coming-time {
  width: 100%;
  max-width: none;
}

.gc-product-summary .has-after-text .gc-coming-time {
  margin-bottom: 0;
}

.gc-product-summary .gc-viewed-offer-time.type-1,
.gc-product-summary .gc-viewed-offer-time.type-2,
.gc-product-summary .gc-viewed-offer-time.type-3,
.gc-product-summary .gc-viewed-offer-time.type-4 {
  border: 1px dashed var(--gc-light);
  padding: 20px;
  text-align: center;
  border-radius: 4px;
}

.gc-product-summary .gc-viewed-offer-time.type-1 .gc-coming-time,
.gc-product-summary .gc-viewed-offer-time.type-2 .gc-coming-time,
.gc-product-summary .gc-viewed-offer-time.type-3 .gc-coming-time,
.gc-product-summary .gc-viewed-offer-time.type-4 .gc-coming-time {
  background-color: transparent;
  padding: 0;
  font-size: 24px;
  margin: 0;
  max-width: none;
}

.gc-product-summary .gc-viewed-offer-time.type-2 .gc-coming-time .time-count span,
.gc-product-summary .gc-viewed-offer-time.type-3 .gc-coming-time .time-count span {
  padding: 8px;
  border: 1px solid var(--gc-light);
}

.gc-product-summary .gc-viewed-offer-time.type-3 .gc-coming-time .time-count span {
  background-color: var(--gc-light);
  color: var(--gc-light);
}

.gc-summary-item.gc-viewed-offer-time.type-4 {
  display: flex;
  align-items: center;
}

.gc-product-summary .gc-viewed-offer-time.type-4 .gc-coming-time-icon {
  margin: 0;
  margin-right: 20px;
  padding-right: 20px;
  border-right: 1px dashed var(--gc-gray);
}

.gc-product-summary .gc-viewed-offer-time.type-4 svg {
  max-width: 80px;
  max-height: 80px;
}

p.offer-time-text-after {
  margin-top: 5px;
}

.gc-product-summary .gc-summary-item .gc-coming-time.time-finish {
  flex-direction: column;
  width: 100%;
  max-width: 100%;
  text-align: center;
  padding: 15px;
}

.time-expired-count {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.gc-product-summary .gc-summary-item .separator-none .time-count+.time-count {
  margin-left: 10px;
}

.gc-countdown-title-wrapper.gc-display-inline {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.gc-countdown-title-wrapper.gc-display-inline .gc-super-deal-title {
  margin: 0;
  margin-right: 30px;
}

.gc-coming-time {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.gc-coming-time .time-count {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  min-width: 10px;
  padding: 0;
  letter-spacing: 1px;
  position: relative;
  color: var(--gc-light);
  font-weight: 500;
}

.gc-coming-time:not(.separator-none) .time-count+.time-count:before {
  position: relative;
  content: ":";
  margin: 0 7px;
  color: currentColor;
  font-size: 13px;
  font-weight: 500;
}

.gc-coming-time.separator-2 .time-count+.time-count:before {
  content: "/";
}

.gc-coming-time.separator-3 .time-count+.time-count:before {
  content: "-";
}

.gc-coming-time.separator-4 .time-count+.time-count:before {
  content: "|";
}

.gc-coming-time .time-count span+span {
  margin-top: 5px;
}

.gc-super-deal-area .gc-super-deal-title-wrapper {
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 30px;
}

.gc-super-deal-area .gc-super-deal-title-wrapper .gc-super-deal-title {
  margin: 0;
  margin-right: 30px;
}

.gc-list-product-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--gc-dark);
  margin-bottom: 40px;
}

.gc-list-product-top .title {
  font-size: 22px;
  margin-bottom: 0;
  width: 70%;
}

.gc-list-product-top .view-all {
  font-size: 16px;
  font-weight: 600;
}

.gc-list-product-item {
  border: 1px solid #ececec;
  display: flex;
  align-items: center;
  padding: 20px 10px;
  position: relative;
  transition: 0.3s linear;
  background: var(--gc-light);
}

.gc-list-product-thumb {
  margin-right: 25px;
  position: relative;
}

.gc-list-product-content {
  flex-grow: 1;
}

.gc-list-product-content h6 {
  font-size: 16px;
  margin-bottom: 7px;
}

.gc-list-product-content p {
  margin-bottom: 7px;
}

.gc-list-product-item .rating {
  display: flex;
}

.gc-list-product-item .rating .star-rating {
  float: none;
}

.gc-list-product-item:hover {
  border-color: var(--gc-dark);
}

.gc-list-product-item .gc-label {
  position: absolute;
  left: 10px;
  top: 10px;
  padding: 0 5px;
}

.woocommerce .gc-list-product-thumb img,
.gc-list-product-thumb img {
  max-width: 100px;
}

.nt-gc-content blockquote .quote-icon {
  margin-right: 25px;
}

.nt-gc-content blockquote {
  margin: 30px 0 30px;
  font-size: 18px;
  color: var(--gc-dark);
  font-weight: 500;
  line-height: 1.9;
  background: var(--gc-gray-soft);
}

.nt-gc-content blockquote p {
  margin: 0;
}

.nt-gc-content .gc-post-content-wrapper>ul:last-child {
  margin-bottom: 0;
}

.gc-blog-post-meta-bottom {
  margin-top: 20px;
  padding-top: 50px;
  border-top: 1px solid var(--gc-border);
}

.gc-blog-post-meta-bottom .gc-meta-label {
  font-weight: 500;
  color: var(--gc-dark);
}

.gc-blog-post-meta-bottom a {
  color: var(--gc-gray-dark);
}

.gc-blog-post-meta-bottom .gc-post-meta+.gc-post-meta {
  margin-top: 5px;
}

.gc-blog-post-meta-top {
  margin-bottom: 20px;
}

.single .gc-blog-post-meta-top {
  margin-bottom: 20px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.3px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.single .gc-blog-post-meta-top a {
  margin-right: 15px;
  color: var(--gc-black-soft);
}

.comment-text p {
  margin-bottom: 0;
  padding-right: 100px;
}

.blog-comment ul li.comment-reply {
  margin-left: 80px;
}

.blog-comment ul li:last-child {
  margin-bottom: 0;
}

.comment-form textarea {
  width: 100%;
  padding: 20px 20px;
  margin-bottom: 0;
  height: 160px;
  transition: 0.3s;
  font-size: 14px;
  color: var(--gc-black-soft);
}

.comment-form input:not(.gc-btn):not([type="checkbox"]) {
  width: 100%;
  padding: 12px 20px;
  margin-bottom: 20px;
  color: var(--gc-black-soft);
  transition: 0.3s;
  font-size: 14px;
}

.comment-form input:not(.gc-btn)::placeholder,
.comment-form textarea::placeholder {
  color: var(--gc-dark);
}

.comment-check-box input {
  width: auto;
  margin: 0;
}

.comment-check-box label {
  font-size: 14px;
  color: var(--gc-black-soft);
  margin: 0;
  user-select: none;
}

.comment-check-box {
  display: flex;
  align-items: flex-start;
}

.gc-error-txt {
  font-size: 150px;
  font-weight: 600;
  margin-bottom: 20px;
  line-height: 1;
  color: var(--gc-primary);
}

.gc-error-content h5 {
  font-weight: 500;
  margin-bottom: 15px;
}

.gc-error-content p {
  margin-bottom: 0;
}

.gc-error-content .search_form {
  margin-top: 25px;
}

.gc-error-area .search_form form {
  position: relative;
}

.gc-error-area .search_form form input {
  height: 50px;
  padding: 5px 60px 5px 20px;
  margin-bottom: 20px;
  box-shadow: none;
}

.gc-error-content .gc-fill-out {
  background: var(--gc-primary);
}

.gc-error-area .search_form form input:focus {
  height: 50px;
}

.gc-error-area .search_form .icon_search {
  position: absolute;
  right: 18px;
  top: 50%;
  padding: 0;
  -moz-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-radius: 0;
  padding-left: 15px;
  box-shadow: none;
  border: none;
  border-left: 1px solid #ddd;
  background: transparent;
  color: var(--gc-black-soft);
}

.gc-error-area .search_form .icon_search i {
  font-size: 22px;
  margin-right: 0;
}

.gc-error-area .gc-btn {
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
}

.gc-error-area .text-center {
  text-align: center;
}

.gc-vertical-menu-wrapper-edit-mode,
.gc-vertical-menu-wrapper {
  display: inline-block;
  position: relative;
}

.gc-vertical-menu-toggle {
  padding: 10px 20px;
  border: 1px solid var(--gc-dark);
  display: inline-block;
  cursor: pointer;
}

.gc-vertical-menu {
  padding: 15px 0;
  z-index: 999;
  border: 1px solid var(--gc-gray-dark);
  position: absolute;
  width: 100%;
  background: var(--gc-light);
  left: 0;
  top: calc(100% - 2px);
  min-width: max-content;
}

.gc-vertical-menu:not(.drop-active) {
  display: none;
}

.gc-mega-menu-content {
  position: absolute;
  display: flex;
  left: 100%;
  top: 0;
  background-color: var(--gc-light);
  border: 1px solid var(--gc-dark);
  width: 750px;
  padding: 20px;
  text-align: left;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: 0.3s linear;
  z-index: 2;
}

.gc-vertical-menu-item:hover .gc-mega-menu-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.gc-vertical-menu ul.navigation,
.gc-vertical-menu .submenu {
  list-style: none;
}

.gc-vertical-menu .has-dropdown .submenu {
  position: absolute;
  left: 100%;
  top: -1px;
  background-color: var(--gc-light);
  border: 1px solid var(--gc-dark);
  width: auto;
  min-width: 150px;
  padding: 20px 0;
  text-align: left;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: 0.3s linear;
  z-index: 2;
  min-width: max-content;
}

.gc-vertical-menu.dropdown-right .has-dropdown .submenu {
  right: 100%;
  left: auto;
}

.gc-vertical-menu .has-dropdown:hover>.submenu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.gc-vertical-menu .navigation .menu-item,
.gc-vertical-menu .gc-vertical-menu-item {
  padding: 0 20px;
  position: relative;
}

span.gc-vertical-menu-item-title,
.gc-vertical-menu .navigation .menu-item>a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 0 0 100%;
  width: 100%;
  font-size: 12px;
  font-weight: 500;
}

.gc-vertical-menu .navigation .menu-item .submenu>a {
  display: block;
  width: 100%;
}

.gc-vertical-menu .dropdown-btn {
  transform: rotate(-90deg);
  margin-left: 10px;
}

.gc-vertical-menu .navigation li.has-dropdown li.has-dropdown li.has-dropdown a>.dropdown-btn,
.gc-vertical-menu .submenu .menu-label {
  display: none;
}

span.gc-vertical-menu-item-title i {
  margin-left: 10px;
}

.gc-posts-row {
  margin-bottom: -40px;
}

.gc-blog-card .gc-posts-row {
  margin-bottom: -30px;
}

.syler-blog-slider .gc-blog-post-content {
  padding: 30px;
}

.gc-blog-classic .excerpt-none .gc-blog-post-content .gc-post-title {
  margin-bottom: 0;
}

.blog-sticky {
  background: var(--gc-dark);
  color: var(--gc-light);
  padding: 3px 10px;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 15px;
  display: inline-block;
  border-radius: 3px;
}

.gc-blog-grid {
  display: flex;
  flex-wrap: wrap;
}

.gc-blog-grid>* {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.gc-post-item {
  flex: 0 0 auto;
  width: 25%;
}

.gc-posts-row .gc-blog-posts-item {
  margin-bottom: 40px;
}

.gc-posts-row .gc-blog-posts-item.style-card {
  margin-bottom: 30px;
}

.gc-blog-post-content {
  background-color: var(--gc-light);
}

.gc-blog-thumb+.gc-blog-post-content {
  margin-top: 20px;
}

.gc-blog-post-content .gc-post-meta-title,
.gc-blog-post-content .gc-post-title,
.gc-blog-post-content .gc-post-excerpt {
  margin: 0;
}

.gc-blog-slider .gc-blog-post-content {
  margin: 0;
  padding: 20px;
}

.gc-post-excerpt {
  line-height: 1.5;
}

.gc-blog-post-content .gc-blog-post-meta {
  margin-bottom: 5px;
}

.gc-blog-post-content .gc-post-title {
  margin-bottom: 10px;
}

.gc-blog-thumb.image-fit {
  position: relative;
  overflow: hidden;
  display: block;
  padding-top: 75%;
}

.gc-blog-thumb.image-fit img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  min-height: 100%;
  object-fit: cover;
}

.style-default .gc-blog-post-category {
  position: absolute;
  display: block;
  right: 15px;
  top: 15px;
}

.style-card .gc-blog-post-item-inner {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 450px;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  padding: 30px;
  overflow: hidden;
}

.style-card .gc-blog-post-item-inner.category-none {
  justify-content: flex-end;
}

.style-card .gc-blog-post-item-inner:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  //background: rgb(57 57 58 / 44%)
}

.style-card.thumb-none .gc-blog-post-item-inner {
  background-color: var(--gc-bg-soft);
}

.style-card.thumb-none .gc-blog-post-item-inner:before {
  content: none;
}

.style-card .gc-blog-post-content {
  padding: 0;
  background-color: transparent;
  z-index: 1;
}

.style-card .gc-blog-post-content,
.style-card .gc-blog-post-content .gc-blog-post-meta h6,
.style-card .gc-blog-post-content .gc-post-title {
  color: var(--gc-light);
  margin-right: 20px;
}

.style-card.thumb-none .gc-blog-post-content,
.style-card.thumb-none .gc-blog-post-content .gc-blog-post-meta h6,
.style-card.thumb-none .gc-blog-post-content .gc-post-title {
  color: var(--gc-dark);
}

.style-card .gc-blog-post-content .gc-post-title a:hover {
  color: var(--gc-light);
  opacity: 0.7;
}

.style-card .gc-blog-post-category {
  text-align: right;
  z-index: 1;
  margin-bottom: 30px;
}

.gc-blog-post-category span {
  padding: 7px 15px;
  display: inline-block;
  text-align: center;
  background: var(--gc-light);
  line-height: 1;
}

.gc-blog-post-meta.gc-inline-two-block {
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-start;
}

.gc-blog-post-meta .gc-post-author,
.gc-blog-post-meta .gc-post-date {
  text-transform: uppercase;
  font-size: 12px;
  margin-right: 20px;
  margin-bottom: 0;
  color: var(--gc-black-soft);
}

.gc-blog-post-meta.gc-inline-two-block .gc-post-meta-title.gc-block-left,
.gc-blog-post-meta.gc-inline-two-block .gc-post-meta-date.gc-block-right a,
.gc-blog-post-meta.gc-inline-two-block .gc-post-meta-date.gc-block-right {
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
  margin-right: 15px;
  font-weight: 500;
  color: var(--gc-black-soft);
}

.gc-blog-post-meta.gc-inline-two-block .gc-post-meta-date.gc-block-right a {
  font-size: 11px;
  margin-right: 10px;
}

.content-alignment-center .gc-blog-post-meta.gc-inline-two-block {
  justify-content: center;
  align-items: baseline;
}

.style-split .gc-blog-post-item-inner {
  display: inline-flex;
  flex-wrap: wrap;
  width: 100%;
}

.style-split .gc-blog-post-thumb-wrapper {
  flex: 0 0 40%;
  position: relative;
  overflow: hidden;
  min-height: 250px;
}

.style-split .gc-blog-post-thumb {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
}

.style-split.thumb-none .gc-blog-post-thumb {
  background-color: var(--gc-bg-soft);
}

.style-split .gc-blog-post-thumb img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  min-height: 100%;
  object-fit: cover;
}

.style-split a.blog-thumb-link {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.style-split a.gc-blog-post-category {
  position: absolute;
  top: 10px;
  left: 10px;
}

.style-split .gc-blog-post-content {
  padding: 15px 30px 0;
  background-color: var(--gc-light);
  flex: 0 0 60%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.style-split .gc-blog-post-meta.gc-inline-two-block {
  text-transform: uppercase;
  font-size: 12px;
  margin-bottom: 0;
  margin-top: 20px;
  padding: 10px 0;
  border-top: 1px solid var(--gc-gray-softer);
}

@media (max-width: 768px) {
  .style-split .gc-blog-post-item-inner {
    flex-direction: column;
  }

  .style-split .gc-blog-post-content {
    padding: 15px 0 0;
  }
}

@media (max-width: 576px) {
  .style-split .gc-blog-post-thumb-wrapper {
    min-height: auto;
  }

  .style-split .gc-blog-post-thumb {
    padding-bottom: 0;
    position: relative;
  }

  .style-split .gc-blog-post-thumb img {
    position: relative;
  }
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

@-webkit-keyframes fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translateY(2000px);
    transform: translateY(2000px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes fadeInUpBig {
  0% {
    opacity: 0;
    -webkit-transform: translateY(2000px);
    -ms-transform: translateY(2000px);
    transform: translateY(2000px);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}

.gc-text-marquee .elementor-heading-title {
  display: flex;
  align-items: baseline;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
}

.gc-text-marquee .elementor-heading-title span {
  -webkit-animation: gcMarquee 20s linear infinite;
  animation: gcMarquee 5s linear infinite;
  padding-left: 20px;
}

@-webkit-keyframes gcMarquee {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

@keyframes gcMarquee {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.nice-select {
  -webkit-tap-highlight-color: transparent;
  border: solid 1px var(--gc-gray);
  color: var(--gc-gray-dark);
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  font-family: inherit;
  font-weight: 400;
  height: 45px;
  line-height: 43px;
  outline: none;
  padding-left: 18px;
  padding-right: 30px;
  position: relative;
  text-align: left !important;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: auto;
}

.nice-select:after {
  border-bottom: 2px solid;
  border-right: 2px solid;
  border-color: var(--gc-black-soft);
  content: "";
  display: block;
  height: 5px;
  margin-top: -4px;
  pointer-events: none;
  position: absolute;
  right: 12px;
  top: 54%;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  width: 5px;
}

.nice-select.open:after {
  -webkit-transform: rotate(-135deg);
  -ms-transform: rotate(-135deg);
  transform: rotate(-135deg);
}

.nice-select.open .list {
  opacity: 1;
  border-radius: 4px;
  pointer-events: auto;
  -webkit-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: var(--gc-gray);
  color: var(--gc-black-soft);
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: var(--gc-gray);
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .list {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .list {
  left: auto;
  right: 0;
}

.nice-select.small {
  font-size: 12px;
  height: 36px;
  line-height: 34px;
}

.nice-select.small:after {
  height: 4px;
  width: 4px;
}

.nice-select.small .option {
  line-height: 34px;
  min-height: 34px;
}

.nice-select .list {
  background-color: var(--gc-light);
  border-radius: 0;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  box-sizing: border-box;
  margin-top: 4px;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  left: 0;
  -webkit-transform-origin: 50% 0;
  -ms-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -webkit-transform: scale(0.75) translateY(-21px);
  -ms-transform: scale(0.75) translateY(-21px);
  transform: scale(0.75) translateY(-21px);
  -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25),
    opacity 0.15s ease-out;
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 999;
  width: 100%;
  max-width: 650px;
  min-width: max-content;
  max-height: 200px;
  overflow: auto;
  padding: 0 0 10px;
  margin: 5px 0 0 0 !important;
}

.nice-select .option {
  cursor: pointer;
  font-weight: 400;
  line-height: 30px;
  list-style: none;
  min-height: 30px;
  font-size: 12px;
  outline: none;
  padding-left: 18px;
  padding-right: 15px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
  overflow: auto;
  margin: 0 !important;
}

.nice-select .option:hover,
.nice-select .option.focus {
  color: var(--gc-light);
  background-color: var(--gc-primary);
}

.nice-select .option.selected {
  color: var(--gc-light);
  background-color: var(--gc-dark);
}

.nice-select .option.selected {
  font-weight: 700;
  font-weight: 500;
}

.nice-select .option.disabled {
  background-color: transparent;
  color: var(--gc-black-soft);
  cursor: default;
}

.no-csspointerevents .nice-select .list {
  display: none;
}

.no-csspointerevents .nice-select.open .list {
  display: block;
}

.nice-select.custom-select span.current {
  height: 100%;
  margin: 0;
  display: flex;
  align-items: center;
}

.nice-select.open .list {
  border-radius: 0;
  margin: 0 !important;
}

.nice-select span.current {
  font-weight: 500;
  text-transform: uppercase;
  font-size: 11px;
}

@media (max-width: 576px) {
  .nice-select .list {
    left: auto;
    right: 0;
  }
}

.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  display: block;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.slick-list:focus {
  outline: none;
}

.slick-list.dragging {
  cursor: pointer;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.slick-track:before,
.slick-track:after {
  display: table;
  content: "";
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

[dir="rtl"] .slick-slide {
  float: right;
}

.slick-slide img {
  vertical-align: middle;
  display: inline-block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}

.slick-prev,
.slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  width: 20px;
  height: 20px;
  padding: 0;
  -webkit-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.slick-prev:hover,
.slick-prev:focus,
.slick-next:hover,
.slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
}

.slick-prev:hover:before,
.slick-prev:focus:before,
.slick-next:hover:before,
.slick-next:focus:before {
  opacity: 1;
}

.slick-prev.slick-disabled:before,
.slick-next.slick-disabled:before {
  opacity: 0.25;
}

.slick-prev:before,
.slick-next:before {
  font-size: 20px;
  line-height: 1;
  opacity: 0.75;
  color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slick-prev {
  left: -25px;
}

[dir="rtl"] .slick-prev {
  right: -25px;
  left: auto;
}

.slick-prev:before {
  content: "Ã¢â€ Â";
}

[dir="rtl"] .slick-prev:before {
  content: "Ã¢â€ â€™";
}

.slick-next {
  right: -25px;
}

[dir="rtl"] .slick-next {
  right: auto;
  left: -25px;
}

.slick-next:before {
  content: "Ã¢â€ â€™";
}

[dir="rtl"] .slick-next:before {
  content: "Ã¢â€ Â";
}

.slick-dots {
  display: block;
  padding: 0;
  margin: 0;
  list-style: none;
  line-height: 1;
  margin-top: 40px;
}

.slick-dots li {
  position: relative;
  display: inline-block;
  align-items: center;
  cursor: pointer;
  border-radius: 100%;
  padding: 3px;
  border: 1px solid var(--gc-border);
}

.slick-dots li.slick-active {
  border-color: var(--gc-dark);
}

.slick-dots li+li {
  margin-left: 10px;
}

[dir="rtl"] .slick-dots li+li {
  margin-left: 0;
  margin-right: 10px;
}

.slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  width: 5px;
  height: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: var(--gc-gray);
  padding: 0;
  border-radius: 100%;
}

.slick-dots li:hover button,
.slick-dots li:focus button,
.slick-dots li.slick-active button {
  background: var(--gc-dark);
}

.mfp-bg {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999998;
  overflow: hidden;
  position: fixed;
  background: #a3a3a3c7;
  opacity: 0.8;
}

.mfp-wrap {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  position: fixed;
  outline: none !important;
  -webkit-backface-visibility: hidden;
}

.mfp-container {
  text-align: center;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 0 8px;
  box-sizing: border-box;
}

.mfp-container:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.mfp-align-top .mfp-container:before {
  display: none;
}

.mfp-content {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 auto;
  text-align: left;
  z-index: 1045;
}

.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
  width: 100%;
  cursor: auto;
}

.mfp-ajax-cur {
  cursor: progress;
}

.mfp-zoom-out-cur,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  cursor: -moz-zoom-out;
  cursor: -webkit-zoom-out;
  cursor: zoom-out;
}

.mfp-zoom {
  cursor: pointer;
  cursor: -webkit-zoom-in;
  cursor: -moz-zoom-in;
  cursor: zoom-in;
}

.mfp-auto-cursor .mfp-content {
  cursor: auto;
}

.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.mfp-loading.mfp-figure {
  display: none;
}

.mfp-hide {
  display: none !important;
}

.mfp-preloader {
  color: #ccc;
  position: absolute;
  top: 50%;
  width: auto;
  text-align: center;
  margin-top: -0.8em;
  left: 8px;
  right: 8px;
  z-index: 1044;
}

.mfp-preloader a {
  color: #ccc;
}

.mfp-preloader a:hover {
  color: #fff;
}

.mfp-s-ready .mfp-preloader {
  display: none;
}

.mfp-s-error .mfp-content {
  display: none;
}

button.mfp-close,
button.mfp-arrow {
  overflow: visible;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
  display: block;
  outline: none;
  padding: 0;
  z-index: 1046;
  box-shadow: none;
  touch-action: manipulation;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

.mfp-close {
  width: 44px;
  height: 44px;
  line-height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  text-decoration: none;
  text-align: center;
  opacity: 0.65;
  padding: 0 0 18px 10px;
  color: #fff;
  font-style: normal;
  font-size: 28px;
  font-family: Arial, Baskerville, monospace;
}

.mfp-close:hover,
.mfp-close:focus {
  opacity: 1;
}

.mfp-close-btn-in .mfp-close {
  color: #333;
}

.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  color: #fff;
  right: -6px;
  text-align: right;
  padding-right: 6px;
  width: 100%;
}

.mfp-counter {
  position: absolute;
  top: 0;
  right: 0;
  color: #ccc;
  font-size: 12px;
  line-height: 18px;
  white-space: nowrap;
}

.mfp-arrow {
  position: absolute;
  opacity: 0.65;
  margin: 0;
  top: 50%;
  margin-top: -55px;
  padding: 0;
  width: 90px;
  height: 110px;
  -webkit-tap-highlight-color: transparent;
}

.mfp-arrow:active {
  margin-top: -54px;
}

.mfp-arrow:hover,
.mfp-arrow:focus {
  opacity: 1;
}

.mfp-arrow:before,
.mfp-arrow:after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  left: 0;
  top: 0;
  margin-top: 35px;
  margin-left: 35px;
  border: medium inset transparent;
}

.mfp-arrow:after {
  border-top-width: 13px;
  border-bottom-width: 13px;
  top: 8px;
}

.mfp-arrow:before {
  border-top-width: 21px;
  border-bottom-width: 21px;
  opacity: 0.7;
}

.mfp-arrow-left {
  left: 0;
}

.mfp-arrow-left:after {
  border-right: 17px solid #fff;
  margin-left: 31px;
}

.mfp-arrow-left:before {
  margin-left: 25px;
  border-right: 27px solid #3f3f3f;
}

.mfp-arrow-right {
  right: 0;
}

.mfp-arrow-right:after {
  border-left: 17px solid #fff;
  margin-left: 39px;
}

.mfp-arrow-right:before {
  border-left: 27px solid #3f3f3f;
}

.mfp-iframe-holder {
  padding-top: 40px;
  padding-bottom: 40px;
}

.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 900px;
}

.mfp-iframe-holder .mfp-close {
  top: -40px;
}

.mfp-iframe-scaler {
  width: 100%;
  height: 0;
  overflow: hidden;
  padding-top: 56.25%;
}

.mfp-iframe-scaler iframe {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: var(--gc-dark-zero);
}

img.mfp-img {
  width: auto;
  max-width: 100%;
  height: auto;
  display: block;
  line-height: 0;
  box-sizing: border-box;
  padding: 40px 0 40px;
  margin: 0 auto;
}

.mfp-figure {
  line-height: 0;
}

.mfp-figure:after {
  content: "";
  position: absolute;
  left: 0;
  top: 40px;
  bottom: 40px;
  display: block;
  right: 0;
  width: auto;
  height: auto;
  z-index: -1;
  //box-shadow: 0 0 8px rgb(165 165 165 / 18%);
  background: #444;
}

.mfp-figure small {
  color: #bdbdbd;
  display: block;
  font-size: 12px;
  line-height: 14px;
}

.mfp-figure figure {
  margin: 0;
}

.mfp-bottom-bar {
  margin-top: -36px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  cursor: auto;
}

.mfp-title {
  text-align: left;
  line-height: 18px;
  color: #f3f3f3;
  word-wrap: break-word;
  padding-right: 36px;
}

.mfp-image-holder .mfp-content {
  max-width: 100%;
}

.mfp-gallery .mfp-image-holder .mfp-figure {
  cursor: pointer;
}

@media screen and (max-width: 800px) and (orientation: landscape),
screen and (max-height: 300px) {
  .mfp-img-mobile .mfp-image-holder {
    padding-left: 0;
    padding-right: 0;
  }

  .mfp-img-mobile img.mfp-img {
    padding: 0;
  }

  .mfp-img-mobile .mfp-figure:after {
    top: 0;
    bottom: 0;
  }

  .mfp-img-mobile .mfp-figure small {
    display: inline;
    margin-left: 5px;
  }

  .mfp-img-mobile .mfp-bottom-bar {
    background: rgba(0, 0, 0, 0.6);
    bottom: 0;
    margin: 0;
    top: auto;
    padding: 3px 5px;
    position: fixed;
    box-sizing: border-box;
  }

  .mfp-img-mobile .mfp-bottom-bar:empty {
    padding: 0;
  }

  .mfp-img-mobile .mfp-counter {
    right: 5px;
    top: 3px;
  }

  .mfp-img-mobile .mfp-close {
    top: 0;
    right: 0;
    width: 35px;
    height: 35px;
    line-height: 35px;
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    text-align: center;
    padding: 0;
  }
}

@media all and (max-width: 900px) {
  .mfp-arrow {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
  }

  .mfp-arrow-left {
    -webkit-transform-origin: 0;
    transform-origin: 0;
  }

  .mfp-arrow-right {
    -webkit-transform-origin: 100%;
    transform-origin: 100%;
  }

  .mfp-container {
    padding-left: 6px;
    padding-right: 6px;
  }
}

@font-face {
  font-display: swap;
  font-family: "swiper-icons";
  src: url("data:application/font-woff;charset=utf-8;base64, 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");
  font-weight: 400;
  font-style: normal;
}

:root {
  --swiper-theme-color: #007aff;
}

.swiper,
.swiper-container {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  z-index: 1;
}

.swiper-vertical>.swiper-wrapper {
  flex-direction: column;
}

.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  box-sizing: content-box;
}

.swiper-android .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0, 0, 0);
}

.swiper-pointer-events {
  touch-action: pan-y;
}

.swiper-pointer-events.swiper-vertical {
  touch-action: pan-x;
}

.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
}

.swiper-slide-invisible-blank {
  visibility: hidden;
}

.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}

.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}

.swiper-3d,
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}

.swiper-3d .swiper-wrapper,
.swiper-3d .swiper-slide,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}

.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}

.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left,
      rgba(0, 0, 0, 0.5),
      rgba(0, 0, 0, 0));
}

.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right,
      rgba(0, 0, 0, 0.5),
      rgba(0, 0, 0, 0));
}

.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top,
      rgba(0, 0, 0, 0.5),
      rgba(0, 0, 0, 0));
}

.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom,
      rgba(0, 0, 0, 0.5),
      rgba(0, 0, 0, 0));
}

.swiper-css-mode>.swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar {
  display: none;
}

.swiper-css-mode>.swiper-wrapper>.swiper-slide {
  scroll-snap-align: start start;
}

.swiper-horizontal.swiper-css-mode>.swiper-wrapper {
  scroll-snap-type: x mandatory;
}

.swiper-vertical.swiper-css-mode>.swiper-wrapper {
  scroll-snap-type: y mandatory;
}

.swiper-centered>.swiper-wrapper::before {
  content: "";
  flex-shrink: 0;
  order: 9999;
}

.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before);
}

.swiper-centered.swiper-horizontal>.swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}

.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before);
}

.swiper-centered.swiper-vertical>.swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}

.swiper-centered>.swiper-wrapper>.swiper-slide {
  scroll-snap-align: center center;
}

.swiper-virtual.swiper-css-mode .swiper-wrapper::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}

.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after {
  height: 1px;
  width: var(--swiper-virtual-size);
}

.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after {
  width: 1px;
  height: var(--swiper-virtual-size);
}

.swiper-button-next,
.swiper-button-prev {
  position: absolute;
  top: auto;
  bottom: 4.7%;
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}

.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  text-transform: none;
  font-variant: initial;
  line-height: 1;
}

.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: "prev";
}

.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: "next";
}

.swiper-button-lock {
  display: none;
}

.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}

.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}

.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal>.swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 10px;
  left: 0;
  width: 100%;
}

.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}

.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width,
      var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height,
      var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: 50%;
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}

button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
  appearance: none;
}

.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}

.swiper-pagination-bullet:only-child {
  display: none !important;
}

.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}

.swiper-vertical>.swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: 10px;
  top: 50%;
  transform: translate3d(0, -50%, 0);
}

.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}

.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}

.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform, 200ms top;
}

.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}

.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms left;
}

.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform, 200ms right;
}

.swiper-pagination-progressbar {
  background: rgba(0, 0, 0, 0.25);
  position: absolute;
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}

.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}

.swiper-horizontal>.swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: 4px;
  left: 0;
  top: 0;
}

.swiper-vertical>.swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: 4px;
  height: 100%;
  left: 0;
  top: 0;
}

.swiper-pagination-lock {
  display: none;
}

.swiper-scrollbar {
  border-radius: 10px;
  position: relative;
  -ms-touch-action: none;
  background: rgba(0, 0, 0, 0.1);
}

.swiper-horizontal>.swiper-scrollbar {
  position: absolute;
  left: 1%;
  bottom: 3px;
  z-index: 50;
  height: 5px;
  width: 98%;
}

.swiper-vertical>.swiper-scrollbar {
  position: absolute;
  right: 3px;
  top: 1%;
  z-index: 50;
  width: 5px;
  height: 98%;
}

.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  left: 0;
  top: 0;
}

.swiper-scrollbar-cursor-drag {
  cursor: move;
}

.swiper-scrollbar-lock {
  display: none;
}

.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.swiper-zoom-container>img,
.swiper-zoom-container>svg,
.swiper-zoom-container>canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.swiper-slide-zoomed {
  cursor: move;
}

.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  animation: swiper-preloader-spin 1s infinite linear;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}

@keyframes swiper-preloader-spin {
  100% {
    transform: rotate(360deg);
  }
}

.swiper .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000;
}

.swiper-free-mode>.swiper-wrapper {
  transition-timing-function: ease-out;
  margin: 0 auto;
}

.swiper-grid>.swiper-wrapper {
  flex-wrap: wrap;
}

.swiper-grid-column>.swiper-wrapper {
  flex-wrap: wrap;
  flex-direction: column;
}

.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out;
}

.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}

.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}

.swiper-fade .swiper-slide-active,
.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

.swiper-cube {
  overflow: visible;
}

.swiper-cube .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}

.swiper-cube .swiper-slide .swiper-slide {
  pointer-events: none;
}

.swiper-cube.swiper-rtl .swiper-slide {
  transform-origin: 100% 0;
}

.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-next,
.swiper-cube .swiper-slide-prev,
.swiper-cube .swiper-slide-next+.swiper-slide {
  pointer-events: auto;
  visibility: visible;
}

.swiper-cube .swiper-slide-shadow-top,
.swiper-cube .swiper-slide-shadow-bottom,
.swiper-cube .swiper-slide-shadow-left,
.swiper-cube .swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.swiper-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  z-index: 0;
}

.swiper-cube .swiper-cube-shadow:before {
  content: "";
  background: var(--gc-dark-zero);
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  filter: blur(50px);
}

.swiper-flip {
  overflow: visible;
}

.swiper-flip .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
}

.swiper-flip .swiper-slide .swiper-slide {
  pointer-events: none;
}

.swiper-flip .swiper-slide-active,
.swiper-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

.swiper-flip .swiper-slide-shadow-top,
.swiper-flip .swiper-slide-shadow-bottom,
.swiper-flip .swiper-slide-shadow-left,
.swiper-flip .swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.swiper-creative .swiper-slide {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  overflow: hidden;
  transition-property: transform, opacity, height;
}

.swiper-cards {
  overflow: visible;
}

.swiper-cards .swiper-slide {
  transform-origin: center bottom;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  overflow: hidden;
}

.swiper-pagination {
  position: absolute;
  text-align: left;
}

.swiper-pagination-bullets {
  display: block;
  padding: 0;
  margin: 0;
  list-style: none;
  line-height: 1;
  margin-top: 40px;
}

.swiper-pagination-bullets .swiper-pagination-bullet {
  position: relative;
  display: inline-block;
  align-items: center;
  cursor: pointer;
  padding: 3px;
  background: var(--gc-border);
  width: 30px;
  height: 3px;
  border-radius: 0;
  margin: 0;
  opacity: 1;
}

.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active {
  border-color: var(--gc-dark);
}

span.swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--gc-pink);
}

.swiper-pagination-bullets .swiper-pagination-bullet:before {
  content: "";
  display: none;
  width: 10px;
  height: 5px;
  background: var(--gc-gray);
  border-radius: 100%;
}

.swiper-pagination-bullets .swiper-pagination-bullet:hover:before,
.swiper-pagination-bullets .swiper-pagination-bullet:focus:before,
.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
  background: var(--gc-purple);
}

.gc-swiper-theme-style .swiper-button-prev,
.gc-swiper-theme-style .swiper-button-next {
  background-image: none;
  background: rgba(255, 255, 255, 0.2);
  border: 0;
  //color: rgb(255, 255, 255);
  width: 30px;
  height: 30px;
  border-radius: 100%;
}

.gc-swiper-theme-style .swiper-button-prev:hover,
.gc-swiper-theme-style .swiper-button-next:hover {
  background-image: none;
  //background: rgb(255 255 255 / 37%);
  border: 0;
  //color: rgb(255, 255, 255);
  width: 30px;
  height: 30px;
  border-radius: 100%;
}

.gc-wc-tab-slider-edit-mode .gc-swiper-theme-style .swiper-button-prev,
.gc-wc-tab-slider-edit-mode .gc-swiper-theme-style .swiper-button-next,
.gc-wc-tab-slider .gc-swiper-theme-style .swiper-button-prev,
.gc-wc-tab-slider .gc-swiper-theme-style .swiper-button-next {
  background: none;
  border: 0;
}

.gc-products-widget-slider .gc-loop-slider .swiper-pagination-bullets,
.gc-wc-tab-slider-edit-mode .gc-swiper-theme-style .swiper-pagination-bullets,
.gc-wc-tab-slider .gc-swiper-theme-style .swiper-pagination-bullets {
  position: absolute;
  top: auto;
}

.gc-swiper-theme-style .swiper-button-prev:after,
.gc-swiper-theme-style .swiper-button-next:after {
  font-size: 15px;
  width: auto;
  height: auto;
}

.gc-swiper-theme-style .swiper-pagination-bullets {
  position: relative;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  text-align: center;
  margin-top: 30px;
}

.gc-swiper-theme-style.gc-swiper-onepage-style .swiper-pagination-bullets {
  position: absolute;
  top: auto;
  left: 0;
  bottom: 30px;
  right: 0;
  text-align: center;
  margin-top: 0;
}

@media (max-width: 480px) {

  .gc-swiper-theme-style .swiper-button-prev,
  .gc-swiper-theme-style .swiper-button-next {
    display: none;
  }
}

.thm-tab-slider.gc-swiper-slider {
  min-height: 300px;
}

.compare-area:not(.has-product) .gc-panel-content-items,
.wishlist-area:not(.has-product) .gc-panel-content-items {
  min-height: auto;
}

.panel-content-item .gc-empty-content {
  color: var(--gc-dark);
  display: flex;
  flex-direction: column;
  align-content: center;
  align-items: center;
}

.sliding-menu {
  position: relative;
  overflow: hidden;
  background-color: transparent;
  text-align: left;
  font-size: 18px;
}

.sliding-menu__panel {
  position: absolute;
  top: 0;
  left: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  left: 0;
}

.sliding-menu li {
  padding: 0;
  margin: 0;
  list-style: none;
}

.sliding-menu .sliding-menu-inner li a,
.sliding-menu li .sliding-menu__nav {
  display: block;
  position: relative;
  padding: 7px 0;
  text-align: left;
  font-size: 20px;
  line-height: 1;
  font-weight: 400;
  color: var(--gc-gray-dark);
  text-decoration: none;
  border-width: 0;
  width: 100%;
  cursor: pointer;
  background-color: transparent;
  -webkit-transition: color 250ms ease, background-color 250ms ease;
  -moz-transition: color 250ms ease, background-color 250ms ease;
  -o-transition: color 250ms ease, background-color 250ms ease;
  transition: color 250ms ease, background-color 250ms ease;
}

.sliding-menu li a:hover,
.sliding-menu li .sliding-menu__nav:hover {
  background: none;
}

.sliding-menu li.current-menu-parent>.sliding-menu__nav,
.sliding-menu li.current-menu-item>.sliding-menu__nav,
.sliding-menu li.current-menu-item>a,
.sliding-menu li a:hover,
.sliding-menu li.active a,
.sliding-menu li .sliding-menu__nav:hover {
  color: var(--gc-dark);
}

.sliding-menu .sliding-menu__nav:before {
  content: "\f105";
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 400;
  text-decoration: inherit;
  color: var(--gc-gray-dark);
  font-size: 14px;
  padding-right: 0.5em;
  position: absolute;
  top: 10px;
  right: -5px;
  font-weight: 900;
}

.sliding-menu li .sliding-menu__nav.sliding-menu__back {
  position: relative;
  margin-bottom: 10px;
  color: var(--gc-dark);
  font-weight: 500;
}

.sliding-menu .sliding-menu__back:after {
  content: "";
  border-bottom: 1px solid var(--gc-dark);
  height: 1px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  opacity: 0.1;
}

.sliding-menu .sliding-menu__back:before {
  content: "\f104";
  font-family: "Font Awesome 6 Free";
  font-style: normal;
  font-weight: 400;
  text-decoration: inherit;
  color: var(--gc-gray-dark);
  font-size: 14px;
  padding-right: 0.5em;
  position: absolute;
  top: 10px;
  right: -5px;
  font-weight: 900;
}

.sliding-menu .sliding-menu__icon {
  margin-right: 0.4em;
}

.sliding-menu .sliding-menu__separator {
  margin: 0.4em 1em;
  border-top: 2px solid #555;
}

li.item-shortcode-li,
li.sliding-menu-inner {
  max-height: 400px;
  overflow: hidden;
  overflow-y: auto;
}

body.admin-bar {
  padding-top: 32px;
}

.elementor-editor-active header.gc-header-default {
  z-index: 0;
}

.elementor-editor-active.scroll-start header.gc-header-default,
.elementor-editor-active header.gc-header-default:hover {
  z-index: 9;
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 1200px) {

  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1580px;
    padding-right: 15px;
    padding-left: 15px;
    width: 90%;
  }
}

.gc-row {
  display: flex;
  flex-direction: column;
}

#wrapper {
  display: flex;
  flex-direction: column;
  position: relative;
  min-height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}

.page-wrapper-inner {
  position: relative;
  -webkit-flex: 1 0 auto;
  -moz-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  height: auto;
}

.site-content {
  flex: 1;
}

.header-spacer {
  height: 80px;
  -moz-transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -o-transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -webkit-transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.gc-popup-open,
.gc-overlay-open {
  width: auto;
  overflow: hidden;
}

.gc-main-overlay {
  position: fixed;
  z-index: 2;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  background: var(--gc-bg-soft);
  opacity: 0;
  -moz-transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -o-transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -webkit-transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.gc-overlay-open .gc-main-overlay {
  width: 100%;
  height: 100%;
  opacity: 0.7;
}

.scroll-to-top {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background: var(--gc-dark);
  position: fixed;
  color: var(--gc-light);
  bottom: 60px;
  right: 40px;
  z-index: 99;
  font-size: 15px;
  text-align: center;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  display: none;
  border-radius: 4px;
  transition: all 0.4s ease;
}

.scroll-to-top:hover {
  background: var(--gc-primary);
  color: var(--gc-dark);
}

.gc-elementor-header {
  z-index: 3;
  position: relative;
}

.page-wrapper .elementor-edit-area {
  z-index: 3;
}

.submenu-open>.submenu,
.mega-open .submenu {
  opacity: 1 !important;
  visibility: visible !important;
  transform: scale(1);
}

.logo.logo-type-sitename {
  max-width: 450px;
}

.big-index {
  z-index: 99997;
}

.text-center {
  text-align: center;
}

.gc-popup-item {
  background: var(--gc-light);
  padding: 40px 0 50px;
  max-width: 1024px;
  margin: 0 auto;
}

.gc-popup-item .wpcf7-form p br {
  display: none;
}

.section-padding {
  padding: 40px 0;
}

.gap-extended.section-padding {
  padding: 25px 0;
}

.gap-wide.section-padding {
  padding: 20px 0;
}

.gap-wider.section-padding {
  padding: 0;
}

@media (min-width: 768px) {
  .section-padding {
    padding: 60px 0;
  }

  .gap-extended.section-padding {
    padding: 45px 0;
  }

  .gap-wide.section-padding {
    padding: 40px 0;
  }

  .gap-wider.section-padding {
    padding: 20px 0;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 80px 0;
  }

  .gap-extended.section-padding {
    padding: 65px 0;
  }

  .gap-wide.section-padding {
    padding: 60px 0;
  }

  .gap-wider.section-padding {
    padding: 40px 0;
  }
}

.nt-section.section-padding.pt-0 {
  padding-top: 0;
}

.nt-section.section-padding.pb-0 {
  padding-bottom: 0;
}

.gc-flex-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.gc-inline-two-block,
.gc-flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}

.gc-inline-two-block {
  -webkit-box-pack: space-between;
  -webkit-justify-content: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.gc-align-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}

.gc-justify-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.gc-block-left,
.gc-flex-left {
  display: flex;
  text-align: left;
  justify-content: flex-start;
}

.gc-block-right,
.gc-flex-right {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  text-align: right;
  -webkit-box-pack: end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.gc-block-full,
.gc-flex-full {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.gc-xl-block {
  display: block;
}

.gc-lg-block {
  display: block;
}

.gc-md-block {
  display: block;
}

.gc-sm-block {
  display: block;
}

.gc-xs-block {
  display: block;
}

@media (min-width: 480px) {
  .gc-xs-block {
    display: none;
  }
}

@media (min-width: 576px) {
  .gc-sm-block {
    display: none;
  }
}

@media (min-width: 768px) {
  .gc-md-block {
    display: none;
  }
}

@media (min-width: 992px) {
  .gc-lg-block {
    display: none;
  }
}

@media (min-width: 1200px) {
  .gc-xl-block {
    display: none;
  }
}

@media (max-width: 1200px) {
  .gc-xl-hidden {
    display: none;
  }
}

@media (max-width: 992px) {
  .gc-lg-hidden {
    display: none;
  }
}

@media (max-width: 768px) {
  .gc-md-hidden {
    display: none;
  }
}

@media (max-width: 576px) {
  .gc-sm-hidden {
    display: none;
  }
}

@media (max-width: 480px) {
  .gc-xs-hidden {
    display: none;
  }
}

.wpcf7-form-control-wrap {
  display: block;
}

.gc-cf7-form-wrapper form.wpcf7-form {
  display: flex;
  flex-wrap: nowrap;
}

.gc-cf7-form-wrapper form.wpcf7-form>* {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.gc-cf7-form-wrapper form.wpcf7-form .wpcf7-form-control-wrap input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]),
.gc-cf7-form-wrapper form.wpcf7-form .wpcf7-form-control-wrap>label,
.gc-cf7-form-wrapper form.wpcf7-form .wpcf7-form-control-wrap select,
.gc-cf7-form-wrapper form.wpcf7-form .wpcf7-form-control-wrap textarea {
  width: 100%;
  margin-bottom: 10px;
}

.gc-cf7-form-wrapper form.wpcf7-form input[type="submit"],
.gc-cf7-form-wrapper form.wpcf7-form button.wpcf-7-submit {
  display: inline-block;
  min-height: 50px;
}

.screen-reader-response ul {
  display: none;
}

.loading-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--gc-light);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.product-loading .loading-wrapper,
.mfp-ajax-cur .mfp-preloader .loading-wrapper,
.tab-loading .loading-wrapper,
.loading .loading-wrapper {
  display: flex;
}

.wpcf7-form.submitting .gc-btn:not(.loading) .loading-wrapper {
  display: flex;
  margin: 1px;
  height: 95%;
  width: 99%;
}

.wpcf7-form.submitting .ajax-loader::before,
.wpcf7-form.submitting .ajax-loader::after,
.wpcf7-form .ajax-loader::before,
.wpcf7-form .ajax-loader::after,
.mfp-ajax-cur .mfp-preloader .ajax-loading::before,
.mfp-ajax-cur .mfp-preloader .ajax-loading::after .tab-loading .ajax-loading::before,
.tab-loading .ajax-loading::after .loading .ajax-loading::before,
.loading .ajax-loading::after {
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}

.wpcf7-form.submitting .ajax-loader::before,
.wpcf7-form.submitting .ajax-loader::after,
.wpcf7-form .ajax-loader::before,
.wpcf7-form .ajax-loader::after,
.ajax-loading::before,
.ajax-loading::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.wpcf7-form .ajax-loader,
.ajax-loading {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: var(--gc-dark);
  color: var(--gc-dark);
}

.wpcf7-form .ajax-loader,
.product-loading .ajax-loading,
.mfp-ajax-cur .mfp-preloader .ajax-loading,
.tab-loading .ajax-loading,
.loading .ajax-loading {
  animation: dotFlashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}

.wpcf7-form .ajax-loader::before,
.wpcf7-form .ajax-loader::after,
.ajax-loading::before,
.ajax-loading::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.wpcf7-form .ajax-loader::before,
.wpcf7-form .ajax-loader::after,
.mfp-ajax-cur .mfp-preloader .ajax-loading::before,
.mfp-ajax-cur .mfp-preloader .ajax-loading::after .tab-loading .ajax-loading::before,
.tab-loading .ajax-loading::after .loading .ajax-loading::before,
.loading .ajax-loading::after {
  animation: dotFlashing 1s infinite alternate;
  animation-delay: 0s;
}

.ajax-loading::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: var(--gc-dark);
  color: var(--gc-dark);
}

.ajax-loading::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: var(--gc-dark);
  color: var(--gc-dark);
}

.has-default-header-type-dark .gc-header-mobile .ajax-loading::before,
.has-default-header-type-dark .gc-header-mobile .ajax-loading::after,
.has-default-header-type-dark .gc-side-panel .ajax-loading::before,
.has-default-header-type-dark .gc-side-panel .ajax-loading::after,
.has-default-header-type-dark .gc-popup-search-panel .ajax-loading::before,
.has-default-header-type-dark .gc-popup-search-panel .ajax-loading::after {
  background-color: var(--gc-light);
  color: var(--gc-light);
}

@keyframes dotFlashing {
  0% {
    background-color: var(--gc-dark);
  }

  50%,
  100% {
    background-color: var(--gc-gray);
  }
}

.gc-mfp-close,
.gc-filter-close,
.gc-panel-close-button,
.gc-panel-close.no-bar {
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  border: 9px solid transparent;
  color: var(--gc-dark);
  width: 36px;
  height: 36px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.gc-filter-close {
  background: var(--gc-gray-soft);
  width: 30px;
  height: 30px;
}

.gc-mfp-close {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 999999;
  opacity: 1;
}

.panel-header .gc-panel-close-button {
  background: var(--gc-gray-soft);
  margin-bottom: 0;
}

.gc-mfp-close:before,
.gc-mfp-close:after,
.gc-filter-close:before,
.gc-filter-close:after,
.gc-panel-close-button:before,
.gc-panel-close-button:after,
.gc-panel-close.no-bar:before,
.gc-panel-close.no-bar:after {
  content: "";
  position: absolute;
  height: 2px;
  width: 90%;
  top: 46%;
  left: 1px;
  transform-origin: 50% 50%;
  background-color: var(--gc-dark);
  opacity: 1;
  -moz-transition: -moz-transform ease 0.25s;
  -webkit-transition: -webkit-transform ease 0.25s;
  -o-transition: -o-transform ease 0.25s;
  -ms-transition: -ms-transform ease 0.25s;
  transition: transform ease 0.25s;
}

.gc-mfp-close:hover:before,
.gc-mfp-close:hover:after,
.gc-filter-close:hover:before,
.gc-filter-close:hover:after,
.gc-panel-close-button:hover:before,
.gc-panel-close-button:hover:after,
.gc-panel-close.no-bar:hover:before,
.gc-panel-close.no-bar:hover:after {
  background-color: var(--gc-dark);
}

.gc-mfp-close:before,
.gc-filter-close:before,
.gc-panel-close-button:before,
.gc-panel-close.no-bar:before {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.gc-mfp-close:after,
.gc-filter-close:after,
.gc-panel-close-button:after,
.gc-panel-close.no-bar:after {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.gc-mfp-close:hover:after,
.gc-filter-close:hover:after,
.gc-panel-close-button:hover:after,
.gc-panel-close.no-bar:hover:after {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.gc-mfp-close:hover:before,
.gc-filter-close:hover:before,
.gc-panel-close-button:hover:before,
.gc-panel-close.no-bar:hover:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.parallax-slider-two:not(.swiper-initialized) .elementor-section:not(:first-child) {
  display: none;
}

a.product-link.has-images.lazy-loading {
  display: block;
  position: relative;
}

.lazy-loading:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: var(--wpr-bg-46c93bd5-d053-423b-9bd3-39573ee12c4d);
  background-repeat: no-repeat;
  background-position: 50% 50%;
  background-color: var(--gc-light);
  z-index: 3;
}

.lazy-loading img {
  z-index: 1;
}

.swiper-slide .lazy-loading img {
  height: inherit;
}

.gc-svg-icon {
  max-width: 24px;
  max-height: 24px;
  cursor: pointer;
  fill: var(--gc-dark);

  &.mobile {
    max-width: 35px;
    max-height: 35px;
  }
}

.gc-svg-icon.mini-icon {
  max-width: 15px;
  max-height: 15px;
}

.gc-not-found-info-wrapper svg.shopBag,
.cart-empty-content svg.shopBag {
  max-width: 100px;
  max-height: 100px;
  fill: var(--gc-gray);
  opacity: 0.5;
}

.admin-bar .gc-side-panel {
  top: 32px;
  height: 100%;
}

.admin-bar .gc-header-mobile-top {
  top: 32px;
}

@media (max-width: 782px) {
  body.admin-bar {
    padding-top: 46px;
  }

  .admin-bar .gc-side-panel {
    top: 46px;
    height: 100%;
  }

  .admin-bar.scroll-start .gc-side-panel {
    top: 0;
  }

  .admin-bar .gc-header-mobile-top,
  .admin-bar .gc-popup-search-panel {
    top: 46px;
  }
}

@media (max-width: 600px) {

  .admin-bar.scroll-start .gc-header-mobile-top,
  .admin-bar .gc-popup-search-panel {
    top: 0;
  }
}

.gc-side-panel {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  width: 460px;
  max-width: 100%;
  z-index: 105;
  padding: 0;
  background: var(--gc-light);
  will-change: transform;
  -webkit-transition: opacity 0.25s ease, ease;
  transition: opacity 0.25s ease, transform 0.25s ease;
  opacity: 1;
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
  box-shadow: -1px 1px 5px 2px #3333331c;
}

.gc-overlay-open .gc-side-panel.active {
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  opacity: 1;
}

.panel-header-actions,
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.panel-header {
  min-height: 50px;
  padding: 15px;
  border-bottom: 1px solid var(--gc-border);
  background: var(--gc-gray-soft);
  display: flex;
  align-items: center;
}

.panel-header-btn {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50px;
}

.panel-header-btn.active {
  background: var(--gc-gray-soft);
}

.panel-header-btn:not(:last-child) {
  margin-right: 5px;
}

.panel-header-btn span.gc-cart-total {
  position: absolute;
  left: -234%;
  top: 3px;
  background: #fff;
  padding: 5px 15px;
  border-radius: 3px;
  border: 1px solid var(--gc-border);
  box-shadow: 0 0 7px 2px #00000014;
}

.panel-top-title {
  display: block;
  position: relative;
  padding: 0 0 10px 0;
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  text-transform: capitalize;
  color: var(--gc-dark);
  text-decoration: none;
  margin-bottom: 20px;
}

.panel-top-title strong {
  font-weight: 500;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  background: var(--gc-dark);
  color: var(--gc-light);
  padding: 2px 10px;
  border-radius: 4px;
}

.panel-top-title:after {
  content: "";
  border-bottom: 1px solid var(--gc-border);
  height: 1px;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}

.panel-content {
  position: relative;
  margin-top: 40px;
  height: 100%;
}

.panel-content .panel-content-item {
  position: absolute;
  width: 100%;
  top: 0;
  right: 0;
  padding: 0 25px;
  will-change: transform;
  -webkit-transition: opacity 0.25s ease, transform 0.5s ease;
  transition: opacity 0.25s ease, transform 0.5s ease;
  opacity: 0;
  -moz-transform: translateX(150%);
  -ms-transform: translateX(150%);
  -webkit-transform: translateX(150%);
  transform: translateX(150%);
}

.panel-content .panel-content-item.active {
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  opacity: 1;
}

.gc-header-mobile-content .gc-cart-goal-wrapper,
.gc-side-panel .gc-cart-goal-wrapper {
  margin-top: 20px;
  padding: 0 25px;
}

.woocommerce-mini-cart {
  position: relative;
}

.woocommerce-mini-cart .quantity {
  min-height: 29px;
}

.gc-wc-count {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background: var(--gc-purple);
  color: var(--gc-light);
  font-size: 8px;
  border-radius: 20px;
  position: absolute;
  left: 0;
  top: 0;
}

span.gc-cart-total {
  font-size: 12px;
  color: var(--gc-dark);
  font-weight: 500;
}

.gc-panel-content-items {
  position: relative;
}

.gc-content-item {
  width: 100%;
  margin-bottom: 15px;
  position: relative;
}

.gc-content-item-inner {
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: relative;
}

.gc-content-item .gc-content-link {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  color: var(--gc-dark);
}

.gc-link img {
  max-width: 80px;
  max-height: 80px;
}

.gc-content-item .gc-content-info {
  display: flex;
  flex-direction: column;
  margin: 5px 20px 0 15px;
  flex: 1;
  justify-content: space-between;
}

.gc-content-item .gc-content-info .gc-btn-small {
  color: #252525;
}

.gc-content-item .gc-content-info .added_to_cart {
  display: none;
}

.gc-content-item .gc-content-del-icon {
  width: 22px;
  height: 22px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  cursor: pointer;
}

.gc-content-item .gc-content-del-icon:hover svg {
  fill: var(--gc-dark);
}

.gc-content-info {
  .gc-price {
    font-weight: 400;
    color: var(--gc-black-soft);
  }
}

.gc-price {
  .price-amount {
    &.original-price {
      color: var(--gc-pink);
      text-decoration: line-through;
    }
  }
}

.gc-content-info .gc-price ins {
  text-decoration: none;
}

.gc-content-info .product-name {
  margin-bottom: 0;
}

.gc-content-info .gc-btn {
  margin-top: 10px;
}

.gc-panel-content-notice .product-name {
  color: var(--gc-primary);
}

.gc-empty-content svg {
  max-width: 50px;
  max-height: 50px;
  float: left;
  margin-right: 20px;
  fill: var(--gc-light);
  opacity: 1;
  margin-bottom: 10px;
  padding: 10px;
  background: var(--gc-purple-dark);
  border-radius: 30px;
}

.compare-area.has-product .gc-empty-content {
  display: none;
}

.gc-coupon-code-wrapper input#coupon_code {
  margin-right: 10px;
}

.gc-woocommerce-form-coupon-wrapper .woocommerce-error {
  flex: 0 0 100%;
  list-style: none;
}

.gc-lost-reset-password-wrapper .gc-flex {
  flex-direction: column;
}

.gc-checkout-footer-item.order-total {
  padding-top: 10px;
  margin-top: 10px;
  border-top: 1px solid var(--gc-border);
}

.gc-checkout-footer-item.order-total {
  text-transform: uppercase;
}

.gc-register-message.gc-error,
.gc-register-message.gc-success,
.gc-login-message.gc-success,
.gc-login-message.gc-error {
  margin-top: 15px;
}

.gc-register-message.gc-success strong,
.gc-register-message.gc-error strong,
.gc-login-message.gc-success strong,
.gc-login-message.gc-error strong {
  color: var(--gc-light);
}

.gc-login-message.gc-error,
.gc-register-message.gc-error {
  background: var(--gc-purple);
  color: var(--gc-light);
  padding: 10px;
}

.gc-login-message.gc-success,
.gc-register-message.gc-success {
  background: var(--gc-success);
  color: var(--gc-light);
  padding: 10px;
}

.woocommerce-invalid-required-field .woocommerce-input-wrapper select,
.woocommerce-invalid-required-field .woocommerce-input-wrapper input,
.woocommerce-invalid-required-field select,
.woocommerce-invalid-required-field input {
  border-color: var(--gc-purple);
}

.gc-panel-checkout-content {
  position: relative;
}

.checkout-area ul.woocommerce-error {
  width: 100%;
  max-height: 146px;
  overflow-y: auto;
  margin: 0;
}

.gc-popup-search-panel form.sidebar-search-form {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 100%;
  position: relative;
}

.gc-popup-search-panel input.sidebar_search_input {
  width: 100%;
  border: 0;
  border-radius: 0;
  outline: none;
  font-size: 36px;
  text-align: center;
}

.gc-popup-search-panel .sidebar_search_button {
  border: 0;
  background: transparent;
  font-size: 30px;
  color: var(--gc-gray);
  position: absolute;
  right: 20px;
  opacity: 0;
  cursor: pointer;
}

.gc-popup-search-panel input.sidebar_search_input:focus+.sidebar_search_button {
  opacity: 1;
}

.gc-popup-search-panel .sidebar_search_button:hover {
  color: var(--gc-dark);
}

.gc-popup-search-panel {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 106;
  width: 100%;
  background: var(--gc-light);
  padding: 50px 0;
  will-change: transform;
  -webkit-transition: opacity 0.25s ease, transform 0.5s ease;
  transition: opacity 0.25s ease, transform 0.5s ease;
  opacity: 0;
  -moz-transform: translateY(-100%);
  -ms-transform: translateY(-100%);
  -webkit-transform: translateY(-100%);
  transform: translateY(-100%);
}

.has-default-header-type-dark .gc-popup-search-panel {
  background: var(--gc-dark);
}

.gc-popup-search-panel.active {
  -moz-transform: translateY(0%);
  -ms-transform: translateY(0%);
  -webkit-transform: translateY(0%);
  transform: translateY(0%);
  opacity: 1;
}

.gc-search-panel-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.gc-popup-search-panel .gc-panel-close {
  background: var(--gc-bg-soft);
}

.gc-popup-search-panel .gc-svg-icon {
  fill: currentColor;
}

.gc-panel-content {
  width: 100%;
  max-width: 600px;
}

.gc-popup-search-panel .autocomplete-suggestion {
  width: 50%;
}

.gc-popup-search-panel .autocomplete-suggestions {
  padding: 0;
  margin-top: 15px;
  max-width: 100% !important;
  position: relative !important;
  display: flex;
  flex-wrap: wrap;
  border: 0 !important;
  width: 100%;
  max-height: 300px !important;
}

.has-default-header-type-dark .gc-popup-search-panel .autocomplete-suggestions {
  background: transparent;
}

.gc-popup-search-panel .no-result+.autocomplete-suggestions {
  background: transparent;
}

.gc-popup-search-panel .no-result+.autocomplete-suggestions .autocomplete-suggestion {
  width: 100%;
  margin: 0;
  padding: 5px 20px;
  border: 2px solid var(--gc-border);
  margin-left: 20px;
  justify-content: center;
}

.gc-popup-search-panel .gc-asform input.gc-as,
.gc-popup-search-panel .gc-asform input.gc-as:hover,
.gc-popup-search-panel .gc-asform input.gc-as:focus {
  font-size: 36px;
  text-align: center;
}

.gc-popup-search-panel input::-webkit-input-placeholder {
  color: var(--gc-gray);
  font-size: 36px;
}

.gc-popup-search-panel .gc-small-title,
.search-area-top .autocomplete-suggestion .gc-small-title {
  display: flex;
  flex-direction: column;
}

.has-default-header-type-dark .gc-popup-search-panel .gc-small-title,
.has-default-header-type-dark .search-area-top .autocomplete-suggestion .gc-small-title,
.has-default-header-type-dark .gc-asform.no-result+.autocomplete-suggestion strong,
.has-default-header-type-dark .gc-popup-search-panel input:focus {
  color: var(--gc-light);
}

.gc-popup-search-panel input:-moz-placeholder {
  color: var(--gc-gray);
  font-size: 36px;
  opacity: 1;
}

.gc-popup-search-panel input:focus:-moz-placeholder {
  opacity: 0;
}

.gc-popup-search-panel input::-moz-placeholder {
  color: var(--gc-gray);
  font-size: 36px;
  opacity: 1;
}

.gc-popup-search-panel input:focus::-moz-placeholder {
  opacity: 0;
}

.gc-popup-search-panel input:-ms-input-placeholder {
  color: var(--gc-gray);
  font-size: 36px;
}

.gc-popup-search-panel input:focus:-ms-input-placeholder {
  opacity: 0;
}

.gc-popup-search-panel input::-ms-input-placeholder {
  color: var(--gc-gray);
  font-size: 36px;
}

.gc-popup-search-panel input:focus::-ms-input-placeholder {
  opacity: 0;
}

.gc-popup-search-panel input::placeholder {
  color: var(--gc-gray);
  font-size: 36px;
}

.gc-popup-search-panel input:focus::placeholder {
  opacity: 0;
}

.gc-popup-search-panel .popup-search-style input:-moz-placeholder {
  font-size: 24px;
}

.gc-popup-search-panel .popup-search-style input::-moz-placeholder {
  font-size: 24px;
}

.gc-popup-search-panel .popup-search-style input:-ms-input-placeholder {
  font-size: 24px;
}

.gc-popup-search-panel .popup-search-style input::-ms-input-placeholder {
  font-size: 24px;
}

.gc-popup-search-panel .popup-search-style input::placeholder {
  font-size: 24px;
}

@media (max-width: 576px) {
  .gc-popup-search-panel {
    padding: 50px 20px;
  }

  .gc-popup-search-panel .popup-search-style input:-moz-placeholder {
    font-size: 14px;
  }

  .gc-popup-search-panel .popup-search-style input::-moz-placeholder {
    font-size: 14px;
  }

  .gc-popup-search-panel .popup-search-style input:-ms-input-placeholder {
    font-size: 14px;
  }

  .gc-popup-search-panel .popup-search-style input::-ms-input-placeholder {
    font-size: 14px;
  }

  .gc-popup-search-panel .popup-search-style input::placeholder {
    font-size: 14px;
  }

  .gc-ajax-product-search+.gc-product-categories.category-area {
    margin-top: 10px;
  }
}

.gc-label {
  color: var(--gc-light);
  //background-color: var(--gc-pink);
  background-color: var(--gc-pink50);
  height: 20px;
  padding: 0 5px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gc-label.gc-in-stock {
  background-color: var(--gc-base);
}

.gc-label.gc-out-stock {
  color: var(--gc-dark);
  background-color: var(--gc-gray);
}

.newsletter-popup-visible #gc-newsletter-popup {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0;
  max-width: 650px;
  position: relative;
  border-radius: 5px;
}

.gc-newsletter-bottom {
  width: 100%;
  text-align: right;
  margin: 0;
  position: absolute;
  text-align: 0;
  right: 0;
  left: auto;
  top: -25px;
  font-size: 12px;
  max-width: 31%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gc-newsletter-bottom label {
  background: var(--gc-light);
  padding: 3px 15px 3px;
  border-radius: 4px 4px 0 0;
}

.gc-footer-newsletter-form-2 {
  position: relative;
}

.gc-footer-newsletter-form-2 .gc-footer-newsletter-form-2-button input {
  position: absolute;
  right: 0;
  top: 0;
  width: 100px;
  border: 1px solid var(--gc-dark);
}

.gc-footer-newsletter-form-2 .gc-footer-newsletter-form-2-button input {
  background: var(--gc-dark);
  color: var(--gc-light);
}

.gc-footer-newsletter-form-2 .gc-footer-newsletter-form-2-button .wpcf7-spinner {
  position: absolute;
  top: 11px;
  right: 20px;
}

.nt-cf7-form-wrapper .wpcf7-response-output {
  display: none !important;
}

.gc-footer-newsletter-form-2 .gc-footer-newsletter-form-2-input input {
  border-color: var(--gc-dark);
}

.gc-footer-newsletter-form-1 .gc-footer-newsletter-form-1-input input {
  width: 100%;
  text-align: center;
  margin-bottom: 10px;
}

.wpcf7 form.sent .wpcf7-response-output {
  color: var(--gc-success);
}

@media (max-width: 768px) {

  .gc-sm-flex-left,
  .flex-sm-left-items {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    text-align: left;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .gc-sm-flex-column {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    flex: 0 0 100%;
    width: 100%;
    align-items: flex-start;
  }

  .gc-toggle-hidden-sidebar span,
  .gc-open-fixed-sidebar span {
    margin-right: 10px;
    cursor: pointer;
  }

  .gc-before-loop .gc-svg-icon,
  .gc-toggle-hidden-sidebar svg,
  .gc-open-fixed-sidebar svg,
  .gc-filter-action svg {
    max-width: 16px;
    max-height: 16px;
  }

  .gc-toggle-hidden-sidebar span,
  .gc-open-fixed-sidebar span,
  .gc-before-loop p.woocommerce-result-count,
  .gc-filter-action {
    font-size: 13px;
  }
}

@media (max-width: 576px) {
  .gc-popup-search-panel .autocomplete-suggestion {
    width: 100%;
  }

  .wpcf7 form.invalid .wpcf7-response-output {
    border-color: var(--gc-purple);
    margin: 14px;
    width: 92% !important;
    border-radius: 0;
  }

  p.gc-newsletter-bottom {
    max-width: 100%;
  }
}

.minicart-extra-text {
  margin: 0 0 30px;
}

.admin-bar .gc-header-mobile {
  top: 32px;
  height: calc(100vh - 32px);
}

@media (max-width: 782px) {
  .admin-bar .gc-header-mobile {
    top: 46px;
    height: calc(100vh - 46px);
  }
}

.gc-header-overlay {
  position: fixed;
  z-index: 103;
  top: 0;
  cursor: pointer;
  cursor: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 512 512'%3e%3cg transform='rotate(45 256 256)'%3e%3crect id='r' x='16' y='216' width='480' height='80' rx='14'/%3e%3cuse href='%23r' transform='rotate(90 256 256)'/%3e%3c/g%3e%3c/svg%3e") 16 16,
    pointer;
  right: 0;
  width: 0;
  height: 0;
  background: var(--gc-bg-soft);
  opacity: 0;
  -moz-transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -o-transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  -webkit-transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: opacity 0.45s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.has-default-header-type-dark .gc-header-overlay {
  background: var(--gc-dark);
}

.gc-overlay-open .gc-header-overlay {
  width: 100%;
  height: 100%;
  opacity: 0.7;
}

.gc-header-mobile {
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100%;
  width: 100%;
  max-width: 530px;
  z-index: 104;
  padding: 0;
  background: var(--gc-light);
  left: 0;
  opacity: 0;
  overflow: hidden;
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  will-change: transform;
  -webkit-transition: opacity 0.25s ease, ease;
  transition: opacity 0.25s ease, transform 0.25s ease;
  box-shadow: 3px 0 5px 2px #3333331c;
}

.gc-header-mobile.no-bar {
  max-width: 450px;
}

body:not(.admin-bar) .gc-header-mobile {
  top: 0;
}

.has-sticky-header.scroll-start .gc-header-mobile {
  position: fixed;
}

.gc-header-mobile.active {
  opacity: 1;
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.gc-header-mobile.has-bar {
  display: flex;
  flex-direction: row;
}

.gc-header-mobile.has-bar .gc-panel-close.no-bar {
  display: none;
}

.gc-panel-close.no-bar {
  margin: 10px 0 0 30px;
  background: var(--gc-bg-soft);
}

.gc-header-mobile-sidebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  padding: 15px 0;
  background: var(--gc-bg-soft);
  z-index: 4;
  min-width: 80px;
}

.gc-header-mobile-sidebar-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  align-items: center;
  text-align: center;
}

.gc-header-mobile-sidebar-logo img.main-logo {
  transform: rotate(-90deg);
  position: absolute;
  left: 0;
}

.gc-header-mobile-sidebar-logo .nav-logo.logo-type-sitename {
  transform: rotate(-180deg);
  writing-mode: vertical-lr;
}

.gc-header-mobile-sidebar-logo .header-text-logo {
  font-size: 24px;
  line-height: 0;
}

.gc-header-mobile-sidebar-top {
  text-align: center;
}

.gc-panel-close {
  position: relative;
  margin-bottom: 20px;
}

.sidebar-top-action {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.sidebar-top-action .top-action-btn {
  margin-top: 10px;
  border-radius: 30px;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.sidebar-top-action .top-action-btn:first-child {
  margin-top: 0;
}

.top-action-btn .gc-wc-count {
  left: -5px;
  top: 0;
}

.sidebar-top-action .top-action-btn.active {
  background: var(--gc-light);
}

.gc-header-mobile-sidebar a {
  color: var(--gc-dark);
}

.gc-header-mobile-content {
  position: relative;
  flex: 1;
}

.gc-header-mobile.no-bar .gc-header-mobile-content {
  padding: 30px;
}

.sidebar-top-action .share {
  display: none;
}

.gc-header-mobile-content .gc-header-slide-menu {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
  left: 0;
  padding: 35px 50px;
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  opacity: 1;
  will-change: transform;
  -webkit-transition: opacity 0.75s ease, transform 0.75s ease;
  transition: opacity 0.75s ease, transform 0.75s ease;
}

.gc-header-mobile.no-bar .gc-header-mobile-content .gc-header-slide-menu {
  padding: 35px 30px;
}

.gc-header-mobile-content .gc-header-slide-menu:not(.active) {
  -moz-transform: translateX(-150%);
  -ms-transform: translateX(-150%);
  -webkit-transform: translateX(-150%);
  transform: translateX(-150%);
  opacity: 0;
}

.gc-sidemenu-copyright {
  font-size: 14px;
  position: absolute;
  bottom: 50px;
  left: 50px;
  max-width: 260px;
}

.gc-sidemenu-lang-switcher {
  display: flex;
  width: 100%;
  align-items: center;
  margin-top: 50px;
  padding-top: 10px;
  border-top: 1px solid var(--gc-border);
}

.gc-sidemenu-lang-switcher .lang-blobe-icon {
  margin-right: 10px;
  margin-top: 10px;
}

.gc-sidemenu-lang-switcher .sliding-menu {
  width: 100%;
}

.search-area-top:not(.active) {
  opacity: 0;
}

.search-area-top {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  border-bottom: 0;
  margin-bottom: 0;
  position: relative;
}

.gc-header-mobile-content .search-area-top input {
  width: 100%;
  border: none;
  border-radius: 0;
  background: transparent;
  padding: 12px 30px;
}

.search-area-top svg {
  fill: var(--gc-gray);
  position: absolute;
}

.search-icon.loading {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  right: 15px;
}

.search-icon:not(.loading) .ajax-loading {
  display: none;
}

.sidebar-bottom-socials {
  display: flex;
  flex-direction: column;
  text-align: center;
}

.sidebar-bottom-socials a {
  font-size: 18px;
  margin-top: 15px;
  line-height: 1;
}

.action-content {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
  left: 0;
  padding: 35px 50px;
  background: var(--gc-light);
  max-height: 100%;
  z-index: 3;
  overflow: hidden;
  opacity: 0;
  -moz-transform: translateX(-150%);
  -ms-transform: translateX(-150%);
  -webkit-transform: translateX(-150%);
  transform: translateX(-150%);
  will-change: transform;
  -webkit-transition: opacity 0.15s ease, transform 0.75s ease;
  transition: opacity 0.15s ease, transform 0.75s ease;
}

.action-content.active {
  opacity: 1;
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  overflow-y: auto;
}

.action-content.checkout-area {
  max-height: 100vh;
}

.cart-area .mini-cart-item {
  display: flex;
  width: 100%;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 25px;
  position: relative;
}

.cart-area.cart-item-loading .gc-mini-cart-item-loading {
  position: relative;
  min-height: 80px;
}

.cart-area:not(.cart-item-loading) .gc-mini-cart-item-loading {
  display: none;
}

.gc-perfect-scrollbar .mini-cart-item:last-child {
  margin-bottom: 0;
}

.cart-item-details {
  display: flex;
  flex: 1;
}

.cart-area .product-link>span {
  display: block;
}

.cart-area .cart-item-title {
  display: flex;
  flex-direction: column;
  margin-left: 15px;
  flex: 1;
}

.cart-area .cart-quantity-wrapper {
  margin-top: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.cart-area .quantity {
  height: 22px;
  max-width: 100px;
}

.cart-area .quantity input.qty {
  max-width: calc(100% - 44px);
}

.cart-area .quantity-button.plus,
.cart-area .quantity-button.minus {
  color: var(--gc-dark);
  background: var(--gc-light);
  font-weight: 400;
}

.cart-area .quantity-button.plus:hover,
.cart-area .quantity-button.minus:hover {
  background: var(--gc-gray);
}

.cart-area .quantity {
  background: var(--gc-light);
}

.cart-area .gc-price,
.cart-item-title .gc-price .cart-quantity {
  color: var(--gc-black-soft);
}

.cart-item-title .cart-name,
.gc-content-info .product-name {
  color: var(--gc-dark);
  font-size: 15px;
  font-weight: 400;
}

.cart-item-title .gc-price span,
.gc-content-info .gc-price {
  font-size: 14px;
  font-weight: 400;
}

.cart-item-title .gc-price {
  font-size: 14px;
  font-weight: 400;
}

.cart-item-title .gc-price .cart-quantity {
  font-size: 12px;
  font-weight: 500;
  color: var(--gc-dark);
}

.cart-area .del-icon {
  width: 22px;
  height: 22px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 30px;
}

.cart-area .del-icon .variation {
  display: none;
}

.cart-area .del-icon a svg,
.wishlist-area .gc-svg-icon.mini-icon,
.compare-area .gc-svg-icon.mini-icon {
  fill: var(--gc-gray-dark);
  color: var(--gc-gray-dark);
}

.cart-area .del-icon a {
  color: var(--gc-dark);
  display: inline-flex;
}

.cart-area .del-icon:hover svg {
  fill: var(--gc-dark);
}

.cart-area .del-icon:hover a {
  color: var(--gc-light);
}

.cart-area .cart-total {
  margin-bottom: 22px;
}

.cart-area .cart-total-price {
  border-top: 1px solid var(--gc-border);
  overflow: hidden;
  padding-top: 15px;
  margin-top: 15px;
  text-transform: uppercase;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--gc-dark);
}

.cart-empty-content .minicart-title {
  display: none;
}

.gc-cart-empty-wrapper {
  text-align: center;
}

.gc-cart-empty-wrapper .gc-btn-medium {
  max-width: 170px;
  margin: 0 auto;
}

.gc-cart-empty-icon,
.cart-area svg.shopBag {
  max-width: 100px;
  max-height: 100px;
  fill: var(--gc-gray);
  opacity: 0.5;
  margin-left: -14px;
  margin-bottom: 10px;
}

.cart-bottom-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.cart-bottom-btn .gc-btn {
  flex: auto;
}

.quantity.hidden {
  display: none;
  opacity: 0;
  visibility: hidden;
}

.contact-area form>label {
  margin-bottom: 10px;
  width: 100%;
}

.contact-area.action-content textarea {
  height: 120px;
  max-height: 120px;
}

.contact-area.action-content form>.wpcf7-form-control-wrap {
  position: relative;
  display: inline-block;
  width: 100%;
}

.contact-area.action-content input,
.contact-area.action-content select,
.contact-area.action-content label,
.contact-area.action-content textarea {
  width: 100%;
  margin-bottom: 10px;
}

.contact-area.action-content label {
  margin-bottom: 5px;
}

.contact-area .wpcf7 form .wpcf7-response-output {
  margin: 0;
  margin-top: 20px;
  padding: 10px 15px;
}

.contact-area .wpcf7-form .ajax-loader,
.contact-area .wpcf7-form:not(.submitting) .loading-wrapper {
  display: none;
}

.contact-area .loading-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: absolute;
  background: var(--gc-light);
}

.header-left-side-menu-form-container-title h6 {
  margin: 25px 0 0;
}

.header-left-side-menu-form-container-title {
  margin: 30px 0 20px;
}

.header-left-side-menu-form-container-title .gc-meta-title {
  text-transform: uppercase;
  font-weight: 500;
  display: block;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: var(--gc-dark);
  margin-top: 25px;
}

.header-left-side-menu-form-container-title address {
  margin: 0;
}

.category-area .product-category a {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.category-area .category-title {
  font-size: 12px;
  margin-top: 10px;
  color: var(--gc-dark);
  text-transform: capitalize;
}

.category-area .cat-count {
  position: absolute;
  top: 8px;
  left: 18px;
  width: 18px;
  height: 18px;
  color: var(--gc-light);
  font-size: 11px;
  background: var(--gc-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
}

.category-area .img-none .product-category {
  padding: 0 10px;
}

.category-area .img-none .product-category a {
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;
}

.category-area .img-none .cat-count {
  position: relative;
  top: auto;
  left: 0;
  margin-right: 10px;
}

.category-area .img-none .category-title {
  margin-top: 0;
}

.account-area ul.navigation {
  display: inline-block;
}

.account-area li.menu-item {
  position: relative;
  display: block;
  width: auto;
  position: relative;
}

.account-area li.menu-item:hover a,
.account-area li.menu-item.is-active a {
  color: var(--gc-dark);
}

.account-area li.menu-item a {
  display: inline-block;
  padding: 10px 0;
  text-align: left;
  position: relative;
  font-size: 14px;
  line-height: 1em;
  color: var(--gc-gray-dark);
  text-decoration: none;
  border-width: 0;
  background-color: transparent;
  -webkit-transition: color 250ms ease, background-color 250ms ease;
  -moz-transition: color 250ms ease, background-color 250ms ease;
  -o-transition: color 250ms ease, background-color 250ms ease;
  transition: color 250ms ease, background-color 250ms ease;
}

.account-area li.menu-item a:after {
  content: "";
  height: 2px;
  width: 0;
  background-color: currentColor;
  position: absolute;
  bottom: 0;
  left: 0;
  -webkit-transition: width 0.25s;
  transition: width 0.25s;
}

.account-area li.menu-item a:hover:after {
  width: 100%;
}

.account-area .panel-top-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.account-area:not(.account-logged-in) .top-title {
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.account-area .form-row {
  margin-right: 0;
  margin-left: 0;
  flex-direction: column;
  display: flex;
}

.account-area .button {
  margin-top: 10px;
}

.account-area label {
  display: flex;
  align-items: center;
}

.account-area .account-area-form-wrapper {
  position: relative;
}

.account-area .login-form-content,
.account-area .register-form-content {
  position: absolute;
  z-index: 3;
  opacity: 0;
  width: 100%;
  height: 100%;
  background: var(--gc-light);
  padding: 0 0;
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  will-change: transform;
  -webkit-transition: opacity 0.15s ease, transform 0.75s ease;
  transition: opacity 0.15s ease, transform 0.75s ease;
}

.account-area .login-form-content.active,
.account-area .register-form-content.active {
  opacity: 1;
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.account-area .form-action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.form-action-btn svg {
  max-width: 16px;
  max-height: 16px;
  fill: var(--gc-primary);
}

.account-area-social-form-wrapper div.nsl-container.nsl-container-block .nsl-container-buttons {
  display: flex;
  justify-content: space-between;
  align-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.gc-header-mobile-top {
  display: none;
}

@media only screen and (min-width: 1280px) {
  .header-text-logo-special {
    margin-top: 15px;
    display: block;
  }

  .header-text-logo-mini {
    display: block;
    line-height: 1;
    font-size: 10px;
    text-align: center;
  }
}

@media only screen and (max-width: 1280px) {
  .gc-header-mobile-top {
    position: fixed;
    top: 0;
    min-height: 80px;
    padding: 0 15px;
    width: 100%;
    z-index: 100;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    justify-content: space-between;
    background: var(--gc-light);
    -moz-transition: height 0.15s linear, box-shadow 0.15s linear,
      background-color 0.15s cubic-bezier(0.2, 0.06, 0.05, 0.95);
    -o-transition: height 0.15s linear, box-shadow 0.15s linear,
      background-color 0.15s cubic-bezier(0.2, 0.06, 0.05, 0.95);
    -webkit-transition: height 0.15s linear, box-shadow 0.15s linear,
      background-color 0.15s cubic-bezier(0.2, 0.06, 0.05, 0.95);
    transition: height 0.15s linear, box-shadow 0.18s linear,
      background-color 0.15s cubic-bezier(0.2, 0.06, 0.05, 0.95);
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  .has-sticky-header.scroll-start .gc-header-mobile-top {
    position: fixed;
    -webkit-box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
    box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
    transition-delay: 0.2s;
  }

  .gc-header-mobile-top .mobile-toggle {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
  }

  .gc-header-mobile-top .mobile-toggle {
    color: var(--gc-dark);
  }

  .gc-header-mobile-top-actions {
    display: flex;
    align-items: center;
  }

  .gc-header-mobile-top .top-action-btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    margin-right: 10px;

    .cart-with-counter {
      position: relative;
      margin-right: 8px;

      .gc-cart-count {
        position: absolute;
        top: -2px;
        right: -5px;
      }
    }
  }

  .gc-header-mobile-top .top-action-btn:last-child {
    margin-right: 0;
  }

  .gc-header-mobile-top .top-action-btn .gc-wc-count {
    left: auto;
    top: auto;
    position: relative;
  }
}

@media only screen and (max-width: 600px) {

  .has-sticky-header.scroll-start .gc-header-mobile,
  .has-sticky-header.scroll-start .gc-header-mobile-top {
    top: 0;
  }

  .has-sticky-header.scroll-start .gc-header-mobile {
    height: 100%;
  }
}

@media only screen and (max-width: 490px) {
  .gc-header-mobile-content .gc-header-slide-menu {
    padding: 5px 35px 20px;
  }

  li.sliding-menu-inner {
    max-height: 300px;
  }

  .gc-sidemenu-lang-switcher li.sliding-menu-inner {
    max-height: 100px;
  }

  .gc-sidemenu-lang-switcher {
    margin-top: 20px;
  }

  .gc-sidemenu-copyright {
    bottom: 20px;
    width: calc(100% - 50px);
  }

  .gc-header-mobile.has-bar {
    flex-direction: column;
    width: 100%;
  }

  .gc-panel-close {
    margin-bottom: 0;
  }

  .gc-header-mobile-sidebar {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 70px;
    padding-left: 25px;
    padding-right: 25px;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .gc-mfp-close,
  .gc-filter-close,
  .gc-panel-close-button {
    border-width: 8px;
    width: 40px;
    height: 40px;
    padding: 0;
  }

  .gc-header-mobile-content {
    padding-left: 35px;
    padding-right: 35px;
  }

  .gc-header-mobile-sidebar-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .action-content {
    padding: 25px;
  }

  .gc-header-mobile-sidebar-inner {
    align-items: flex-end;
    flex: 1;
  }

  .sidebar-top-action .top-action-btn {
    margin: 0;
  }

  .sidebar-top-action {
    display: flex;
    flex: 0 0 90%;
    width: 90%;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    margin-top: 1px;
  }

  .sidebar-top-action .top-action-btn {
    height: 35px;
    width: 35px;
  }

  .top-action-btn .gc-wc-count {
    width: 25px;
    height: 25px;
    left: -5px;
    top: -5px;
    font-size: 15px;
  }

  .gc-header-mobile .gc-header-mobile-sidebar svg {
    max-width: 35px;
    max-height: 35px;
  }

  .top-action-btn[data-name="menu"] {
    flex: 0 0 20%;
    width: 20%;
    text-align: left;
  }

  .gc-header-mobile-sidebar-bottom {
    position: absolute;
    top: 70px;
    background: var(--gc-bg-soft);
    width: 100%;
    left: 0;
    padding: 10px;
    opacity: 0;
    visibility: hidden;
    border-top: 1px solid var(--gc-border);
    -moz-transform: translateX(-150%);
    -ms-transform: translateX(-150%);
    -webkit-transform: translateX(-150%);
    transform: translateX(-150%);
    will-change: transform;
  }

  .gc-header-mobile-sidebar-bottom.active {
    opacity: 1;
    visibility: visible;
    -moz-transform: translateX(0%);
    -ms-transform: translateX(0%);
    -webkit-transform: translateX(0%);
    transform: translateX(0%);
    -webkit-transition: opacity 0.75s ease, transform 0.75s ease;
    transition: opacity 0.75s ease, transform 0.75s ease;
  }

  .sidebar-bottom-socials a {
    margin: 0 10px;
  }

  .sidebar-top-action .share {
    display: flex;
  }

  .gc-header-mobile .gc-header-mobile-sidebar-logo {
    display: none;
  }

  .sidebar-bottom-socials {
    flex-direction: row;
    margin-top: 0;
    justify-content: center;
  }

  .sidebar-bottom-socials i {
    font-size: 18px;
  }

  .cart-empty-actions .gc-btn {
    margin-bottom: 10px;
  }

  .category-area .cat-count {
    top: 0;
    left: 10px;
    width: 16px;
    height: 16px;
    font-size: 10px;
  }
}

.gc-product {
  position: relative;
  display: flex;
  overflow: hidden;

  &:not(.row) {
    flex-direction: column;
    margin-bottom: 30px;
  }

  .has-images {
    .overlay-thumb {
      position: absolute;
      max-width: 100%;
      top: 0;
      left: 0;
      opacity: 0;
    }
  }

  &:not(.added-term) {
    &:hover {
      .has-images {
        .product-thumb {
          opacity: 0;
          -moz-transition: all ease 0.55s;
          -webkit-transition: all ease 0.55s;
          -o-transition: all ease 0.55s;
          -ms-transition: all ease 0.55s;
          transition: all ease 0.55s;
        }

        .overlay-thumb {
          opacity: 1;
          -moz-transition: all ease 0.55s;
          -webkit-transition: all ease 0.55s;
          -o-transition: all ease 0.55s;
          -ms-transition: all ease 0.55s;
          transition: all ease 0.55s;
        }
      }
    }
  }

  .gc-stock-status {
    padding: 2px 10px 2px;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 1px;
    background-color: var(--gc-bg-soft);
    color: var(--gc-dark);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .gc-thumb-wrapper {
    .gc-stock-status {
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }

  .gc-out-of-stock {
    background-color: var(--gc-gray-dark);
    color: var(--gc-light);
  }
}

.gc-product-widget-slider {
  .gc-product {
    &:not(.row) {
      margin-bottom: 0;
    }
  }
}

.gc-thumb-wrapper {
  position: relative;
  margin-bottom: 15px;
  overflow: hidden;
  height: 400px;

  img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
}

.gc-products.products {
  .gc-thumb-wrapper {
    img {
      width: 100%;
    }
  }
}

.gc-product-labels {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 2;

  >span {
    &:not(:last-child) {
      margin-bottom: 5px;
    }
  }
}

.has-iframe-video .gc-loop-product-iframe-wrapper>a {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.has-iframe-video .gc-product-labels {
  display: none;
}

.has-iframe-video .gc-loop-product-iframe-wrapper {
  padding: 0;
  padding-top: 100%;
  position: relative;
  overflow: hidden;
}

.has-iframe-video .gc-loop-product-iframe-wrapper video,
.has-iframe-video .gc-loop-product-iframe-wrapper iframe {
  position: absolute;
  width: 100%;
  max-width: 100%;
  height: 180%;
  top: 50%;
  left: 49%;
  transform: translate(-48%, -50%);
  pointer-events: none;
}

.gc-product .gc-product-name {
  margin: 5px 0;
}

.gc-has-hidden-cart .gc-btn-small,
.gc-product .gc-product-name {
  font-size: 15px;
}

.gc-loop-item-list-wrapper .star-rating {
  margin: 0 0 15px;
}

.gc-product-excerpt {
  margin: 0;
}

.gc-has-hidden-cart {
  position: relative;
  overflow: hidden;
  min-height: 33px;
}

.gc-has-hidden-cart .gc-price,
.gc-has-hidden-cart .gc-product-name,
.gc-has-hidden-cart .gc-cart-hidden {
  -moz-transition: all ease 0.25s;
  -webkit-transition: all ease 0.25s;
  -o-transition: all ease 0.25s;
  -ms-transition: all ease 0.25s;
  transition: all ease 0.25s;
}

.gc-has-hidden-cart .gc-btn-small,
.gc-block-right .gc-btn-small {
  color: var(--gc-dark);
  font-weight: 500;
}

.outofstock .gc-btn-small {
  text-decoration: underline;
}

.gc-has-hidden-cart .gc-price {
  display: block;
}

.gc-cart-hidden {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  -moz-transform: translateY(40px);
  -webkit-transform: translateY(40px);
  -o-transform: translateY(40px);
  -ms-transform: translateY(40px);
  transform: translateY(40px);
}

.gc-product:not(.woo-catalog-mode-enabled):hover .gc-cart-hidden {
  -moz-transform: translateY(0);
  -webkit-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
  z-index: 1;
}

.gc-product:not(.woo-catalog-mode-enabled):hover .gc-has-hidden-cart .gc-price,
.gc-product:not(.woo-catalog-mode-enabled):hover .gc-has-hidden-cart .gc-product-name {
  -moz-transform: translateY(-40px);
  -webkit-transform: translateY(-40px);
  -o-transform: translateY(-40px);
  -ms-transform: translateY(-40px);
  transform: translateY(-40px);
  opacity: 0;
  visibility: hidden;
}

.gc-product-list .gc-cart-hidden.gc-mini-icon {
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
}

.gc-loop-item-list-wrapper {
  position: relative;
  overflow: hidden;
  padding: 15px;
  margin-bottom: 30px;
  border: 1px solid var(--gc-border);
  -moz-box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, 0.3);
}

.gc-loop-item-list-wrapper .gc-product {
  margin-bottom: 0;
  align-items: center;
}

.gc-loop-item-list-wrapper .gc-product-labels {
  position: absolute;
  top: 0;
  left: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.gc-loop-item-list-wrapper .gc-product .gc-thumb-wrapper .gc-stock-status {
  left: auto;
}

.gc-loop-item-list-wrapper .gc-thumb-wrapper {
  margin-bottom: 0;
}

.gc-loop-item-list-wrapper .gc-btn {
  background-color: transparent !important;
  color: var(--gc-dark);
  border: 0;
  padding: 0;
  height: auto;
  justify-content: flex-start;
  text-transform: uppercase;
  font-size: 11px;
}

.gc-loop-item-list-wrapper .gc-price-cart {
  margin-top: 25px;
}

.gc-product .gc-price {
  font-weight: 400;
}

.gc-product span.del>span {
  //text-decoration: underline
}

.gc-loop-item-list-wrapper a.product-link.has-images {
  position: relative;
  overflow: hidden;
  display: block;
}

.gc-cart-hidden .gc-btn {
  width: 100%;
  font-size: 16px;
  width: 100%;
  display: block;
  padding: 0;
  color: var(--gc-dark);
}

.woocommerce div.product .gc-product form.cart {
  margin-bottom: 0;
  width: 100%;
}

.woocommerce div.product .gc-product form.cart div.quantity {
  margin: 0;
  float: none;
  max-width: 100%;
  position: absolute;
  height: 33px;
  z-index: 0;
}

.woocommerce div.product .gc-product form.cart.added-cart div.quantity {
  z-index: 1;
}

.gc-product .quantity-button.plus,
.gc-product .quantity-button.minus {
  background: var(--gc-dark);
}

.gc-product .view-cart,
.woocommerce.gc-product a.added_to_cart,
.gc-product .added_to_cart {
  display: none;
}

.gc-swatches-static {
  margin-top: 10px;
}

.gc-swatches-static:empty {
  display: none;
}

.gc-loop-product-buttons-static {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.gc-loop-product-buttons-static .gc-product-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 40px;
  background: var(--gc-bg-soft);
}

.gc-loop-product-buttons-static .gc-product-button:hover {
  color: var(--gc-light);
  background: var(--gc-dark);
}

.gc-loop-product-buttons-hover {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: -36px;
  top: 10px;
  opacity: 0;
  -moz-transition: all ease 0.25s;
  -webkit-transition: all ease 0.25s;
  -o-transition: all ease 0.25s;
  -ms-transition: all ease 0.25s;
  transition: all ease 0.25s;
  z-index: 2;
}

.gc-product:hover .gc-loop-product-buttons-hover {
  right: 15px;
  opacity: 1;
}

.gc-product .gc-loop-product-buttons-hover.loading {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  transition: none;
  transform: none;
}

.gc-product .gc-loop-product-buttons-hover.loading .loading-wrapper {
  background: rgba(255, 255, 255, 0.95);
}

.gc-product-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gc-bg-soft);
}

.gc-product-button:hover,
.gc-product-button:hover svg {
  //color: var(--gc-light);
  //background: var(--gc-dark);
  //fill: var(--gc-light)
}

.gc-loop-product-buttons-hover .gc-product-button {
  width: 36px;
  height: 36px;
  border-radius: 40px;
  margin-top: 10px;
}

.gc-loop-product-buttons-hover .gc-product-button .gc-svg-icon {
  max-width: 18px;
}

.gc-loop-product-buttons-hover .gc-product-button:first-child {
  margin-top: 0;
}

.gc-add-to-cart-btn.gc-product-button {
  position: relative;
  overflow: hidden;
}

.gc-add-to-cart-btn.gc-product-button>a:first-child {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.gc-price-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.gc-block-right.gc-mini-icon {
  display: inline-flex;
  justify-content: flex-end;
}

.gc-mini-icon .gc-product-button {
  width: 27px;
  height: 27px;
  border-radius: 27px;
  cursor: pointer;
}

.gc-mini-icon .gc-product-button.cart-icon {
  position: relative;
}

.gc-mini-icon .gc-product-button.cart-icon a.gc-btn {
  position: absolute;
  font-size: 0;
  width: 100%;
  height: 100%;
  border: 0;
  min-width: auto;
  background: transparent;
}

.gc-mini-icon .gc-product-button:not(:first-child) {
  margin-left: 5px;
}

.gc-mini-icon svg {
  max-width: 30px;
  max-height: 30px;
}

.gc-title-buttons-static .gc-mini-icon {
  margin-top: 10px;
}

.gc-product-button.added {
  background: var(--gc-dark);
  color: var(--gc-light);
}

.gc-product-button.added svg {
  fill: var(--gc-light) !important;
}

.gc-product-button.added:hover {
  opacity: 0.7;
}

.gc-title-block .gc-block-left {
  flex: 0 0 60%;
}

.gc-title-block.gc-title-buttons .gc-block-left {
  flex: 0 0 100%;
}

.gc-title-rating .gc-block-left {
  flex: 0 0 75%;
}

.gc-title-rating .gc-block-right {
  align-items: center;
}

.woocommerce .products .gc-price-rating .star-rating,
.woocommerce .products .gc-title-rating .star-rating {
  margin: 0;
}

.gc-cart-buttons.gc-inline-two-block,
.gc-title-cart.gc-inline-two-block,
.gc-title-price.gc-inline-two-block,
.gc-title-rating.gc-inline-two-block {
  align-items: center;
  justify-content: space-between;
}

.gc-btn-text {
  position: relative;
  color: var(--gc-flex);
}

.gc-btn-text a:hover {
  position: relative;
  color: var(--gc-primary);
}

.gc-btn-text .ajax_add_to_cart.added {
  color: var(--gc-primary);
}

.gc-title-cart .gc-btn-text .view-cart {
  margin-top: 0;
  margin-left: 10px;
}

.gc-btn-text a:after {
  content: "";
  height: 2px;
  width: 0;
  background-color: currentColor;
  position: absolute;
  bottom: 0;
  left: 0;
  -webkit-transition: width 0.25s;
  transition: width 0.25s;
}

.gc-btn-text a:hover:after {
  width: 100%;
}

.gc-cart-buttons .gc-block-left .gc-btn,
.gc-cart-buttons .gc-block-left .gc-btn.view-cart {
  min-width: auto;
  margin-top: 0;
}

.gc-cart-buttons .gc-block-left .gc-btn.view-cart {
  margin-left: 5px;
}

.gc-loop-slider.swiper-container-horizontal>.swiper-pagination-bullets {
  width: auto;
  left: auto;
  right: 10px;
}

.gc-loop-slider .swiper-pagination.swiper-pagination-clickable.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 9px;
  right: 0;
  width: auto;
  text-align: right;
  padding: 0 10px;
}

.woocommerce.gc-product .swiper-pagination-bullets .swiper-pagination-bullet:before {
  width: 1px;
  height: 1px;
}

.gc-product .gc-thumb-wrapper.gc-thumb-slider .gc-stock-status {
  z-index: 1;
}

.gc-swatches-hover {
  display: flex;
  flex-direction: column;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  opacity: 0;
}

.woosv_color .gc-product .gc-swatches-hover .variations .wpcvs-type-color .wpcvs-term {
  background: transparent;
}

.gc-product .gc-swatches-hover .variations .reset {
  position: relative;
  bottom: auto;
  right: auto;
}

.gc-product .gc-swatches-hover .variations .reset a {
  border: 2px solid var(--gc-dark);
  color: var(--gc-dark);
  text-align: center;
  padding: 3px 10px;
  line-height: 1;
  width: 100%;
  display: block;
}

.gc-product .gc-swatches-hover .variations .reset a:hover {
  background-color: var(--gc-dark);
  color: var(--gc-light);
}

.gc-product.added-term .gc-thumb-slider .gc-swatches-hover,
.gc-product:hover .gc-thumb-slider .gc-swatches-hover {
  opacity: 1;
}

.gc-product.added-term .gc-thumb-slider.has-swatches .swiper-pagination {
  opacity: 0;
}

.gc-product .gc-thumb-slider .gc-product-labels {
  z-index: 1;
}

.gc-product-type-5 .gc-price-buttons {
  margin-bottom: 5px;
}

.gc-loop-product-buttons-mobile {
  display: none;
}

.gc-product-bottom-title {
  color: var(--gc-dark);
}

@media (min-width: 1025px) {
  .gc-add-to-cart-btn.gc-product-button {
    display: none;
  }

  .gc-add-to-cart-btn.gc-product-button+.gc-product-button {
    margin-top: 0;
  }
}

@media (max-width: 1024px) {
  .gc-before-loop.gc-inline-two-block>div {
    flex: 0 0 auto;
    margin-top: 0;
  }

  .gc-inline-two-block {
    flex-wrap: wrap;
  }

  .gc-block-left,
  .gc-block-right,
  .gc-title-block .gc-block-left {
    flex: 0 0 100%;
  }

  .gc-block-right,
  .gc-cart-buttons .gc-block-right {
    margin-top: 10px;
  }

  .gc-cart-buttons .gc-block-left {
    flex-wrap: wrap;
  }

  .gc-cart-buttons .gc-btn,
  .gc-cart-buttons .view-cart {
    width: 100%;
  }

  .gc-cart-buttons .view-cart {
    margin-top: 10px;
  }

  .gc-cart-buttons .gc-block-left .gc-btn.view-cart {
    margin-left: 0;
  }

  .gc-block-right {
    text-align: start;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .gc-product .gc-thumb-slider .gc-swatches-hover {
    opacity: 1;
  }

  .gc-price-buttons .gc-block-right.gc-mini-icon {
    justify-content: flex-start;
  }

  .gc-product:hover .gc-cart-hidden {
    -moz-transform: translateY(0);
    -webkit-transform: translateY(0);
    -o-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    z-index: 1;
  }

  .gc-cart-hidden,
  .gc-product:hover .gc-cart-hidden,
  .gc-product:hover .gc-has-hidden-cart .gc-price,
  .gc-product:hover .gc-has-hidden-cart .gc-product-name {
    -moz-transform: none;
    -webkit-transform: none;
    -o-transform: none;
    -ms-transform: none;
    transform: none;
    opacity: 1;
    visibility: visible;
    position: relative;
  }

  .gc-product .gc-loop-product-buttons-hover {
    right: 10px;
    opacity: 1;
  }

  .gc-loop-product-buttons-hover,
  .gc-cart-hidden,
  .gc-block-right:empty {
    display: none;
  }

  .gc-loop-product-buttons-mobile .gc-mini-icon .gc-product-button {
    width: 24px;
    height: 24px;
    border-radius: 24px;
  }

  .gc-loop-product-buttons-mobile .gc-mini-icon svg {
    max-width: 14px;
    max-height: 14px;
  }

  .gc-loop-product-buttons-mobile {
    display: flex;
    margin-bottom: 10px;
    gap: 10px;
  }

  .gc-loop-item-list-wrapper .gc-thumb-wrapper {
    margin-bottom: 15px;
  }

  .gc-loop-item-list-wrapper .gc-block-right {
    display: none;
  }

  .gc-loop-item-list-wrapper .gc-price-cart {
    margin-top: 15px;
  }
}

@media (max-width: 992px) {
  .gc-hidden-on-mobile {
    display: none;
  }
}

@media (max-width: 480px) {
  .gc-block-right {
    flex-wrap: wrap;
  }

  .gc-loop-product-buttons-hover .gc-product-button {
    width: 25px;
    height: 25px;
  }

  .gc-loop-product-buttons-hover .gc-svg-icon {
    max-width: 18px;
    max-height: 18px;
  }

  .gc-cart-hidden {
    margin-top: 5px;
  }

  .gc-btn {
    min-width: auto;
    height: 38px;
  }

  .gc-label {
    height: 16px;
    padding: 0 7px;
    font-size: 8px;
    letter-spacing: 1px;
  }

  .gc-product-name {
    font-size: 14px;
  }

  .gc-loop-item-list-wrapper .gc-product .gc-btn,
  .gc-product .gc-price {
    font-size: 12px;
  }

  .gc-loop-item-list-wrapper .gc-product-excerpt {
    display: none;
  }

  .gc-loop-item-list-wrapper .gc-price-cart {
    margin-top: 10px;
  }

  .gc-single-product-type-stretch .gc-breadcrumbs {
    display: none;
  }
}

.wps-form-row input:not([type="checkbox"]):not([type="radio"]):not([type="submit"]),
.wps-form-row textarea {
  width: 100%;
  background-color: #f2f4ec;
  border: none;
  padding: 15px;
}

input[type="checkbox"],
input[type="radio"] {
  margin: 0;
  line-height: 0;
  margin-right: 5px;
}

nav.navbar.transparent-white ul.ul-h.nav-menu>li>a {
  text-decoration: none;
}

nav.navbar .container .row>div {
  padding: 0;
}

body:not(.edlementor-page) .nav-menu.nav-social i {
  font-family: "FontAwesome";
}

.gc-category-content {
  padding: 15px;
  background-color: var(--gc-bg-soft);
}

.gc-category-content .gc-category-title,
.gc-category-content .gc-category-description {
  margin: 0;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 1px;
  text-align: center;
}

.gc-category-content .gc-category-title+.gc-category-description {
  margin-top: 15px;
  color: var(--gc-dark);
}

.gc-category-content .gc-category-count {
  font-size: 12px;
}

.gc-product-category-slider .slick-list {
  margin-left: -15px;
  margin-right: -15px;
}

.gc-product-category-slider .gc-category-item {
  padding: 0 15px;
}

.gc-category-thumb.img-center img {
  margin: 0 auto;
}

.gc-category-thumb.img-right img {
  margin-left: auto;
  margin-right: 0;
}

.gc-hero-banner-product-slider .banner-details-col,
.gc-hero-banner-product-slider .banner-image-col {
  align-items: center;
  display: inline-flex;
  justify-content: flex-start;
}

.gc-hero-banner-product-slider .gc-container-xl.container-xl,
.gc-hero-banner-product-slider .hero-banner-slide-row {
  height: 100%;
}

.gc-hero-banner-product-slider .banner-details-wrapper>* {
  width: 100%;
}

.gc-hero-banner-product-slider .product-image-wrapper {
  display: flex;
}

.gc-hero-banner-product-slider .hero-banner-slide-row {
  align-items: stretch;
}

.gc-hero-banner-product-slider .hero-banner-slide-item .banner-details-wrapper {
  padding: 60px;
}

.gc-hero-banner-product-slider .slick-dots {
  text-align: center;
  position: absolute;
  bottom: 40px;
  margin: 0;
}

.gc-hero-banner-product-slider.dots-alingment-center .slick-dots {
  left: 50%;
  transform: translateX(-50%);
}

.gc-hero-banner-product-slider.dots-alingment-left .slick-dots {
  left: 50px;
  right: auto;
  transform: none;
}

.gc-hero-banner-product-slider.dots-alingment-right .slick-dots {
  right: 50px;
  left: auto;
}

.gc-woo-banner-wrapper {
  position: relative;
  overflow: hidden;
  display: block;
}

.gc-banner-content-bottom {
  background: #fff;
  padding: 15px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-content: flex-start;
  align-items: flex-start;
  opacity: 0;
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.gc-woo-banner-wrapper:hover .gc-banner-content-bottom {
  opacity: 1;
}

.gc-woo-banner-wrapper.banner-style-classic .gc-banner-image:before,
.gc-woo-banner-wrapper:not(.banner-style-classic):before {
  content: "";
  background: rgba(57, 57, 58, 0.34);
  z-index: 1;
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.gc-woo-banner-wrapper .gc-banner-link {
  z-index: 3;
}

.gc-woo-banner-wrapper .gc-banner-image {
  position: relative;
  overflow: hidden;
  display: block;
  min-height: 300px;
  background-repeat: no-repeat;
  background-size: cover;
}

.gc-woo-banner-wrapper:before,
.gc-woo-banner-wrapper .gc-banner-link,
.gc-woo-banner-wrapper .gc-banner-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}

.gc-woo-banner-wrapper img {
  min-height: 100%;
  object-fit: cover;
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.gc-woo-banner-wrapper:hover img {
  transform: scale(1.2);
}

.gc-woo-banner-wrapper .gc-banner-content {
  padding: 30px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.gc-woo-banner-wrapper:not(.banner-style-classic) .gc-banner-content,
.gc-woo-banner-wrapper:not(.banner-style-classic) .gc-banner-title {
  color: var(--gc-light);
}

.gc-woo-banner-wrapper {
  .gc-banner-title {
    margin: 0;
  }

  .banner-content-item {
    display: block;
    margin: 0;
    line-height: 1;

    &.gc-banner-catname,
    &.gc-banner-title,
    &.gc-banner-catcount {
      color: var(--gc-dark);
    }
  }

  span.gc-banner-button {
    display: inline-block;
    padding: 10px 15px;
    color: var(--gc-light);
    background: var(--gc-dark);
  }

  .banner-content-item+.banner-content-item {
    margin-top: 15px;
  }
}

.gc-woo-banner-wrapper.banner-style-card-hover .gc-banner-content .banner-content-item {
  opacity: 0;
  -webkit-transform: translateY(20px);
  transform: translateY(20px);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
}

.gc-woo-banner-wrapper.banner-style-card-hover:hover .gc-banner-content .banner-content-item+.banner-content-item {
  -webkit-transition-delay: 0.15s;
  transition-delay: 0.15s;
}

.gc-woo-banner-wrapper.banner-style-card-hover:hover .gc-banner-content>div>* {
  opacity: 1;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.gc-woo-banner-wrapper.banner-style-classic .gc-banner-content {
  position: relative;
}

.gc-woo-banner-wrapper.banner-style-classic .gc-banner-content {
  position: relative;
}

.gc-woo-banner-iframe-container {
  position: relative;
}

.gc-woo-banner-iframe-wrapper {
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  position: absolute;
  overflow: hidden;
  z-index: 0;
  direction: ltr;
  -webkit-transition: opacity 1s;
  -o-transition: opacity 1s;
  transition: opacity 1s;
  pointer-events: none;
}

.gc-woo-banner-iframe-wrapper iframe,
.gc-woo-banner-iframe-wrapper video {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 110%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  max-width: none !important;
}

.gc-products-column,
.shop-cat-banner-template-wrapper {
  position: relative;
  overflow: hidden;
}

.gc-products-column {
  .gc-block-left {
    p {
      margin-bottom: 0;
    }
  }
}

.gc-testimonials .slick-list {
  padding: 0 !important;
}

.gc-testimonials .gc-testimonial-content p {
  font-size: 22px;
  line-height: 1.75;
}

.gc-testimonials .gc-testimonial-info .name {
  margin: 0;
}

.gc-testimonials .gc-testimonial-info img {
  border-radius: 100%;
  display: inline-block;
}

.gc-testimonial-1 .gc-testimonial-content {
  margin-top: 40px;
}

.gc-testimonial-1 {
  text-align: center;
}

.gc-testimonial-1.align-right {
  text-align: right;
}

.gc-testimonial-1.align-left {
  text-align: left;
}

.gc-testimonial-1 .gc-testimonial-info img {
  margin-bottom: 20px;
}

.gc-testimonial-2 .gc-testimonial-avatar,
.gc-testimonial-3 .gc-testimonial-avatar {
  margin-right: 30px;
}

.gc-testimonial-2 .gc-testimonial-content {
  margin-bottom: 40px;
}

.gc-testimonial-2 .gc-testimonial-info,
.gc-testimonial-3 .gc-testimonial-info {
  text-align: left;
  width: 100%;
  justify-content: center;
}

.gc-testimonial-2.align-right .gc-testimonial-info.gc-flex,
.gc-testimonial-3.align-right .gc-testimonial-info.gc-flex {
  flex-direction: row-reverse;
  justify-content: flex-start;
  text-align: right;
}

.gc-testimonial-2.align-left .gc-testimonial-info.gc-flex,
.gc-testimonial-3.align-left .gc-testimonial-info.gc-flex {
  justify-content: flex-start;
}

.gc-testimonial-2.align-right .gc-testimonial-info.gc-flex .gc-testimonial-avatar,
.gc-testimonial-3.align-right .gc-testimonial-info.gc-flex .gc-testimonial-avatar {
  margin-right: 0;
  margin-left: 30px;
}

.gc-testimonial-3 .gc-testimonial-content {
  margin-top: 40px;
}

.swiper-slide>[class^="col-"],
.swiper-slide>[class*=" col-"] {
  -ms-flex: 0 0 100% !important;
  flex: 0 0 100%;
  max-width: 100%;
  padding: 0 !important;
}

.gc-tab-nav {
  display: flex;
  flex-wrap: wrap;
}

.gc-tab-nav-item {
  color: var(--gc-dark);
  position: relative;
  padding-bottom: 5px;
  overflow: hidden;
  cursor: pointer;
}

.gc-tab-nav-item:hover,
.gc-tab-nav-item.is-active {
  color: currentColor;
}

.gc-tab-nav-item:after {
  content: "";
  width: 0;
  position: absolute;
  bottom: 0;
  left: -2px;
  height: 2px;
  background-color: currentColor;
  -webkit-transition: width 0.25s;
  transition: width 0.25s;
}

.gc-tab-nav-item:hover:after,
.gc-tab-nav-item.is-active:after {
  width: 100%;
  left: 0;
}

.gc-tab-nav-item:not(:last-child) {
  margin-right: 20px;
}

.gc-tab-page {
  display: none;
  margin-top: 30px;
  position: relative;
}

.gc-tab-page.is-active {
  display: block;
}

@media (max-width: 768px) {
  .gc-tab-page:not(.is-active) {
    display: none;
  }

  .gc-tab-nav .gc-tab-nav-item {
    margin: 0 20px 0 0;
    padding-bottom: 5px;
  }
}

form#commentform {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: -10px;
  margin-left: -10px;
}

.comment-form .comment-notes,
.comment-form .comment-form-comment,
.comment-form .form-submit,
.comment-form .comment-form-rating,
.comment-form .comment-form-cookies-consent,
.comment-form .comment-form-images {
  flex-basis: 100%;
  max-width: 100%;
  width: 100%;
}

.comment-form>div,
.comment-form>p {
  flex: 1 1 220px;
  padding-right: 10px;
  padding-left: 10px;
}

.comment-form input:not(.btn):not([type="checkbox"]) {
  margin-bottom: 0;
}

p.comment-form-cookies-consent {
  display: flex;
  align-items: flex-start;
}

.post--data p.comment-form-cookies-consent {
  align-items: center;
  margin-top: 0;
}

.post--data .comment-form-comment {
  margin: 0;
}

.post--data #respond p.form-submit {
  margin: 0;
}

p.comment-form-cookies-consent input#wp-comment-cookies-consent {
  margin-right: 10px;
}

p.comment-form-cookies-consent label {
  width: auto;
  margin: 0;
  line-height: 1;
}

.comment-form input#submit {
  width: auto;
  line-height: 1;
  min-width: 150px;
  border: 0;
}

#nt-single .blog-post-meta {
  margin: 30px 0 20px;
}

.header-top-area.gc-elementor-before-header {
  position: relative;
  z-index: 1;
}

.gc-bottom-mobile-nav {
  position: fixed;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  height: 4rem;
  bottom: 0;
  left: 0;
  background-color: var(--gc-light);
  // -webkit-box-shadow: -2px 2px 45px -20px rgb(0 0 0 / 30%);
  // -moz-box-shadow: -2px 2px 45px -20px rgba(0, 0, 0, .3);
  // box-shadow: -2px 2px 45px -20px rgb(0 0 0 / 30%);
  z-index: 102;
  padding-top: 5px;
  will-change: transform;
  -webkit-transition: opacity 0.25s ease, ease;
  transition: opacity 0.25s ease, transform 0.25s ease;
  opacity: 0;
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
}

.gc-overlay-open .gc-bottom-mobile-nav,
body:not(.scroll-start) .gc-bottom-mobile-nav.show-onscroll {
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
  opacity: 0;
}

.gc-bottom-mobile-nav .mobile-nav-wrapper {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}

.gc-bottom-mobile-nav .mobile-nav-wrapper>ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  margin: 0;
  padding: 0;
  list-style: none;
}

.gc-bottom-mobile-nav .menu-item a {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  text-decoration: none;
  color: var(--gc-gray-dark);
}

.gc-bottom-mobile-nav .menu-item svg {
  max-width: 22px;
  max-height: 22px;
}

.gc-bottom-mobile-nav .menu-item i {
  font-size: 20px;
}

.gc-bottom-mobile-nav .menu-item a,
.gc-bottom-mobile-nav .menu-item a span {
  font-size: 12px;
}

.gc-bottom-mobile-nav .menu-item a span.gc-wc-count,
.gc-bottom-mobile-nav .gc-wc-count {
  width: 15px;
  height: 15px;
  left: 13px;
  top: -3px;
  font-size: 9px;
}

@media screen and (max-width: 1024px) {

  .gc-bottom-mobile-nav.active,
  .gc-bottom-mobile-nav.show-allways,
  .scroll-start .gc-bottom-mobile-nav.show-onscroll {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }

  .has-bottom-mobile-nav .scroll-to-top {
    right: 15px;
    bottom: 80px;
  }

  .has-bottom-mobile-nav .gc-product-bottom-popup-cart.active:not(.relative) {
    bottom: 64px;
  }

  body.bottom-popup-cart-active.has-bottom-mobile-nav .scroll-to-top {
    bottom: 150px;
  }

  body.has-bottom-mobile-nav {
    padding-bottom: 64px;
  }
}

.account-popup-content.gc-popup-item {
  max-width: 600px;
  padding: 58px 40px 40px 40px;
}

.account-popup-content.gc-popup-item .account-area.account-logged-in {
  overflow: hidden;
}

.account-popup-content.gc-popup-item .woocommerce-form.woocommerce-form-login.login,
.account-popup-content.gc-popup-item .woocommerce-form-register,
.checkout_coupon.woocommerce-form-coupon {
  max-width: 100%;
  margin-top: 0;
  margin-bottom: 0;
}

.account-popup-content.gc-popup-item .account-area .account-area-form-wrapper {
  display: flex;
}

.account-popup-content.gc-popup-item .account-area .login-form-content,
.account-popup-content.gc-popup-item .account-area .register-form-content {
  position: relative;
  -webkit-transition: opacity 0.15s ease, transform 0.25s ease;
  transition: opacity 0.15s ease, transform 0.25s ease;
}

.account-popup-content.gc-popup-item .account-area .login-form-content,
.account-popup-content.gc-popup-item .account-area .register-form-content {
  position: relative;
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  flex: 0 0 100%;
}

.account-popup-content.gc-popup-item .account-area .login-form-content.active,
.account-popup-content.gc-popup-item .account-area .register-form-content.active {
  opacity: 1;
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.account-popup-content.gc-popup-item .account-area .register-form-content.active,
.account-popup-content.gc-popup-item .account-area .login-form-content:not(.active) {
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

.account-popup-content.gc-popup-item .lost_password {
  margin-bottom: 0;
}

.woocommerce .account-popup-content.gc-popup-item .register-form-content form .form-row {
  margin: 0;
}

.gc-header-mobile-top-actions a.account-page-link,
.gc-header-mobile-top-actions a.gc-open-popup {
  line-height: 1;
}

@media (max-width: 576px) {

  .nt-gc-content .gc-myaccount-navigation>ul,
  .gc-myaccount-navigation>ul {
    margin: 0;
    padding-left: 0;
  }

  .gc-myaccount-page-content .gc-myaccount-page-content-inner {
    padding: 30px;
  }

  .gc-myaccount-page-content .gc-myaccount-content a:not(.button) {
    display: initial;
  }

  .gc-myaccount-page-content .gc-myaccount-content a:not(.button):after {
    display: none;
  }
}

.gc-product-popup:not(.active) {
  opacity: 0;
  visibility: hidden;
}

.gc-product-popup.active {
  z-index: 100;
}

.zoom-in .mfp-img {
  opacity: 0;
}

.mfp-product-gallery .mfp-figure figure {
  max-width: 430px;
}

.item-shortcode-wrapper>div {
  width: 100%;
}

@media (max-width: 1024px) {
  .gc-myaccount-page-content.row>div {
    width: 100%;
  }

  .woocommerce-page .gc-myaccount-page-content a.button {
    padding: 0 10px;
    min-height: 38px;
    min-width: auto;
  }

  .gc-myaccount-content {
    padding-left: 15px;
  }

  .nt-gc-content .gc-myaccount-navigation ul {
    padding: 0;
    margin: 0;
  }

  .gc-myaccount-content {
    padding-left: 0;
  }

  .nt-gc-content .gc-myaccount-content table.table.table-striped {
    margin-bottom: 0;
  }
}

@media (max-width: 576px) {

  .nt-gc-content .gc-myaccount-content table.table.table-striped th,
  .nt-gc-content .gc-myaccount-content table.table.table-striped td {
    padding: 8px;
    font-size: 10px;
  }

  .woocommerce-page .gc-myaccount-page-content a.button {
    padding: 0 5px;
    min-height: 20px;
    font-size: 10px;
  }

  .gc-myaccount-page-content .gc-myaccount-page-content-inner {
    padding: 15px;
  }
}

.item-shortcode-wrapper>div {
  width: 100%;
}

.gallery-menu {
  text-align: center;
  margin-bottom: 40px;
}

.gallery-menu span {
  cursor: pointer;
  position: relative;
}

.gallery-menu span+span {
  margin-left: 15px;
}

.gallery-menu span:hover,
.gallery-menu span.active {
  color: var(--gc-primary);
}

.gallery-menu span:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  -webkit-transition: width 0.25s ease;
  transition: width 0.25s ease;
}

.gallery-menu span:hover:before {
  width: 100%;
}

ul#shipping_method {
  list-style: none;
  padding: 0;
}

ul#shipping_method li {
  display: flex;
  align-items: center;
  line-height: 1;
  margin-bottom: 15px;
}

ul#shipping_method li label {
  margin: 0;
  display: inline-flex;
  justify-content: space-between;
}

.gc-cart-totals-inner .gc-tax-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  line-height: 1;
}

.gc-cart-totals-inner .gc-tax-total,
.gc-cart-totals-inner .woocommerce-Price-amount {
  font-weight: 600;
}

.gc-cart-discount {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid var(--gc-border);
}

.cart-item-details .product-link img {
  max-width: 80px;
}

.cart-item-details dl.variation {
  display: flex;
  margin: 5px 0;
  line-height: 1;
  align-items: flex-start;
  flex-wrap: wrap;
}

.cart-item-details dl.variation dt {
  display: none;
}

.cart-item-details dl.variation dd {
  margin: 0px !important;
  margin-right: 6px !important;
  padding-right: 6px;
}

.cart-item-details dl.variation dd:not(:last-child) {
  border-right: 1px solid #ddd;
}

.gc-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.gc-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--gc-dark);
}

.gc-scrollbar::-webkit-scrollbar-track {
  background-color: var(--gc-gray);
}

.gc-main-slider {
  .gc-slide-inner {
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px;
    text-align: left;

    @media (max-width: 1199.98px) {
      padding: 40px;
    }

    @media (max-width: 991.98px) {
      padding: 30px;
    }

    @media (max-width: 579.98px) {
      padding: 30px 20px;
    }

    @media (max-width: 479.98px) {
      padding: 20px 10px;
    }
  }

  .gc-btn-large {
    background-color: var(--gc-green);
  }

  .swiper-button-prev {
    left: 10px;

    &:before {
      display: none;
    }
  }

  .swiper-button-next {
    right: 10px;

    &:before {
      display: none;
    }
  }
}

.gc-slide-head {
  font-size: 78px;
  margin: 0;
  line-height: 1;
  color: var(--gc-light);

  @media (max-width: 1199.98px) {
    font-size: 60px;
  }

  @media (max-width: 991.98px) {
    font-size: 54px;
  }

  @media (max-width: 579.98px) {
    font-size: 42px;
  }

  @media (max-width: 479.98px) {
    font-size: 32px;
  }
}

.gc-slide-text {
  position: relative;
  font-size: 1.5em;
  margin: 10px 0 30px;
  cursor: default;
  color: var(--gc-light);
}

.gc-swiper-theme-style.gc-main-slider .swiper-pagination-bullets {
  position: absolute;
  top: auto;
  left: 0;
  bottom: 5px;
  right: 0;
  text-align: center;
  margin: 0;
}

.gc-main-slider .has-animation:not(.animated) {
  visibility: hidden;
}

.has-header-sidebar .header-spacer {
  display: none;
}

.ninetheme-scrollbar {
  padding-right: 10px;
}

.ninetheme-scrollbar>.elementor-container::-webkit-scrollbar,
.ninetheme-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.ninetheme-scrollbar>.elementor-container::-webkit-scrollbar-thumb,
.ninetheme-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--gc-dark);
}

.ninetheme-scrollbar>.elementor-container::-webkit-scrollbar-track,
.ninetheme-scrollbar::-webkit-scrollbar-track {
  background-color: var(--gc-gray);
}

.has-header-sidebar .site-content {
  display: flex;
}

.has-header-sidebar .site-content {
  flex: 1;
  width: calc(100% - 360px);
  flex: 1;
  min-height: 100vh;
  margin-left: 360px;
  display: flex;
  flex-direction: column;
}

.has-header-sidebar.header-sidebar-position-right .site-content {
  margin-left: auto;
  margin-right: 360px;
}

.gc-main-sidebar-header .elementor-section .elementor-container {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.gc-main-sidebar-header .elementor-section .elementor-container .elementor-column {
  width: 100%;
}

body.has-header-sidebar .elementor-element.elementor-element-af676c3 .gc-category-item {
  -ms-flex: 0 0 calc(100% / 2);
  flex: 0 0 calc(100% / 2);
  max-width: calc(100% / 2);
}

body.has-header-sidebar.header-sidebar-color-dark .elementor-462 .elementor-heading-title,
body.has-header-sidebar.header-sidebar-color-dark .elementor-element.elementor-element-ad2503e .elementor-icon-list-text {
  color: var(--gc-light);
}

body.has-header-sidebar.header-sidebar-color-dark .gc-main-sidebar-header .elementor-462 .gc-btn {
  border: 1px solid var(--gc-black-border);
}

.header-sidebar-position-right .gc-side-panel,
.gc-header-mobile.after-sidebar-header {
  z-index: 999;
  max-width: 360px;
}

.gc-main-sidebar-header .gc-mobile-header-actions {
  margin-bottom: 40px;
}

.gc-main-sidebar-header .logo {
  max-width: 100%;
}

.gc-main-sidebar-header,
.gc-main-sidebar-header.gc-active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  max-width: 360px;
  min-width: 240px;
  height: 100%;
  z-index: 999;
  background: var(--gc-light);
  padding: 120px 40px 60px;
  border-right: 1px solid var(--gc-border);
  opacity: 1;
  will-change: transform;
  -webkit-transition: opacity 0.55s ease, transform 0.25s ease;
  transition: opacity 0.55s ease, transform 0.25s ease;
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  overflow-y: auto;
}

.has-header-sidebar:not(.header-sidebar-position-right) .gc-side-panel {
  left: 0;
  right: auto;
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
  max-width: 360px;
  z-index: 999;
}

.has-header-sidebar:not(.header-sidebar-position-right) .gc-side-panel.active {
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.has-header-sidebar:not(.header-sidebar-position-right) .panel-content .panel-content-item {
  left: 0;
  right: auto;
  -moz-transform: translateX(-150%);
  -ms-transform: translateX(-150%);
  -webkit-transform: translateX(-150%);
  transform: translateX(-150%);
}

.has-header-sidebar:not(.header-sidebar-position-right) .panel-content .panel-content-item.active {
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.header-sidebar-position-right .gc-main-sidebar-header,
.header-sidebar-position-right .gc-main-sidebar-header.gc-active {
  left: auto;
  right: 0;
}

.header-sidebar-position-right .gc-header-mobile.after-sidebar-header,
.header-sidebar-position-right .gc-header-mobile.after-sidebar-header .action-content {
  left: auto;
  right: 0;
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}

.header-sidebar-position-right .gc-header-mobile.after-sidebar-header.active,
.header-sidebar-position-right .gc-header-mobile.after-sidebar-header .action-content.active {
  -moz-transform: translateX(0%);
  -ms-transform: translateX(0%);
  -webkit-transform: translateX(0%);
  transform: translateX(0%);
}

.gc-main-sidebar-inner.second-menu {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--gc-border);
}

.gc-main-sidebar-header>div {
  width: 100%;
}

.gc-main-sidebar-inner {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.gc-main-sidebar-header ul {
  list-style: none;
}

.gc-main-sidebar-header .primary-menu li,
.gc-main-sidebar-header .primary-menu li>a {
  position: relative;
}

.gc-main-sidebar-header .primary-menu>li>a,
.gc-main-sidebar-header .submenu>li>a {
  display: flex;
  align-items: center;
}

.gc-main-sidebar-header .primary-menu>li>a {
  font-size: 20px;
  font-weight: 600;
}

.gc-main-sidebar-header .second-menu .primary-menu>li>a {
  font-size: 13px;
  text-transform: uppercase;
}

.gc-main-sidebar-header .second-menu .primary-menu>li>a:hover {
  color: var(--gc-primary);
}

.gc-main-sidebar-header .submenu>li {
  font-size: 16px;
}

.gc-main-sidebar-header .primary-menu li+li {
  margin-top: 5px;
}

.gc-main-sidebar-header .dropdown-btn {
  position: absolute;
  right: 0;
  cursor: pointer;
  z-index: 1;
}

.gc-main-sidebar-header .submenu {
  display: none;
}

.gc-main-sidebar-header .submenu:not(.item-shortcode-wrapper) {
  padding: 15px 0 10px 15px;
}

.gc-main-sidebar-header .item-shortcode-wrapper {
  margin-top: 15px;
}

.gc-main-sidebar-header .header-top-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 30px 0;
}

.gc-main-sidebar-header .top-action-btn {
  position: relative;
  display: flex;
  align-items: center;
  margin-right: 30px;
}

.gc-main-sidebar-header .top-action-btn .gc-wc-count {
  left: auto;
  right: -15px;
  top: -6px;
}

.gc-main-sidebar-header .search-area-top {
  margin-bottom: 0;
}

.has-header-sidebar .gc-popup-search-panel {
  max-width: calc(100% - 360px);
  left: 360px;
}

.autocomplete-suggestions {
  padding: 10px;
  background: var(--gc-light);
  border: 1px solid var(--gc-border);
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  overflow-y: auto;
  max-width: 280px !important;
  max-height: calc(100vh - 210px) !important;
}

.gc-main-sidebar-header .autocomplete-suggestions {
  bottom: 50px;
}

.gc-mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: var(--gc-light);
  z-index: 99;
  padding: 0 20px;
}

.gc-mobile-header-spacer {
  position: relative;
  height: 80px;
}

.admin-bar .gc-mobile-header {
  top: 32px;
}

.gc-mobile-header-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1;
}

.gc-mobile-header-actions span {
  display: inline-flex;
  align-items: center;
}

.gc-mobile-header-actions>span+span {
  margin-left: 15px;
}

.sidebar-menu-active .gc-bottom-mobile-nav {
  z-index: 97;
}

.gc-main-sidebar-header .primary-menu li.gc-active>a span {
  transform: rotate(180deg);
}

.gc-main-sidebar-inner.second-menu {
  margin-top: 30px;
  padding-top: 35px;
  border-top: 1px solid var(--gc-border);
}

.gc-sidemenu-lang-switcher {
  display: flex;
  width: 100%;
  align-items: center;
  margin-top: 35px;
  padding-top: 10px;
  border-top: 1px solid var(--gc-border);
}

.header-sidebar-color-dark .gc-main-sidebar-header,
.header-sidebar-color-dark .gc-main-sidebar-header.gc-active {
  background: var(--gc-dark);
  border-right: 1px solid var(--gc-dark-zero);
}

.header-sidebar-color-dark .gc-header-mobile.after-sidebar-header {
  background: var(--gc-dark);
}

.header-sidebar-color-dark .header-text-logo,
.header-sidebar-color-dark .gc-main-sidebar-header .primary-menu>li>a,
.header-sidebar-color-dark .gc-main-sidebar-header .submenu>li>a,
.header-sidebar-color-dark .sliding-menu .sliding-menu-inner li a,
.header-sidebar-color-dark .sliding-menu li .sliding-menu__nav,
.header-sidebar-color-dark .gc-main-sidebar-header .gc-svg-icon {
  color: var(--gc-light);
}

.header-sidebar-color-dark .header-text-logo:hover,
.header-sidebar-color-dark .gc-main-sidebar-header .primary-menu>li>a:hover,
.header-sidebar-color-dark .gc-main-sidebar-header .primary-menu>li.gc-active>a,
.header-sidebar-color-dark .gc-main-sidebar-header .submenu>li>a:hover,
.header-sidebar-color-dark .gc-main-sidebar-header .submenu>li.gc-active>a,
.header-sidebar-color-dark .sliding-menu .sliding-menu-inner li a:hover,
.header-sidebar-color-dark .sliding-menu li .sliding-menu__nav:hover {
  color: var(--gc-primary);
}

.header-sidebar-color-dark .gc-main-sidebar-header .gc-svg-icon {
  fill: var(--gc-light);
}

.header-sidebar-color-dark .gc-main-sidebar-inner.second-menu {
  border-color: var(--gc-black-border);
}

.header-sidebar-color-dark .gc-shop-popup-notices:not(.active) {
  right: 0;
}

.gc-main-sidebar-header a.account-page-link {
  display: inline-flex;
}

@media (min-width: 992px) {

  .has-header-sidebar .gc-mobile-header-spacer,
  .has-header-sidebar .gc-mobile-header,
  .has-header-sidebar .gc-mobile-menu-close-trigger {
    display: none;
  }
}

@media (max-width: 992px) {
  .gc-landscape .gc-main-sidebar-header.gc-active {
    max-width: 100%;
    flex-direction: row;
  }

  .gc-landscape .gc-mobile-header-bottom {
    padding-left: 40px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }

  .gc-landscape .gc-main-sidebar-inner {
    max-height: calc(100vh - 140px);
  }

  .gc-landscape .gc-main-sidebar-header .header-top-buttons {
    justify-content: flex-end;
    margin-top: 0;
  }

  .gc-landscape .gc-main-sidebar-header .header-top-buttons>div+div {
    margin-left: 20px;
  }

  .gc-landscape .gc-main-sidebar-header .autocomplete-suggestions {
    max-width: 100% !important;
  }

  .gc-landscape .gc-main-sidebar-header .gc-mobile-header-actions {
    margin-bottom: 20px;
  }

  .nt-gc-inner-container {
    padding: 20px 5px;
  }

  .has-header-sidebar .site-content-inner {
    width: 100%;
  }

  .gc-main-sidebar-header {
    opacity: 0;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
  }

  .has-header-sidebar .site-content {
    margin-left: 0;
    width: 100%;
  }

  .has-header-sidebar.header-sidebar-position-right .site-content {
    margin: 0;
  }

  .has-header-sidebar .gc-popup-search-panel {
    max-width: 100%;
    left: 0;
  }
}

@media (max-width: 768px) {
  .admin-bar .gc-mobile-header {
    top: 46px;
  }

  .admin-bar .gc-main-sidebar-header.gc-active {
    top: 46px;
    max-height: calc(100% - 46px);
  }

  .admin-bar.scroll-start .gc-main-sidebar-header.gc-active {
    top: 0;
    max-height: calc(100%);
  }
}

@media (max-width: 600px) {
  .gc-main-sidebar-header.gc-active {
    max-width: 100%;
    padding: 20px 30px 30px;
  }

  .site-content:before {
    content: none;
    display: none;
  }

  .admin-bar .gc-mobile-header {
    top: 46px;
  }

  .admin-bar.scroll-start .gc-mobile-header {
    top: 0;
  }
}

.gc-sticky-cart-toggle {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 103;
  background: var(--gc-dark);
  color: var(--gc-light);
  //border: 1px solid rgb(0 0 0 / 0%);
  transition: 0.5s;
  right: 25px;
  bottom: 75px;
  width: 50px;
  height: 50px;
  border-radius: 50px;
}

.bottom-popup-cart-active .gc-sticky-cart-toggle {
  bottom: 135px;
}

.gc-sticky-cart-toggle.active {
  background: var(--gc-success);
}

.gc-sticky-cart-toggle svg {
  fill: var(--gc-light);
}

#gc-cart-fly {
  position: absolute;
  width: 50px;
  opacity: 1;
  z-index: 9999999;
  font-size: 20px;
  line-height: 3;
  text-align: center;
  border-radius: 60px;
  overflow: hidden;
}

.woocommerce.gc-product:hover .gc-swatches-hover {
  opacity: 1;
}

.nt-shop-page .gc-products-wrapper .slick-slide {
  display: block;
  float: none;
}

.gc-products-wrapper .slick-slide.product-category>a {
  display: block;
}

.gc-products-wrapper .slick-slide.product-category .gc-loop-category-title {
  margin-top: 10px;
  padding-right: 30px;
  position: relative;
}

.gc-products-wrapper .slick-slide.product-category .gc-loop-category-title .cat-count {
  position: absolute;
  right: 0;
  top: 4px;
  font-size: 14px;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gc-dark);
  border-radius: 30px;
  color: var(--gc-light);
}

.shop-loop-mode-subcategories .gc-header-default .gc-header-bottom-bar,
.shop-loop-mode-subcategories .nt-shop-page.loop-mode-subcategories .gc-before-loop.gc-shop-filter-top-area {
  display: none;
}

.shop-loop-mode-subcategories .nt-shop-page.loop-mode-subcategories .gc-products.row {
  margin-bottom: 0;
}

.woocommerce .product .grouped-list-item .quantity .qty,
.woocommerce .quantity .qty,
.quantity input {
  max-width: calc(100% - 60px);
}

body:not(.scroll-start) .gc-load-more {
  display: none;
}

.gc-thumb-wrapper .gc-loop-video-wrapper {
  padding-bottom: 100%;
}

.gc-thumb-wrapper .gc-loop-video-wrapper video {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gc-compare-items {
  overflow-x: auto;
  border: 3px solid var(--gc-border);
  padding: 20px;
  min-height: calc(50vh - 10px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.gc-compare-items .gc-empty-content,
.gc-compare-items.gc-empty-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  align-content: center;
  gap: 10px;
}

.gc-empty-content svg {
  max-width: 60px;
  max-height: 60px;
  float: left;
  margin-right: 20px;
  fill: var(--gc-light);
  opacity: 1;
  margin-bottom: 10px;
  padding: 10px;
  background: var(--gc-purple-dark);
  border-radius: 100%;
}

.gc-empty-content .gc-btn-small {
  color: var(--gc-light);
  border-color: var(--gc-dark);
  background-color: var(--gc-purple);
  padding: 10px 30px;
  border-radius: 5px;
  line-height: 1;
}

.gc-compare-items>table {
  border-spacing: 0;
  border-collapse: collapse;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
}

.gc-compare-items>table>thead>tr>th {
  color: var(--gc-dark);
  border-collapse: collapse;
  padding: 15px 15px 30px 15px;
  margin: 0;
  width: auto;
  min-width: 200px;
  max-width: 300px;
  vertical-align: middle;
  font-weight: 700;
  text-align: left;
  background-color: var(--gc-gray-soft);
  border: none;
  border-image-width: 0;
  position: relative;
}

.gc-compare-items>table>thead>tr>th .name {
  text-transform: uppercase;
  display: block;
  max-width: calc(100% - 30px);
}

.gc-compare-items>table>tbody {
  border-spacing: 0;
  border-collapse: collapse;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  padding: 0;
  z-index: 6;
}

.gc-compare-items>table>tbody>tr>td {
  position: relative;
  border-collapse: collapse;
  background-color: var(--gc-light);
  padding: 15px 0 15px 15px;
  vertical-align: top;
  text-align: left;
  width: auto;
  min-width: 200px;
  max-width: 300px;
  color: var(--gc-dark);
  font-weight: 400;
  border: none;
  border-image-width: 0;
}

.gc-compare-items>table>thead>tr>th:first-child,
.gc-compare-items>table>tbody>tr>td:first-child {
  padding-left: 15px;
}

.gc-compare-items>table>tbody>tr {
  border: none;
  margin: 0;
  padding: 0;
}

.gc-compare-items>table>tbody>tr:nth-child(2n)>td {
  background-color: var(--gc-gray-soft);
}

.gc-compare-items .add_to_cart_inline {
  margin: 0;
  line-height: 1;
}

.gc-compare-items .add_to_cart_inline {
  margin: 0;
  line-height: 1;
  position: absolute;
  bottom: 7px;
}

.gc-compare-items .gc-compare-del-icon {
  position: absolute;
  top: 18px;
  right: 17px;
  line-height: 1;
}

.gc-compare-items td ul {
  padding-left: 17px;
}

.gc-compare-items .gc-empty-content,
.gc-compare-items.gc-empty-content {
  text-align: center;
}

.gc-compare-items[data-count="0"] table,
.gc-compare-items:not([data-count="0"]) .gc-empty-content {
  display: none;
}

.gc-compare-items .gc-empty-content svg,
.gc-compare-items.gc-empty-content svg {
  float: none;
  margin-right: 0;
  margin-bottom: 10px;
}

.gc-compare-items table thead tr th.th-placeholder,
.gc-compare-items table tbody tr td.td-placeholder {
  text-align: center;
}

.gc-compare-items table thead tr th.th-placeholder:before,
.gc-compare-items table tbody tr td.td-placeholder:before {
  content: "";
  display: inline-block;
  width: 60px;
  height: 8px;
  background-color: #eee;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
}

.gc-compare-popup-list {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9999;
  background: var(--gc-light);
  padding: 80px 20px 20px 20px;
  overflow: auto;
  display: none;
  opacity: 0;
}

.gc-compare-popup-list.loading,
.gc-compare-popup-list.loaded {
  display: flex;
  opacity: 1;
}

.gc-compare-popup-list.loading {
  align-items: center;
  justify-content: center;
}

.gc-compare-popup-list-inner {
  width: 100%;
  display: flex;
}

.gc-compare-popup-list.loading .gc-compare-popup-list-inner,
.gc-compare-popup-list.loading .gc-panel-close-button {
  display: none;
}

.gc-compare-popup-list.loaded .gc-panel-close-button {
  position: absolute;
  top: 35px;
  right: 20px;
}

.gc-compare-popup-list .gc-compare-items.no-product {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.gc-yith-compare-btn.gc-product-button {
  position: relative;
  overflow: hidden;
}

.gc-yith-compare-btn.gc-product-button .compare {
  position: absolute;
  font-size: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

@-webkit-keyframes rotating {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotating {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.gc-compare-popup-list.loading .svgloading {
  max-width: 50px;
  max-height: 50px;
  -webkit-animation: rotating 2s linear infinite;
  -moz-animation: rotating 2s linear infinite;
  -ms-animation: rotating 2s linear infinite;
  -o-animation: rotating 2s linear infinite;
  animation: rotating 2s linear infinite;
}

.gc-compare-popup-list.loaded .svgloading {
  display: none;
}

.gc-checkout-review-order-table span.product-img {
  max-width: 50px;
}

.gc-quickshop-wrapper .gc-quickshop-buttons-wrapper .gc-btn a {
  color: var(--gc-light);
}

.gc-summary-item.gc-product-after-cart {
  display: flex;
  gap: 10px;
}

.gc-product-summary .gc-summary-item.gc-product-after-cart>div {
  margin: 0;
  flex: 1;
  position: relative;
}

.gc-slider-thumbs-left .has-arrow .swiper-button-next,
.gc-slider-thumbs-left .has-arrow .swiper-button-prev,
.gc-slider-thumbs-left .has-arrow .swiper-button-next:hover,
.gc-slider-thumbs-left .has-arrow .swiper-button-prev:hover,
.gc-slider-thumbs-right .has-arrow .swiper-button-next,
.gc-slider-thumbs-right .has-arrow .swiper-button-prev,
.gc-slider-thumbs-right .has-arrow .swiper-button-next:hover,
.gc-slider-thumbs-right .has-arrow .swiper-button-prev:hover {
  top: auto;
  left: auto;
  right: auto;
  bottom: 0;
  position: relative;
  opacity: 1;
  transform: none;
  margin: 0;
}

.gc-slider-thumbs-left .gc-thums-arrows,
.gc-slider-thumbs-right .gc-thums-arrows {
  display: flex;
}

.gc-swiper-slider-wrapper .gc-thums-arrows .swiper-button-prev:after,
.gc-swiper-slider-wrapper .gc-thums-arrows .swiper-button-next:after {
  font-size: 16px;
  width: 30px;
  height: 30px;
}

@media (min-width: 769px) {

  .gc-slider-thumbs-left .gc-thums-arrows>div,
  .gc-slider-thumbs-right .gc-thums-arrows>div {
    flex: 1;
  }

  .gc-slider-thumbs-left .gc-thums-arrows .swiper-button-next,
  .gc-slider-thumbs-left:hover .gc-thums-arrows .swiper-button-next,
  .gc-slider-thumbs-right .gc-thums-arrows .swiper-button-next,
  .gc-slider-thumbs-right:hover .gc-thums-arrows .swiper-button-next {
    transform: rotate(90deg);
  }

  .gc-slider-thumbs-left .gc-thums-arrows .swiper-button-prev,
  .gc-slider-thumbs-left:hover .gc-thums-arrows .swiper-button-prev,
  .gc-slider-thumbs-right .gc-thums-arrows .swiper-button-prev,
  .gc-slider-thumbs-right:hover .gc-thums-arrows .swiper-button-prev {
    transform: rotate(90deg);
  }
}

@media (max-width: 768px) {

  .gc-slider-thumbs-left .gc-thums-arrows,
  .gc-slider-thumbs-right .gc-thums-arrows {
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }

  .gc-slider-thumbs-left .gc-thums-arrows .swiper-button-next,
  .gc-slider-thumbs-left .gc-thums-arrows .swiper-button-prev {
    height: 30px;
  }
}

.gc-features-item {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 20px;

  @media (max-width: 1400px) {}

  @media (max-width: 1024px) {
    //justify-content: flex-start;
    margin-bottom: 25px;
  }

  @media (max-width: 767px) {}
}

.gc-features-icon {
  color: var(--gc-dark);
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
}

.gc-features-icon svg {
  max-width: 40px;
  max-height: 40px;
  fill: var(--gc-dark);
}

.features-title {
  margin: 0 0 5px;
}

.features-desc {
  font-size: 13px;
}

@media (min-width: 1024px) {

  .gc-container.container,
  .gc-container-xl.container-xl {
    max-width: 1180px;
  }
}

@media (min-width: 1200px) {

  .gc-container.container,
  .gc-container-xl.container-xl {
    max-width: 1580px;
  }
}

.header-spacer,
.gc-header-top-menu-area>ul>li.menu-item {
  height: 80px;
}

.gc-header-top-right .gc-header-default-inner>div:not(:first-child) {
  margin-left: 15px;
}

.gc-header-top-left .gc-header-default-inner>div:not(:last-child) {
  margin-right: 15px;
}

.gc-header-default .top-action-btn {
  margin-left: 15px;
}

.avatar {
  &.avatar-sm {
    width: 2.3rem;
    height: 2.3rem;
    line-height: 1.75rem;
    font-size: 0.65rem;
    object-fit: cover;
  }

  &.avatar-rounded {
    border-radius: 50%;
  }
}

.gc-header-mobile {
  max-width: 530px;
}

.gc-header-mobile-sidebar {
  min-width: 80px;
}

.nt-logo img {
  max-width: 70px;
}

.gc-header-mobile-sidebar-logo .nt-logo img {
  max-width: 80px;
}

body .gc-woocommerce-pagination ul {
  justify-content: center;
}

/* swiper banner*/
.gc-main-slider {
  height: 69vh;

  .swiper-button-prev,
  .swiper-button-next {
    top: 50%;
  }
}

.e-con {
  --display: flex;
  --container-max-width: 1580px;
  --container-default-padding-block-start: 0px;
  --container-default-padding-inline-end: 10px;
  --container-default-padding-block-end: 0px;
  --container-default-padding-inline-start: 10px;
  --border-radius: 0;
  --border-block-start-width: 0px;
  --border-inline-end-width: 0px;
  --border-block-end-width: 0px;
  --border-inline-start-width: 0px;
  --border-style: initial;
  --border-color: initial;
  --container-widget-height: initial;
  --container-widget-flex-grow: 0;
  --container-widget-align-self: initial;
  --content-width: min(100%, var(--container-max-width, 1140px));
  --width: 100%;
  --min-height: initial;
  --height: auto;
  --text-align: initial;
  --margin-block-start: 0px;
  --margin-inline-end: 0px;
  --margin-block-end: 0px;
  --margin-inline-start: 0px;
  --padding-block-start: 30px;
  --padding-inline-end: 30px;
  --padding-block-end: var(--container-default-padding-block-end, 10px);
  --padding-inline-start: var(--container-default-padding-inline-start, 10px);
  --position: relative;
  --z-index: revert;
  --overflow: visible;
  --gap: var(--widgets-spacing, 20px);
  --overlay-mix-blend-mode: initial;
  --overlay-opacity: 1;
  --overlay-transition: 0.3s;
  --e-con-grid-template-columns: repeat(3, 1fr);
  --e-con-grid-template-rows: repeat(2, 1fr);
  position: var(--position);
  width: var(--width);
  min-width: 0;
  min-height: var(--min-height);
  height: var(--height);
  border-radius: var(--border-radius);
  margin-block-start: var(--bc-margin-block-start, var(--margin-block-start));
  margin-inline-end: var(--bc-margin-inline-end, var(--margin-inline-end));
  margin-block-end: var(--bc-margin-block-end, var(--margin-block-end));
  margin-inline-start: var(--bc-margin-inline-start,
      var(--margin-inline-start));
  padding-inline-start: var(--bc-padding-inline-start,
      var(--padding-inline-start));
  padding-inline-end: var(--bc-padding-inline-end, var(--padding-inline-end));
  z-index: var(--z-index);
  overflow: var(--overflow);
  transition: background var(--background-transition, 0.3s),
    border var(--border-transition, 0.3s),
    box-shadow var(--border-transition, 0.3s),
    transform var(--e-con-transform-transition-duration, 0.4s);
}

.e-con>.e-con-inner {
  gap: var(--gap);
  width: 100%;
  max-width: var(--content-width);
  margin: 0 auto;
  padding-inline-start: 0;
  padding-inline-end: 0;
  height: 100%;
}

.e-con,
.e-con>.e-con-inner {
  display: var(--display);
}

.e-con-full,
.e-con>.e-con-inner {
  text-align: var(--text-align);
  padding-block-start: var(--bc-padding-block-start,
      var(--padding-block-start));
  padding-block-end: var(--bc-padding-block-end, var(--padding-block-end));
}

.elementor-widget-gc-woo-banner {
  position: relative;
  flex-direction: var(--flex-direction);
  flex-wrap: var(--flex-wrap);
  justify-content: var(--justify-content);
  align-items: var(--align-items);
  align-content: var(--align-content);
  gap: var(--gap);
}

@media (min-width: 768px) {

  .footer-services-width,
  .footer-bottom-inner {
    --content-width: 1140px;
  }
}

.footer-services-width-parent {
  overflow: hidden;
}

.footer-services-width {
  padding-bottom: 40px;
  padding-top: 40px;
  margin-bottom: 40px;
  margin-top: 40px;
  position: relative;

  @media (max-width: 1400px) {}

  @media (max-width: 1024px) {
    padding-bottom: 15px;
  }

  @media (max-width: 767px) {}

  &:before,
  &:after {
    content: "";
    position: absolute;
    background: #eaebed;
    height: 1px;
    width: 400%;
    transform: translateX(-50%);
  }

  &:before {
    top: 0;
    left: 0;
  }

  &:after {
    bottom: 0;
    left: 0;
  }
}

.social-icons-wrapper {
  --icon-size: 15px;
  --icon-padding: 5px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  column-gap: 5px;

  .social-icon {
    background-color: var(--gc-dark);
    border-radius: 5px 5px 5px 5px;
    font-size: var(--icon-size, 25px);
    line-height: var(--icon-size, 25px);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      color: var(--gc-light);
      width: 1em;
      height: 1em;
      position: relative;
      display: block;
    }
  }
}

.foot-menu-list {
  ul {
    padding: 0;

    li {
      list-style-type: none;

      a {
        color: #7a7a7a;
      }
    }
  }
}

.transform-type-translate {
  text-transform: uppercase;
}

.footer-bottom {
  background: #000;
  padding: 15px 0;
  margin-top: 30px;

  .footer-bottom-inner {
    max-width: var(--content-width);
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
  }

  .footer-bottom-menu {
    @media (max-width: 991.98px) {
      display: flex;
      justify-content: center;
    }

    ul {
      padding: 0px;
      display: flex;
      align-items: center;

      li {
        list-style-type: none;
        color: white;
        text-transform: uppercase;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }
}

.gc-main-slider {
  &.gc-swiper-theme-style {

    .swiper-button-prev,
    .swiper-button-next {
      color: white;
    }
  }
}

.gc-tab-header {
  border-style: solid;
  border-width: 0px 0px 0px 0px;
  margin-bottom: 20px;
  padding-bottom: 10px;

  @media (max-width: 991.98px) {
    flex-wrap: wrap;
  }

  .gc-tab-title {
    margin: 0;
    font-size: 30px;
    font-weight: 700;
  }

  .gc-tab-menu {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;

    @media (max-width: 991.98px) {
      flex-basis: 100%;
    }

    &.nav-tabs {
      border: none;
    }

    a {
      display: flex;
      align-items: center;
      gap: 10px;
      line-height: 1;
      color: var(--gc-purple);
      padding: 5px 5px;
      min-height: 30px;
      border: 0px solid var(--gc-border);
      border-radius: 0px;

      &.nav-link {
        margin-bottom: 0px;
        border: none;

        &.active {
          background: var(--gc-purple-bg);
        }
      }
    }
  }

  .gc-btn {
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

.gc-shop-filter-top-area {
  .gc-block-left {
    color: #7a7a7a;
  }
}

body:not(.shop-layout-fixed-sidebar) {
  .nt-sidebar {
    .gc-close-sidebar {
      @media (min-width: 1024px) {
        display: none;
      }
    }
  }

  .gc-before-loop {
    .gc-block-right {
      align-items: center;

      >div.gc-open-fixed-sidebar {
        @media (min-width: 1024px) {
          display: none;
        }

        svg {
          max-width: 20px;
          max-height: 20px;
        }
      }

      &>div {
        &+div {
          margin-left: 2px;
          padding-left: 20px;
          position: relative;
        }
      }
    }
  }
}

.gc-filter-action {
  display: flex;
  align-items: center;

  li {
    list-style-type: none;

    &:not(:last-child) {
      margin-right: 10px;
    }

    a,
    svg {
      display: flex;
      align-items: center;
      justify-content: center;
      fill: var(--gc-gray-dark);
      color: var(--gc-gray-dark);
    }
  }

  svg {
    max-width: 20px;
    max-height: 20px;
  }
}

.gc-before-loop {
  margin-bottom: 30px;
}

.nt-sidebar:not(.gc-blog-sidebar) {
  @media (max-width: 1024px) {
    position: fixed;
    z-index: 105;
    left: 0;
    top: 0;
    height: 100%;
    background: var(--gc-light);
    box-shadow: 0px 12px 24px 0px rgba(120, 120, 120, 0.3);
    padding: 50px 30px;
    width: 100%;
    max-width: 400px;
    opacity: 0;
    overflow: hidden;
    transform: translateX(-100%);
    will-change: transform;
    transition: opacity 0.25s ease, transform 0.25s ease;

    &.active {
      opacity: 1;
      transform: translateX(0%);
    }
  }

  .gc-close-sidebar {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
  }
}

.nt-sidebar-inner-wrapper {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;

  .accordion {
    .accordion-item {
      background: transparent;
      border: none;
      margin-bottom: 15px;

      .accordion-header {
        .accordion-button {
          padding: 5px;
          color: var(--gc-black);
          line-height: 1.8;
          font-size: 16px;
          padding: 0px;
          font-weight: 500;
          display: flex;
          justify-content: space-between;

          &:focus {
            box-shadow: none;
          }

          &:not(.collapsed) {
            background: none;

            .fa-plus {
              display: none;
            }
          }

          &.collapsed {
            .fa-minus {
              display: none;
            }

            .fa-plus {
              display: block;
            }
          }

          .fa-solid {
            width: 28px;
            height: 28px;
            background: var(--gc-bg-soft);
            border-radius: 50%;
            position: relative;

            &:before {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            }
          }

          &:after {
            font-family: "Font Awesome 6 Free";
            content: "\f067";
            background: transparent;
            width: 28px;
            height: 28px;
            font-size: 12px;
            background: var(--gc-bg-soft);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            display: none;
          }

          &:not(.collapsed) {
            box-shadow: none;

            &:after {
              content: "\f068";
            }
          }
        }
      }
    }

    .accordion-collapse {
      .accordion-body {
        padding: 0;
      }
    }

    .gc-swatches-widget-color-item {
      position: relative;
      margin-right: 12px;
      width: 12px;
      height: 12px;
      border-radius: 100%;
      display: inline-block;
    }

    ul {
      margin: 0;
      padding: 0;
      text-transform: capitalize;
      list-style-type: none;

      li {
        margin: 0px;
        position: relative;
        line-height: 1.6;
        font-weight: 400;
        padding: 0;
        display: flex;
        align-items: center;
      }
    }
  }
}

.shop-layout-left-sidebar .nt-sidebar-inner:not(.gc-is-sticky) {
  padding-right: 30px;
}

.nt-sidebar-inner-widget.gc-widget-hide {
  margin: 0 0 15px 0 !important;
}

.nt-sidebar-inner-widget h5 {
  text-transform: capitalize;
  margin: 0 0 0;
  color: var(--gc-black);
  font-style: normal;
  line-height: 1.8;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pb-100 {
  padding-bottom: 100px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-30 {
  padding-top: 30px;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

ul.product-categories {
  margin: 0 0 40px;
  padding: 0;
  text-transform: capitalize;
  list-style-type: none;
}

ul.product-categories li {
  margin: 0;
  position: relative;
  line-height: 1.6;
  font-weight: 400;

  &+li {
    margin-top: 7px;
  }

  span {
    position: absolute;
    font-size: 14px;
    width: 25px;
    right: 0;
    top: -4px;
    left: 95%;
    padding: 0;
    text-align: center;
    color: var(--gc-black-soft);
    line-height: 2;
    left: 92%;
  }
}

.position-relative {
  position: relative;
}

// .swiper-container {
//     width: 600px;
//     height: 400px;
//     margin: 20px auto;
//   }

.swiper-thumb {
  width: 600px;
  height: 100px;
  margin: 10px auto;
}

.swiper-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.zoom-trigger {
  cursor: zoom-in;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.gallery-col {
  .zoom-trigger {
    z-index: 2;
  }

  .swiper-container {
    width: auto;
    height: auto;
    z-index: 1;

    img {
      width: 100%;
    }

    .swiper-button-next,
    .swiper-button-prev {
      &:before {
        display: none;
      }

      &:after {
        font-size: 13px;
        color: var(--gc-purple);
      }
    }
  }
}

.pid-description {
  margin-top: 30px;
  position: relative;
  display: block;
  width: 100%;
  height: 100%;

  table.shop_attributes {
    width: 100%;

    th {
      width: 50%;
      text-transform: capitalize;
      font-weight: 500;
    }

    td,
    th {
      font-size: 0.9rem;
      color: var(--gc-black);
    }
  }

  .accordion {
    .accordion-item {
      background-color: var(--gc-light);
      border: 1px solid var(--gc-border);

      &:not(:first-of-type) {
        border-top: none;
      }

      .accordion-header {
        .accordion-button {
          padding: 12px 20px;
          color: var(--gc-black);
          line-height: 1.6;
          font-size: 14.6px;
          font-weight: 500;

          &:focus {
            box-shadow: none;
          }

          &:not(.collapsed) {
            background: none;
          }

          &:after {
            font-family: "Font Awesome 6 Free";
            content: "\f078";
            width: 28px;
            height: 28px;
            font-size: 12px;
            background: transparent;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          &:not(.collapsed) {
            box-shadow: none;

            &:after {
              content: "\f078";
            }
          }
        }
      }
    }

    .accordion-collapse {
      .accordion-body {
        padding: 1rem 1.25rem;

        p {
          color: var(--e-global-color-text);
        }
      }
    }

    .gc-swatches-widget-color-item {
      position: relative;
      margin-right: 12px;
      width: 12px;
      height: 12px;
      border-radius: 100%;
      display: inline-block;
    }

    ul {
      margin: 0;
      padding: 0;
      text-transform: capitalize;
      list-style-type: none;

      li {
        margin: 0px;
        position: relative;
        line-height: 1.6;
        font-weight: 400;
        padding: 0;
        display: flex;
        align-items: center;
      }
    }
  }

  div.cr-qna-search-block {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 30px;

    .cr-input-text,
    .cr-ajax-search input[type="text"] {
      border: 1px solid var(--gc-border) !important;
      border-radius: 0;
      background-color: var(--gc-light);
      color: var(--gc-black);
      background-image: none;
      background-repeat: no-repeat;
      background-position: inherit;
      background-size: inherit;
      padding: 3px 7px;
      padding-left: 40px;
      padding-right: 20px;
      width: 100%;
      height: 49px;
      margin: 0;
      font-size: inherit;
      box-shadow: none;
      outline: none;
      border-radius: 5px;
    }

    .cr-ajax-qna-search {
      position: relative;
      flex-grow: 1;

      .cr-qna-search-icon,
      .cr-qna-search-icon {
        image-rendering: var(--gc-success) !important;
        fill: var(--gc-success) !important;
        fill: #18b394;
        display: inline-block;
        position: absolute;
        width: 1.1em;
        height: 1.1em;
        left: 9px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .cr-clear-input {
      position: absolute;
      cursor: pointer;
      margin-left: -1.7em;
      margin-top: -8px;
      height: 16px;
      top: 50%;
      right: 8px;

      svg {
        display: block;
      }
    }

    button.cr-qna-ask-button {
      font-size: 14px;
      font-weight: inherit;
      background: var(--gc-black);
      border: 0 solid;
      border-radius: 0;
      height: 49px;
      color: #fff;
      cursor: pointer;
      display: inline-block;
      outline-style: none;
      margin-top: 0;
      margin-bottom: 0;
      margin-left: 10px;
      margin-right: 0;
      min-height: 38px;
      padding: 5px 20px;
      border-radius: 5px;
    }
  }
}

.home-slider {
  .gc-thumb-wrapper {
    img {
      // max-height:392px;
      // object-fit: cover;
      // @media(max-width:640px){
      //     max-height:226px;
      // }
      // @media(max-width:480px){
      //     max-height:263px;
      // }
      // @media(max-width:320px){
      //     max-height:384px;
      // }

      /*
                320: {
                  slidesPerView: 2,
                  spaceBetween: 15,
                },
                480: {
                  slidesPerView: 3,
                  spaceBetween: 15,
                },
                640: {
                  slidesPerView: 4,
                  spaceBetween: 20,
                },
                960: {
                  slidesPerView: 5,
                  spaceBetween: 20,
                },
                */
    }
  }
}

.cart-item-scroll {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 15px;
  @extend %scroll-bar;
}

%scroll-bar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 12px;
  }

  /* Scrollbar thumb */
  &::-webkit-scrollbar-thumb {
    background-color: var(--gc-purple);
    border-radius: 10px;
    border: 3px solid transparent;
  }

  /* Scrollbar track piece */
  &::-webkit-scrollbar-track {
    background: lightgrey;
    border-radius: 10px;
    //border: 1px solid grey;
  }

  &::-webkit-scrollbar-corner {
    background: grey;
  }
}

.pid-gallery {

  .swiper-button-prev,
  .swiper-button-next {
    top: 50%;
    z-index: 999;
  }
}

.gc-cart-form {
  .cart-scroll {
    max-height: 600px;
    overflow-y: auto;
    @extend %scroll-bar;
  }

  .gc-cart-item {
    // padding-top: 15px;
    padding: 15px;
    border-top: 1px solid var(--gc-border);
    margin-left: 0;
    margin-right: 0;
  }

  .gc-cart-item+.gc-cart-item {
    margin-top: 15px;
  }

  .product-remove {
    text-align: right;
    position: relative;
  }

  .remove {
    width: 30px;
    height: 30px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 30px;

    &:hover {
      svg {
        fill: var(--gc-black);
        text-decoration: none;
        opacity: 0.8;
      }
    }

    svg {
      fill: var(--gc-gray-dark);
      color: var(--gc-gray-dark);
    }
  }

  .gc-price {
    font-size: 14px;
    font-weight: 400;

    .cart-quantity {
      font-size: 13px;
      font-weight: 500;
      color: var(--gc-black);
    }
  }

  .gc-quantity-small {
    .quantity {
      max-height: 25px;
      margin-right: 0px;
    }
  }

  .quantity-button {

    &.plus,
    &.minus {
      width: 25px;
    }
  }

  .gc-actions {
    .gc-input {
      margin-right: 10px;
    }
  }

  .gc-hidden {
    position: relative;
    overflow: hidden;

    .gc-btn {
      will-change: transform;
      transition: opacity 0.25s ease, transform 0.25s ease;
      opacity: 0;
      transform: translateY(100%);

      &[aria-disabled="false"] {
        transform: translateY(0%);
        opacity: 1;
      }
    }
  }

  .gc-meta-right {
    justify-content: space-between;

    &.gc-align-center {
      @media (max-width: 576px) {
        margin-top: 10px;
      }
    }
  }

  .woocommerce-shipping-methods {
    list-style: none;
  }

  .quantity {
    width: 100%;
    height: 38px;
    max-width: 140px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 20px;
    border: 2px solid var(--gc-border);
    min-height: 46px;
    border-radius: 5px;
    padding: 0 10px;
    max-width: 120px;

    @media (max-width: 480px) {
      max-width: 100%;
      margin-right: 0;
      margin-bottom: 10px;
    }

    .quantity-button {
      input {
        border: 0;
        font-size: 12px;
        padding: 0;
        text-align: center;
        height: 100%;
        flex: 1;
        width: auto;
        max-width: calc(100% - 60px);
      }

      &.minus {
        &:before {
          content: "";
          width: 6px;
          height: 1px;
          background: var(--gc-dark);
        }
      }

      &.plus,
      &.minus {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 22px;
        flex: 0 0 22px;
        cursor: pointer;
        font-size: 14px;
        color: var(--gc-black);
        z-index: 1;
        position: relative;
      }
    }
  }

  .input-text {
    border-style: solid;
    border-radius: 5px 5px 5px 5px;
    height: 38px;
    font-size: 14px;
    border: none;
    text-align: center;
  }

  .gc-input-small {
    height: 33px;
    font-size: 13px;
    min-width: 160px;
    padding: 12px 10px;
    min-height: 45px;
    border: 2px solid var(--gc-border);
    max-width: 100%;
    margin-right: 10px;
    max-width: 160px;
  }
}

.form-login-toggle,
.form-coupon-toggle {
  background-color: var(--gc-gray-soft);
  margin-bottom: 0;
  padding: 10px 20px;
  border: 1px solid var(--gc-border);
  max-width: 450px;
  margin-bottom: 10px;
}

.woocommerce-form-login,
.woocommerce-form-coupon {

  select,
  textarea,
  input.input-text {
    display: block;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
    padding: 10px 15px;
    border-radius: 0;
    background-clip: padding-box;
    outline: 0;
    box-shadow: none;
    font-weight: 400;
    transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out,
      color 0.3s ease-in-out;
    min-height: 45px;
    border: 2px solid var(--gc-border);
    max-width: 100%;
    color: var(--gc-gray-dark);
  }

  &.login {
    max-width: 450px;
    //margin-top: 20px;
    //margin-bottom: 20px;
  }

  .form-row {
    label {
      width: 100%;
      display: block;
      font-size: 12px;
      text-transform: uppercase;
      color: var(--gc-black);
      margin-bottom: 15px;
      line-height: 1;
      font-weight: 500;
    }
  }

  .form-row {
    &.gc-coupon-row {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      align-items: stretch;
      margin: 0;

      .gc-btn {
        height: auto;
      }
    }
  }

  input#coupon_code {
    max-width: 160px;
    margin-right: 10px;
    height: 25px;

    &+.gc-btn-medium {
      height: 45px;
    }
  }
}

.box {
  transition: height 0.25s ease-in;

  &.show {
    opacity: 1;
    transform: scale(1);
    height: auto;
    display: block;
  }

  &.hide {
    opacity: 0;
    transform: scale(0.9);
    height: 0;
    display: none;
  }
}

.error {
  color: red;
  font-size: 12px;
  font-weight: 500;
  margin-top: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

/* Quick Look Modal Styles */
.quick-look-modal {
  .modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  .modal-header {
    background-color: var(--gc-purple-bg);
    border-bottom: 1px solid var(--gc-border);

    .modal-title {
      font-weight: 500;
      color: var(--gc-purple-dark);
    }
  }

  .modal-body {
    padding: 20px;
  }

  .gc-product-title {
    font-size: 24px;
    margin-bottom: 15px;
    font-weight: 500;
  }

  .gc-price {
    margin-bottom: 15px;
    font-size: 18px;

    .del {
      text-decoration: line-through;
      color: var(--gc-black-soft);
      margin-right: 5px;
    }

    .ins {
      font-weight: 500;
      color: var(--gc-purple);
    }
  }

  .product-details__short-description {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--gc-text);
  }

  .gc-select-variations-terms-title {
    font-weight: 500;
    margin-bottom: 10px;
    font-size: 16px;
  }

  .gc-selected-variations-terms {
    margin-bottom: 15px;
    font-size: 14px;
    color: var(--gc-text-soft);
  }

  .gc-variations-items {
    margin-bottom: 15px;

    .gc-small-title {
      min-width: 80px;
      font-weight: 500;
    }

    .gc-flex {
      display: flex;
      align-items: center;
    }
  }

  .gc-quantity-wrapper {
    .quantity {
      display: flex;
      align-items: center;
      max-width: 120px;
      border: 1px solid var(--gc-border);
      border-radius: 4px;
      overflow: hidden;
      height: 40px;

      .quantity-button {
        width: 30px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--gc-bg-soft);
        cursor: pointer;
        font-size: 16px;
        user-select: none;

        &:hover {
          background-color: var(--gc-purple-bg);
        }
      }

      input {
        width: 60px;
        height: 100%;
        border: none;
        text-align: center;
        font-size: 14px;
        background-color: var(--gc-light);
      }
    }
  }

  .gc-cart-form-buttons {
    margin: 20px 0;

    .gc-btn {
      padding: 12px 25px;
      font-weight: 500;
      font-size: 16px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.9;
      }
    }
  }

  .gc-product-popup-details {
    margin-top: 20px;

    .gc-estimated-delivery, .gc-product-view {
      margin-bottom: 10px;
      font-size: 14px;
      color: var(--gc-text-soft);

      svg {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }

    .gc-view-count {
      font-weight: 500;
      color: var(--gc-purple);
    }
  }
}

.auth-pages {
  padding-top: 50px;
  padding-bottom: 50px;

  h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 30px;
    line-height: 1.1;
  }

  .form-group {
    margin-bottom: 15px;

    label {
      font-size: 16px;
      line-height: 1.3;
      width: 100%;
      margin-bottom: 4px;
    }

    .form-control {
      padding: 8px 10px;
      min-height: 42px;
      border: 1px solid #eaebed;
      max-width: 100%;
      box-sizing: border-box;

      &:focus {
        box-shadow: none;
      }
    }
  }

  .btn-black {
    color: white;
    border: solid 1px #1b1b1b;
    background: #1b1b1b;
    border-radius: 5px;
    font-size: 16px;
    min-width: 160px;
    min-height: 42px;
    padding-left: 15px;
    padding-right: 15px;
    width: 100%;
    box-sizing: border-box;

    &:hover,
    &:active {
      border: solid 1px #1b1b1b;
      background: #1b1b1b;
      color: white;
    }
  }

  p {
    font-size: 16px;

    .fw-bold {
      font-weight: 500 !important;
    }
  }

  .box {
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
    max-width: 450px;
    margin: 0 auto;

    @media (max-width: 767.98px) {
      max-width: calc(100% - 20px);
    }
  }
}

// colors and sizes
.gc-variations {
  padding-top: 30px;

  .gc-variations-items {
    &+.gc-variations-items {
      margin-top: 20px;
      margin-bottom: 20px;
    }
  }

  .gc-select-variations-terms-title {
    text-transform: uppercase;
    margin-bottom: 5px;
    display: block;
    font-weight: 500;
    color: var(--gc-dark);
  }

  .gc-selected-variations-terms {
    margin-bottom: 20px;
    padding: 3px 15px;
    background: var(--gc-green);
    border-radius: 4px;
    color: var(--gc-light);
    text-transform: uppercase;
    font-size: 11px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .gc-small-title {
    margin-right: 20px;
    min-width: 45px;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: var(--gc-dark);
  }

  .gc-terms {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .gc-term {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    text-align: center;
    position: relative;

    &.gc-selected:before {
      content: "";
      height: calc(100% - 3px);
      width: calc(100% - 3px);
      border-radius: 100%;
      text-align: center;
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      opacity: 1;
      visibility: visible;
      border: 2px solid white;
      transform: translate(-50%, -50%);
    }

    &.gc-white {
      border: solid 1px var(--gc-gray) !important;
    }

    &.gc-selected.gc-white {
      border: none !important;
    }

    &.gc-selected.gc-white:before {
      border: 2px solid black;
      height: calc(100% - 0px);
      width: calc(100% - 0px);
    }
  }

  .gc-type-color .gc-term,
  .gc-product .gc-term {
    height: auto;
    width: auto;
    min-height: 18px;
    min-width: 18px;
    border-radius: 100%;
    border-color: 1px solid var(--gc-gray);
    background-color: var(--gc-light);
  }

  .gc-type-color .gc-term {
    font-size: 0;
  }

  .gc-type-button {
    .gc-term {
      font-size: 10px;
      font-weight: 600;
      border: 1px solid var(--gc-gray);
      min-width: 25px;
      min-height: 24px;
      border-radius: 4px;
      padding-left: 4px;
      padding-right: 4px;

      &+.gc-term {
        margin-left: 5px;
      }
    }
  }

  .gc-btn-reset.reset_variations {
    background: var(--gc-black);
    color: var(--gc-light);
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    min-height: 25px;
    margin-left: 10px;
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 400;
    letter-spacing: 0.5px;
    border-radius: 4px;
  }
}

.woobanner {

  .swiper-button-prev,
  .swiper-button-next {
    top: 50%;
    bottom: inherit;
    color: white;

    &:before {
      display: none;
    }

    &:after {
      font-size: 28px;
    }
  }

  a {
    height: 100%;

    .gc-woo-banner-wrapper {
      height: 100%;

      .lazyload-wrapper {
        height: 100%;
      }
    }
  }

  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    height: 300px;

    @media (max-width: 1199.98px) {
      height: 260px;
    }

    @media (max-width: 1023.98px) {
      height: 240px;
    }

    @media (max-width: 959.98px) {
      height: 220px;
    }

    @media (max-width: 719.98px) {
      height: 200px;
    }

    @media (max-width: 579.98px) {
      height: 240px;
    }

    @media (max-width: 479.98px) {
      height: auto;
    }

    .img-fluid {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
    }
  }
}

.footer-bottom-menu {
  [class*="col-"] {
    @media (max-width: 719.98px) {
      margin-bottom: 20px;
    }
  }
}

.top-action-btn {
  position: relative;
  display: inline-block;
  cursor: pointer;

  .search-box {
    position: absolute;
    top: calc(100% + 10px);
    right: -6px;
    z-index: 1000;
    background-color: #fff;
    border: 1px solid #ddd;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    column-gap: 12px;

    &:before {
      width: 10px;
      height: 10px;
      position: absolute;
      top: -6px;
      right: 10px;
      content: "";
      border: solid 1px #ddd;
      transform: rotate(45deg);
      border-bottom: none;
      border-right: none;
      background: white;
    }

    input {
      width: 200px;
      padding: 5px;

      border: 1px solid #ddd;
    }
  }
}


.privacy-policy-container {
  margin: 2rem 5%;
  font-family: 'Arial, sans-serif';
  color: #333;

  .content-wrapper {
    background-color: #fff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .privacy-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #444;
  }

  .privacy-content {
    line-height: 1.6;
    font-size: 1rem;
    color: #555;

    p {
      margin-bottom: 1rem;
    }

    a {
      color: #007bff;
      text-decoration: underline;

      &:hover {
        text-decoration: none;
      }
    }

    ul,
    ol {
      margin-left: 2rem;
      margin-bottom: 1rem;
    }

    h2,
    h3 {
      margin-top: 1.5rem;
      color: #666;
    }
  }

  .no-data-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;

    .no-data-text {
      color: #999;
      font-size: 1.5rem;
    }
  }
}

.policy-container {
  margin: 2rem;
  border: 1px solid #ccc;
  padding: 1.5rem;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  .policy-title {
    background-color: #9462dd;
    /* Purple background */
    color: #fff;
    padding: 1rem;
    text-align: center;
    border-radius: 8px 8px 0 0;
    border-bottom: 2px solid #9462dd;
  }

  .policy-content {
    padding: 1.5rem;
    line-height: 1.6;
    color: #333;

    p {
      margin-bottom: 1rem;
    }

    h2,
    h3 {
      margin-top: 2rem;
      color: #8e44ad;
    }
  }

  .no-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 1.5rem;
    color: #888;
  }
}

.gc-order-footer {
  display: flex;
  flex-direction: column;
}


@import url(_taxonomy.scss);
@import url(_tabTwo.scss);



.gc-myaccount-navigation {
  @media(max-width:899.98px) {
    min-height: inherit !important;
  }

  .tabs-logout {
    color:var(--gc-purple);
    @media(max-width:899.98px) {
      padding: 0;
    }
  }

  .MuiTab-root {
    @media(max-width:899.98px) {
      padding: 0px;
      min-height: inherit;
      min-width: inherit;
      margin-right: 15px;
    }




    &.Mui-selected {
      color: var(--gc-purple);
    }
  }

  .MuiTabs-indicator {
    background-color: var(--gc-purple);
  }



}


.address-list-container {
  @media(max-width:767.98px) {
    overflow-x: auto;
  }
}

.address-list-table {
  @media(max-width:767.98px) {
    min-width: 700px;

  }
}

.gc-myaccount-content {
  .MuiBox-root {
    @media(max-width:899.98px) {
      padding-left: 0px;
      padding-right: 0px;
    }
  }
}

.gc-myaccount-page-content {
  .gc-myaccount-page-content-inner {
    @media(max-width:899.98px) {
      padding: 15px;
    }
  }
  .MuiTabScrollButton-horizontal{
    @media(max-width:899.98px) {
      opacity:1 !important;
      margin-left:-16px;
    }
  }
}

// #root {
//   display: flex;
//   flex-flow: column;
//   height: 100vh;
// }
// .footer-outer{
//   margin-top: auto;
// }

.gc-products .swiper-button-prev:after, .gc-products .swiper-button-next:after{
  background: rgba(255, 255, 255, 0.2);
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
}
.nt-sidebar-inner-wrapper{
  .css-1v5z18m{
    max-width: 300px;
    width: 100%;
  }
  .accordion {
    background: #f5f5f5;
    padding: 30px;
    .accordion-button{
      padding: 10px 15px !important;
    }
    .accordion-body{
      padding: 0 15px !important;
    }
}
}
.cartimg {
  width: 120px;
  height: 120px;
  fill: #ddd;
}
.woocommerce-billing-fields__field-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  @media screen and (max-width:768px) {
   gap: 0;
  }
  .form-row{
    width: calc(50% - 15px);
    @media screen and (max-width:768px) {
      width: calc(100%);
    }
  }
}
.gc-btn-medium{
  margin: 0 auto;
  width: auto !important;
}
.css-heg063-MuiTabs-flexContainer{
  flex-wrap: wrap;
  gap: 20px;
}
.user-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid #eee;
}