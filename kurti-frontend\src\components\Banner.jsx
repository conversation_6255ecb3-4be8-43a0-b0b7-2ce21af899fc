import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

// import required modules
import { useGeneralSettingsQuery } from "api/util.api";
import { useNavigate } from "react-router-dom";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { isMobileOnly } from "react-device-detect";

const Banner = () => {
  const { data: settingData = {} } = useGeneralSettingsQuery();
  const navigate = useNavigate();

  const onClickBanner = (url) => {
    if (url) {
      try {
        const parsedUrl = new URL(url);
        const path = parsedUrl.pathname + parsedUrl.search + parsedUrl.hash; // Keep path, query params, and hash
        navigate(path);
      } catch (error) {
        navigate(url); // Return the original if it's not a full URL
      }
    }
  };
  return (
    <>
      <div
        className="gc-main-slider-wrapper"
        style={{ minHeight: isMobileOnly ? "0" : "291px" }}
      >
        <div
          className="gc-main-slider gc-swiper-theme-style swiper-container gc-swiper-slider"
          style={{
            height: isMobileOnly ? "15vh" : "60vh",
          }}
        >
          {settingData?.homePageBanner?.length > 0 && (
            <Swiper
              slidesPerView={1}
              loop={true}
              pagination={{
                clickable: true,
                renderBullet: (index, className) => {
                  return `<span class="${className}" style="width: ${
                    isMobileOnly ? "6px" : "12px"
                  }; height: ${isMobileOnly ? "6px" : "12px"}; border-radius: 50%;"></span>`;
                },
              }}
              navigation={true}
              modules={[Pagination, Navigation, Autoplay]}
              className="swiper-wrapper"
              autoplay={{
                delay: 2500,
                disableOnInteraction: false,
              }}
              speed={1000}
            >
              {settingData.homePageBanner.map((item, idx) => (
                <SwiperSlide
                  className="swiper-slide cursor-pointer"
                  data-swiper-autoplay="3000"
                  data-anim-in={idx % 2 === 0 ? "fadeInUp" : "slideInUp"}
                  key={idx}
                  onClick={() => onClickBanner(item?.redirectURL)}
                >
                  <div
                    className="gc-slide-inner"
                    style={{ backgroundImage: `url(${item?.image})` }}
                  ></div>
                </SwiperSlide>
              ))}
            </Swiper>
          )}
        </div>
      </div>
    </>
  );
};

export default Banner;
