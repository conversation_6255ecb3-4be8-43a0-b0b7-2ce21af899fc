const {
    models: { Coupon },
} = require('../../../../lib/models');
const { utcDateTime } = require('../../../../lib/util');
const slugify = require('slugify');

class CouponController {
    async add(req, res) {
        const {
            code,
            discountType,
            discountValue,
            maxDiscount,
            bundleDetails,
            expiry,
            maxUses,
            minCartValue,
            status,
            description,
        } = req.body;

        try {
            const existingCoupon = await Coupon.findOne({ code });
            if (existingCoupon) {
                return res.warn('error', req.__('Coupon code already exists'));
            }

            const newCoupon = new Coupon({
                code,
                discountType,
                discountValue,
                maxDiscount,
                bundleDetails,
                expiry,
                numberOfUses: 0, // Initialize numberOfUses to 0
                maxUses,
                minCartValue,
                status,
                description,
            });

            const savedCoupon = await newCoupon.save();
            return res.status(201).json(savedCoupon);
        } catch (error) {
            console.error('Error creating coupon:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async list(req, res) {
        const { status, code, cartAmount, page = 1, limit = 10, search} = req.query;

        const finalResponse = {
            items: [],
            count: 0,
            page,
            limit,
        };

        const pageNumber = parseInt(page);
        const limitNumber = parseInt(limit);
        let skip = (pageNumber - 1) * limitNumber;

        try {
            let qry = {
                expiry: { $gt: new Date() }, // Only include non-expired coupons
            };

            if (status) {
                qry.status = status;
            }

            if (code) {
                qry.code = { $regex: code, $options: 'i' }; // case-insensitive search
            }

            if (cartAmount) {
                qry.$or = [
                    { discountType: 'bundle' }, // Include all bundle coupons
                    { minCartValue: { $lte: parseFloat(cartAmount) } }, // Include coupons with minCartValue <= cartAmount
                ];
            }
            if (search) {
                // Create a case-insensitive regular expression for the search term
                const searchRegex = new RegExp(search, 'i');
                qry.$or = [
                    { code: searchRegex },
                    { discountType: searchRegex },
                ];
            }

            const couponCount = await Coupon.countDocuments(qry);

            const coupons = await Coupon.find(qry)
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limitNumber)
                .exec();

            if (coupons.length === 0) {
                return res.warn('', req.__('NO_COUPON_FOUND'));
            }

            finalResponse.items = coupons;
            finalResponse.count = couponCount;

            res.success(finalResponse);
        } catch (error) {
            console.log('Error fetching coupons:', error);
            res.warn(error);
        }
    }

    async detail(req, res) {
        const { id } = req.params;

        try {
            const coupon = await Coupon.findById(id).populate('bundleDetails.category', 'name');

            if (!coupon) {
                return res.warn('', req.__('COUPON_NOT_FOUND'));
            }

            return res.success(coupon);
        } catch (error) {
            console.log('Error fetching coupon details:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async activeToggle(req, res) {
        const { id, status } = req.params;

        try {
            const coupon = await Coupon.findById(id);

            if (!coupon) {
                return res.warn('', req.__('COUPON_NOT_FOUND'));
            }

            coupon.status = status;
            const updatedCoupon = await coupon.save();

            return res.success(updatedCoupon);
        } catch (error) {
            console.log('Error updating coupon status:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async couponValidate(req, res) {
        const { couponCode, cartTotal, productsInCart } = req.body;

        try {
            const coupon = await Coupon.findOne({ code: couponCode }).populate('bundleDetails.category', 'name');

            if (!coupon) {
                return res.notFound(null, 'Coupon not found');
            }

            // Check if the coupon is active
            if (coupon.status !== 'Active') {
                return res.badRequest(null, 'Coupon is not active');
            }

            // Check if the coupon has expired
            if (new Date() > coupon.expiry) {
                return res.badRequest(null, 'Coupon has expired');
            }

            // Check usage limit
            if (coupon.maxUses !== null && coupon.numberOfUses >= coupon.maxUses) {
                return res.badRequest(null, 'Coupon usage limit reached');
            }

            // Check minimum cart value
            if (coupon.discountType !== 'bundle' && cartTotal < coupon.minCartValue) {
                return res.badRequest(null, `Minimum cart value is ${coupon.minCartValue}`);
            }

            // Check bundle discount specifics
            if (coupon.discountType === 'bundle') {
                console.log('checking>>>>', productsInCart, coupon.bundleDetails, cartTotal);
                const categoryCount = productsInCart.filter(
                    product => product.category.toString() === coupon.bundleDetails.category._id.toString()
                ).map(product => product.quantity).reduce((a, b) => a + b, 0);
                
                if (categoryCount < coupon.bundleDetails.quantity) {
                    return res.badRequest(
                        null,
                        `At least ${coupon.bundleDetails.quantity} items of category ${coupon.bundleDetails.category.name} required`
                    );
                }
                if (categoryCount > coupon.bundleDetails.quantity) {
                    return res.badRequest(
                        null,
                        `At most ${coupon.bundleDetails.quantity} items of category ${coupon.bundleDetails.category.name} required`
                    );
                }
                return res.success({
                    isValid: true,
                    discountAmount: parseInt(cartTotal - coupon.bundleDetails.bundlePrice),
                    message: 'Coupon applied successfully',
                });
            }

           
            console.log("coupon.discountType>>>",coupon.discountType)

            // Calculate discount
            let discountAmount = 0;
            if (coupon.discountType === 'flat') {
                discountAmount = coupon.discountValue;
            } else if (coupon.discountType === 'percentage') {
                discountAmount = (cartTotal * coupon.discountValue) / 100;
                if (coupon.maxDiscount && discountAmount > coupon.maxDiscount) {
                    discountAmount = coupon.maxDiscount;
                }
            }

            return res.success({
                isValid: true,
                discountAmount: discountAmount,
                message: 'Coupon applied successfully',
            });
        } catch (error) {
            return res.serverError(null, 'Server error', error);
        }
    }
}

module.exports = new CouponController();
