const {
    models: {
        <PERSON><PERSON><PERSON><PERSON>,
        CaptionTag,
        <PERSON>loth<PERSON>ength,
        Color,
        Discount,
        Fabric,
        Sections,
        Occasion,
        Pattern,
        PaymentMode,
        Brand,
    },
} = require('../../../../lib/models');

class MasterController {
    // APIs for size
    async getSize(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await SizeType.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving size type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }
    async addSize(req, res) {
        const { name, values } = req.body;
        try {
            // Create and save the new size type to the database
            const addedData = await SizeType.create({ name, values });

            // Return success response with the added size type
            return res.success({ ...addedData._doc }, req.__('SIZE_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding size type:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateSize(req, res) {
        const { dataId } = req.params;
        const { name, values } = req.body;
        try {
            // Find the size type by its ID
            const sizeType = await SizeType.findOne({ _id: dataId, isDeleted: false });

            // If the size type is not found, return an error response
            if (!sizeType) {
                return res.warn({}, req.__('Size type not found'));
            }

            // Update the size type fields if provided
            if (name) {
                sizeType.name = name;
            }
            if (values) {
                sizeType.values = values;
            }

            // Save the updated size type to the database
            await sizeType.save();

            // Return a success response with the updated size type
            return res.success({ ...sizeType._doc }, req.__('SIZE_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating size type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for caption tag
    async getCaptionTag(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await CaptionTag.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving size type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }
    async addCaptionTag(req, res) {
        const { name, sortOrder } = req.body;
        try {
            // Create and save the new size type to the database
            const addedData = await CaptionTag.create({ name, sortOrder });
            // Return success response with the added size type
            return res.success({ ...addedData._doc }, req.__('CAPTION_TAG_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding caption tag:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }
    async updateCaptionTag(req, res) {
        const { dataId } = req.params;
        const { name, sortOrder } = req.body;

        try {
            // Find the caption tag by its ID
            const captionTag = await CaptionTag.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the caption tag is not found, return an error response
            if (!captionTag) {
                return res.notFound({}, req.__('Caption tag not found'));
            }

            // Update the caption tag fields if provided
            if (name) {
                captionTag.name = name;
            }
            if (sortOrder) {
                captionTag.sortOrder = sortOrder;
            }

            // Save the updated caption tag to the database
            await captionTag.save();

            // Return a success response with the updated caption tag
            return res.success({ ...captionTag._doc }, req.__('CAPTION_TAG_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating caption tag:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for cloth length
    async getClothLength(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await ClothLength.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving size type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }
    async addClothLength(req, res) {
        const { name, sortOrder } = req.body;
        try {
            // Create and save the new cloth length to the database
            const addedData = await ClothLength.create({ name, sortOrder });
            // Return success response with the added size type
            return res.success({ ...addedData._doc }, req.__('CAPTION_TAG_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding caption tag:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }
    async updateClothLength(req, res) {
        const { dataId } = req.params;
        const { name, sortOrder } = req.body;

        try {
            // Find the cloth length by its ID
            const clothLength = await ClothLength.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the cloth length is not found, return an error response
            if (!clothLength) {
                return res.notFound({}, req.__('Cloth length not found'));
            }

            // Update the cloth length fields if provided
            if (name) {
                clothLength.name = name;
            }
            if (sortOrder) {
                clothLength.sortOrder = sortOrder;
            }

            // Save the updated cloth length to the database
            await clothLength.save();

            // Return a success response with the updated cloth length
            return res.success({ ...clothLength._doc }, req.__('CLOTH_LENGTH_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating cloth length:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for colors
    async getColors(req, res) {
        try {
            const { dataId, name, hexCode } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
                ...(hexCode && { hexCode }), // Include hexCode in query if provided
            };

            const data = await Color.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving size type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addColor(req, res) {
        const { name, hexCode, sortOrder } = req.body;
        try {
            const addedData = await Color.create({ name, hexCode, sortOrder });

            return res.success({ ...addedData._doc }, req.__('COLOR_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding color:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateColor(req, res) {
        const { dataId } = req.params;
        const { name, hexCode, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the color by its ID
            const color = await Color.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the color is not found, return an error response
            if (!color) {
                return res.notFound({}, req.__('Color not found'));
            }

            // Update the color fields if provided
            if (name) {
                color.name = name;
            }
            if (hexCode) {
                color.hexCode = hexCode;
            }
            if (sortOrder) {
                color.sortOrder = sortOrder;
            }

            // Save the updated color to the database
            await color.save();

            // Return a success response with the updated color
            return res.success({ ...color._doc }, req.__('COLOR_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating color:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for Discount
    async getDiscountList(req, res) {
        try {
            const { dataId, name, value } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
                ...(value && { value }), // Include hexCode in query if provided
            };

            const data = await Discount.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving size type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addDiscount(req, res) {
        const { name, value, sortOrder } = req.body;
        try {
            const addedData = await Discount.create({ name, value, sortOrder });

            return res.success({ ...addedData._doc }, req.__('DISCOUNT_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding discount:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateDiscount(req, res) {
        const { dataId } = req.params;
        const { name, value, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the discount by its ID
            const discount = await Discount.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the discount is not found, return an error response
            if (!discount) {
                return res.notFound({}, req.__('Discount not found'));
            }

            // Update the discount fields if provided
            if (name) {
                discount.name = name;
            }
            if (value) {
                discount.value = value;
            }
            if (sortOrder) {
                discount.sortOrder = sortOrder;
            }

            // Save the updated discount to the database
            await discount.save();

            // Return a success response with the updated discount
            return res.success({ ...discount._doc }, req.__('DISCOUNT_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating discount:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for Fabric
    async getFabrics(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await Fabric.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving fabric type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addFabric(req, res) {
        const { name, image, sortOrder } = req.body;
        try {
            const addedData = await Fabric.create({ name, image, sortOrder });

            return res.success({ ...addedData._doc }, req.__('DISCOUNT_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding fabric:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateFabric(req, res) {
        const { dataId } = req.params;
        const { name, image, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the fabric by its ID
            const fabric = await Fabric.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the fabric is not found, return an error response
            if (!fabric) {
                return res.notFound({}, req.__('Fabric not found'));
            }

            // Update the fabric fields if provided
            if (name) {
                fabric.name = name;
            }
            if (image) {
                fabric.image = image;
            }
            if (sortOrder) {
                fabric.sortOrder = sortOrder;
            }

            // Save the updated fabric to the database
            await fabric.save();

            // Return a success response with the updated fabric
            return res.success({ ...fabric._doc }, req.__('FABRIC_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating fabric:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for Sections
    async getSections(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await Sections.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving sections type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addSections(req, res) {
        const { name, sortOrder, tag } = req.body;
        try {
            const addedData = await Sections.create({ name, sortOrder, tag });

            return res.success({ ...addedData._doc }, req.__('SECTION_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding section:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateSection(req, res) {
        const { dataId } = req.params;
        const { name, sortOrder, tag } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the section by its ID
            const section = await Sections.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the section is not found, return an error response
            if (!section) {
                return res.notFound({}, req.__('Section not found'));
            }

            // Update the section fields if provided
            if (name) {
                section.name = name;
            }
            if (sortOrder) {
                section.sortOrder = sortOrder;
            }
            if (tag) {
                section.tag = tag;
            }

            // Save the updated section to the database
            await section.save();

            // Return a success response with the updated section
            return res.success({ ...section._doc }, req.__('SECTION_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating section:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for Occasions
    async getOccasions(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await Occasion.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving Occasion type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addOccasions(req, res) {
        const { name, image, sortOrder } = req.body;
        try {
            const addedData = await Occasion.create({ name, image, sortOrder });

            return res.success({ ...addedData._doc }, req.__('DISCOUNT_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding Occasion:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateOccasion(req, res) {
        const { dataId } = req.params;
        const { name, image, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the occasion by its ID
            const occasion = await Occasion.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the occasion is not found, return an error response
            if (!occasion) {
                return res.notFound({}, req.__('Occasion not found'));
            }

            // Update the occasion fields if provided
            if (name) {
                occasion.name = name;
            }
            if (image) {
                occasion.image = image;
            }
            if (sortOrder) {
                occasion.sortOrder = sortOrder;
            }

            // Save the updated occasion to the database
            await occasion.save();

            // Return a success response with the updated occasion
            return res.success({ ...occasion._doc }, req.__('OCCASION_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating occasion:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for Pattern
    async getPatterns(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await Pattern.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving Pattern type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addPattern(req, res) {
        const { name, image, sortOrder } = req.body;
        try {
            const addedData = await Pattern.create({ name, image, sortOrder });

            return res.success({ ...addedData._doc }, req.__('PATTERN_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding Pattern:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updatePattern(req, res) {
        const { dataId } = req.params;
        const { name, image, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the pattern by its ID
            const pattern = await Pattern.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the pattern is not found, return an error response
            if (!pattern) {
                return res.notFound({}, req.__('Pattern not found'));
            }

            // Update the pattern fields if provided
            if (name) {
                pattern.name = name;
            }
            if (image) {
                pattern.image = image;
            }
            if (sortOrder) {
                pattern.sortOrder = sortOrder;
            }

            // Save the updated pattern to the database
            await pattern.save();

            // Return a success response with the updated pattern
            return res.success({ ...pattern._doc }, req.__('PATTERN_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating pattern:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for Pattern
    async getBrands(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await Brand.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving Brand type:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    async addBrand(req, res) {
        const { name, image, sortOrder } = req.body;
        try {
            const addedData = await Brand.create({ name, image, sortOrder });

            return res.success({ ...addedData._doc }, req.__('BRAND_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding Brand:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updateBrand(req, res) {
        const { dataId } = req.params;
        const { name, image, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the brand by its ID
            const brand = await Brand.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the brand is not found, return an error response
            if (!brand) {
                return res.notFound({}, req.__('Brand not found'));
            }

            // Update the brand fields if provided
            if (name) {
                brand.name = name;
            }
            if (image) {
                brand.image = image;
            }
            if (sortOrder) {
                brand.sortOrder = sortOrder;
            }

            // Save the updated brand to the database
            await brand.save();

            // Return a success response with the updated brand
            return res.success({ ...brand._doc }, req.__('BRAND_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating brand:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }

    // APIs for cloth length
    async getPaymentMode(req, res) {
        try {
            const { dataId, name } = req.query;

            const query = {
                isDeleted: false,
                isSuspended: false,
                ...(dataId && { _id: dataId }), // Include _id in query if provided
                ...(name && { name }), // Include name in query if provided
            };

            const data = await PaymentMode.find(query);

            return res.success(data);
        } catch (error) {
            console.error('Error retrieving Payment Mode:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }
    async addPaymentMode(req, res) {
        const { name, sortOrder } = req.body;
        try {
            // Create and save the new cloth length to the database
            const addedData = await PaymentMode.create({ name, sortOrder });
            // Return success response with the added size type
            return res.success({ ...addedData._doc }, req.__('PAYMENT_MODE_ADDED'));
        } catch (error) {
            // Return bad request response if an error occurs
            console.error('Error adding Payment Mode:', error);
            return res.badRequest({}, req.__(error.message));
        }
    }

    async updatePaymentMode(req, res) {
        const { dataId } = req.params;
        const { name, sortOrder } = req.body;

        try {
            // Ensure the dataId is provided
            if (!dataId) {
                return res.badRequest({}, req.__('Data ID is required'));
            }

            // Find the payment mode by its ID
            const paymentMode = await PaymentMode.findOne({ _id: dataId, isDeleted: false, isSuspended: false });

            // If the payment mode is not found, return an error response
            if (!paymentMode) {
                return res.notFound({}, req.__('Payment mode not found'));
            }

            // Update the payment mode fields if provided
            if (name) {
                paymentMode.name = name;
            }
            if (sortOrder) {
                paymentMode.sortOrder = sortOrder;
            }

            // Save the updated payment mode to the database
            await paymentMode.save();

            // Return a success response with the updated payment mode
            return res.success({ ...paymentMode._doc }, req.__('PAYMENT_MODE_UPDATED'));
        } catch (error) {
            // Return an internal server error response if an error occurs
            console.error('Error updating payment mode:', error);
            return res.warn({ error: 'Internal server error' });
        }
    }
}

module.exports = new MasterController();
