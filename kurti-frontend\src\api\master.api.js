import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals/endpoints";
import { apiClient } from "./api-client";

export const useTagsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_TAGS);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["tags"],
  });

export const useColorsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_COLORS);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["colors"],
  });

export const useBrandsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_BRANDS);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["brands"],
  });

export const useOccasionsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_OCCASIONS);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["occasions"],
  });

export const useLengthQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_LENGTH);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["length"],
  });

export const useFabricQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_FABRIC);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["fabric"],
  });

export const useSectionsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_SECTIONS);
      if (response?.success) {
        return response.data;
      }
    },
    staleTime: Infinity,
    queryKey: ["sections"],
  });
