.vieworder-page {
    padding-top: 60px;
    .bg-primary{
        background:var(--gc-purple)
    }
    .bg-primary-title{
        background:var(--gc-purple)
    }
    .progress-bar{
        background:var(--gc-purple)
    }
    .img-thumbnail{
        max-height: 150px;
    }

    // Invoice dropdown styling
    .invoice-dropdown {
        .dropdown-toggle {
            background-color: var(--gc-purple) !important;
            border-color: var(--gc-purple) !important;
            color: white !important;

            &:hover {
                background-color: var(--gc-purple) !important;
                border-color: var(--gc-purple) !important;
                color: white !important;
                opacity: 0.9;
            }

            &:focus {
                background-color: var(--gc-purple) !important;
                border-color: var(--gc-purple) !important;
                color: white !important;
                box-shadow: 0 0 0 0.2rem rgba(128, 0, 128, 0.25) !important;
            }

            &:active {
                background-color: var(--gc-purple) !important;
                border-color: var(--gc-purple) !important;
                color: white !important;
            }

            &.show {
                background-color: var(--gc-purple) !important;
                border-color: var(--gc-purple) !important;
                color: white !important;
            }

            &:disabled {
                background-color: var(--gc-purple) !important;
                border-color: var(--gc-purple) !important;
                color: white !important;
                opacity: 0.6;
            }
        }

        .dropdown-menu {
            border: 1px solid var(--gc-purple);
            box-shadow: 0 0.5rem 1rem rgba(128, 0, 128, 0.15);

            .dropdown-item {
                transition: all 0.2s ease-in-out;

                &:hover {
                    background-color: rgba(128, 0, 128, 0.1);
                    color: var(--gc-purple);
                }

                &:focus {
                    background-color: rgba(128, 0, 128, 0.1);
                    color: var(--gc-purple);
                }

                &:active {
                    background-color: rgba(128, 0, 128, 0.15);
                    color: var(--gc-purple);
                }

                &:disabled {
                    opacity: 0.6;
                    color: #6c757d;
                }
            }
        }
    }
}

.timeline {
    position: relative;
    padding: 0;
    list-style: none;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #dee2e6;
    left: 50%;
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin: 0 0 2rem 0;
}

.timeline-item::before,
.timeline-item::after {
    content: '';
    display: table;
}

.timeline-item::after {
    clear: both;
}

.timeline-item::before,
.timeline-item::after {
    content: ' ';
    display: table;
}

.timeline-item::after {
    clear: both;
}

.timeline-item .timeline-marker {
    position: absolute;
    left: 50%;
    width: 18px;
    height: 18px;
    background: #ffffff;
    border: 4px solid var(--gc-purple);
    border-radius: 50%;
    top: 0;
    transform: translateX(-50%);
}

.timeline-item .timeline-content {
    margin-left: calc(50% + 1rem);
    padding: 0.5rem 1rem;
    background: #ffffff;
    border-radius: 0.25rem;
    position: relative;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: 0;
    margin-right: calc(50% + 1rem);
}

.progress {
    height: 30px;
}

/* Invoice Dropdown Styles */
.invoice-dropdown {
    .dropdown-toggle {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
        background-color: var(--gc-purple);
        border: var(--gc-purple);

        &:active, &:focus {
            background-color: var(--gc-purple);
            border: var(--gc-purple);
            box-shadow: none;
        }

        &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &:disabled {
            transform: none;
            box-shadow: none;
        }
    }

    .dropdown-menu {
        border: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        padding: 0.5rem 0;
        min-width: 200px;

        .dropdown-item {
            padding: 0.75rem 1.25rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;

            &:hover {
                background-color: #f8f9fa;
                color: var(--gc-purple);
                transform: translateX(4px);
            }

            &:disabled {
                opacity: 0.6;
                transform: none;
                cursor: not-allowed;
            }

            .spinner-border-sm {
                width: 0.875rem;
                height: 0.875rem;
            }
        }
    }
}

/* Action buttons container */
.action-buttons-container {
    .btn {
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
    }
}

