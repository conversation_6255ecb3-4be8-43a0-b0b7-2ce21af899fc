const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');

let client = null;
let isInitialized = false;
let isReady = false;

/**
 * Initialize the WhatsApp client
 * @returns {Promise<void>}
 */
const initWhatsApp = async () => {
    if (isInitialized) {
        return;
    }

    // Create a client instance
    client = new Client({
        authStrategy: new LocalAuth(),
        puppeteer: {
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
        }
    });

    // Set up event listeners
    client.on('qr', (qr) => {
        console.log('QR RECEIVED, scan this with your WhatsApp app:');
        qrcode.generate(qr, { small: true });
    });

    client.on('ready', () => {
        console.log('WhatsApp client is ready!');
        isReady = true;
    });

    client.on('authenticated', () => {
        console.log('WhatsApp client authenticated');
    });

    client.on('auth_failure', (msg) => {
        console.error('WhatsApp authentication failed:', msg);
    });

    // Initialize the client
    await client.initialize();
    isInitialized = true;
};

/**
 * Send a message via WhatsApp
 * @param {String} phoneNumber - The phone number to send the message to (with country code)
 * @param {String} message - The message to send
 * @returns {Promise<Object>} - The result of the send operation
 */
const sendMessage = async (phoneNumber, message) => {
    if (!isInitialized) {
        await initWhatsApp();
    }

    // Wait for client to be ready
    if (!isReady) {
        await new Promise(resolve => {
            const checkReady = setInterval(() => {
                if (isReady) {
                    clearInterval(checkReady);
                    resolve();
                }
            }, 1000);
        });
    }

    try {
        // Format the phone number (remove any non-numeric characters)
        const formattedNumber = phoneNumber.replace(/[^0-9]/g, '');

        // Send the message
        const chatId = `${formattedNumber}@c.us`;
        const result = await client.sendMessage(chatId, message);
        return result;
    } catch (error) {
        console.error('Error sending WhatsApp message:', error);
        throw error;
    }
};

/**
 * Send a file via WhatsApp
 * @param {String} phoneNumber - The phone number to send the file to (with country code)
 * @param {String} filePath - The path to the file to send
 * @param {String} caption - Optional caption for the file
 * @returns {Promise<Object>} - The result of the send operation
 */
const sendFile = async (phoneNumber, filePath, caption = '') => {
    if (!isInitialized) {
        await initWhatsApp();
    }

    // Wait for client to be ready
    if (!isReady) {
        await new Promise(resolve => {
            const checkReady = setInterval(() => {
                if (isReady) {
                    clearInterval(checkReady);
                    resolve();
                }
            }, 1000);
        });
    }

    try {
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }

        // Format the phone number (remove any non-numeric characters)
        const formattedNumber = phoneNumber.replace(/[^0-9]/g, '');

        // Send the file
        const chatId = `${formattedNumber}@c.us`;
        const media = MessageMedia.fromFilePath(filePath);
        const result = await client.sendMessage(chatId, media, { caption });
        return result;
    } catch (error) {
        console.error('Error sending WhatsApp file:', error);
        throw error;
    }
};

/**
 * Send an invoice via WhatsApp
 * @param {String} phoneNumber - The phone number to send the invoice to (with country code)
 * @param {String} invoicePath - The path to the invoice PDF
 * @param {Object} order - The order object
 * @returns {Promise<Object>} - The result of the send operation
 */
const sendInvoice = async (phoneNumber, invoicePath, order) => {
    try {
        // Create a message with order details
        const message = `Thank you for your order #${order.orderId}!\n\nHere is your invoice for the amount of ₹${order.totalPrice.toFixed(2)}.\n\nThank you for shopping with us!`;

        // Send the message
        await sendMessage(phoneNumber, message);

        // Send the invoice file
        const result = await sendFile(phoneNumber, invoicePath, `Invoice #${order.orderId}`);
        return result;
    } catch (error) {
        console.error('Error sending WhatsApp invoice:', error);
        throw error;
    }
};

module.exports = {
    initWhatsApp,
    sendMessage,
    sendFile,
    sendInvoice
};
