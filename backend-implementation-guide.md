# Backend Implementation Guide for Token Exchange

This guide explains how to implement the token exchange endpoint on your backend server.

## Overview

The token exchange endpoint allows your backend to:
1. Verify a Firebase token
2. Generate a backend JWT token for the authenticated user
3. Return the backend token to the frontend

## Implementation Steps

### 1. Create a new endpoint in your backend

```javascript
// Example implementation in Express.js
const express = require('express');
const admin = require('firebase-admin');
const jwt = require('jsonwebtoken');
const router = express.Router();

// Initialize Firebase Admin SDK
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://your-project.firebaseio.com"
});

// Token exchange endpoint
router.post('/exchange-token', async (req, res) => {
  try {
    const { firebaseToken } = req.body;
    
    if (!firebaseToken) {
      return res.status(400).json({
        success: false,
        message: 'Firebase token is required'
      });
    }
    
    // Verify the Firebase token
    const decodedToken = await admin.auth().verifyIdToken(firebaseToken);
    
    // Get user information
    const uid = decodedToken.uid;
    const email = decodedToken.email;
    
    // Find or create user in your database
    let user = await User.findOne({ email });
    
    if (!user) {
      user = await User.create({
        email,
        firebaseUid: uid,
        // Add other user properties as needed
      });
    }
    
    // Generate a JWT token for your backend
    const backendToken = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        // Add other claims as needed
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // Return the backend token
    return res.status(200).json({
      success: true,
      data: {
        token: backendToken,
        user: {
          _id: user._id,
          email: user.email,
          // Add other user properties as needed
        }
      }
    });
  } catch (error) {
    console.error('Token exchange error:', error);
    return res.status(401).json({
      success: false,
      message: 'Invalid Firebase token'
    });
  }
});

module.exports = router;
```

### 2. Register the route in your main app

```javascript
const authRoutes = require('./routes/auth');
app.use('/auth', authRoutes);
```

### 3. Update your authentication middleware

Make sure your authentication middleware can validate the backend JWT tokens:

```javascript
const jwt = require('jsonwebtoken');

const authMiddleware = (req, res, next) => {
  try {
    // Get the token from the Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }
    
    // Extract the token
    const token = authHeader.split(' ')[1];
    
    // Verify the token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Add the user info to the request
    req.user = decoded;
    
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
};

module.exports = authMiddleware;
```

## Testing

You can test the token exchange endpoint using tools like Postman:

1. Get a Firebase token by authenticating a user through Firebase Auth
2. Send a POST request to `/auth/exchange-token` with the Firebase token
3. Verify that you receive a backend token in response
4. Use the backend token for subsequent API requests

## Security Considerations

- Store your JWT secret securely using environment variables
- Set appropriate expiration times for your tokens
- Consider implementing token refresh mechanisms
- Validate all incoming tokens thoroughly
