import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { useUpdateCartMutation } from "api/cart.api";
import useCartStore, { setCartItems } from "stores/cart";
import useUserStore from "stores/user";
import { toast } from "react-toastify";
import ProductColorListMolecule from "./ProductColorList.molecule";
import ProductSizeListMolecule from "./ProductSizeList.molecule";
import { SETTINGS } from "globals";
import parse from "html-react-parser";
import { Link } from "react-router-dom";
import { IconCalendar, IconSmiley } from "utils/icons";
import { generateRandomNumber, getEstimatedDeliveryDate } from "utils";

const QuickLookMolecule = ({ show, onHide, productInfo }) => {
  const [selectedSize, setSelectedSize] = useState("");
  const [selectedColor, setSelectedColor] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [ctaText, setCtaText] = useState(SETTINGS.ADD_TO_CART);

  const { mutateAsync: updateCart } = useUpdateCartMutation();
  const cartItems = useCartStore((state) => state.cartItems);
  const token = useUserStore((state) => state.userInfo.token);

  // Initialize selected size and color when product info changes
  useEffect(() => {
    if (productInfo && productInfo.stock && productInfo.stock.length > 0) {
      setSelectedSize(productInfo.stock[0].size);
    }

    if (productInfo && productInfo.attributes && productInfo.attributes.color && productInfo.attributes.color.length > 0) {
      setSelectedColor(productInfo.attributes.color[0]);
    }
  }, [productInfo]);

  const productPrices = useMemo(() => {
    if (productInfo?.stock?.length) {
      return productInfo.stock.find((item) => item.size === selectedSize);
    }
  }, [productInfo, selectedSize]);

  const productImages = useMemo(() => {
    if (productInfo?.attributes?.color?.length && selectedColor?.hexCode) {
      return productInfo.attributes.color.find(
        (item) => item.hexCode === selectedColor.hexCode
      )?.images;
    }
  }, [productInfo, selectedColor]);

  const availableSizes = useMemo(() => {
    if (productInfo?.stock?.length) {
      return productInfo.stock.map((item) => item.size);
    }
  }, [productInfo]);

  const handleSelectSize = (size) => {
    setSelectedSize(size);
  };

  const handleSelectColor = (color) => {
    setSelectedColor(color);
  };

  const handleQuantityChange = (type) => {
    if (type === "increase") {
      setQuantity(quantity + 1);
    } else if (type === "decrease" && quantity > 1) {
      setQuantity(quantity - 1);
    }
  };

  const productCheckQry = (item, product, selectedColor, selectedSize) => {
    return (
      item?._id === product?._id &&
      item?.selectedColor?.hexCode === selectedColor?.hexCode &&
      item?.selectedSize === selectedSize
    );
  };

  const isProductInCart = useMemo(
    () =>
      cartItems.some((item) =>
        productCheckQry(item, productInfo, selectedColor, selectedSize)
      ),
    [cartItems, productInfo, selectedColor, selectedSize]
  );

  const handleAddToCart = () => {
    if (!selectedSize || !selectedColor) {
      toast.error("Please select size and color");
      return;
    }

    if (isProductInCart) {
      setCartItems(cartItems.filter((item) => !productCheckQry(item, productInfo, selectedColor, selectedSize)));
      toast.success("Item removed from cart");
      return;
    }

    const itemInfo = {
      _id: productInfo._id,
      name: productInfo.name,
      mainImage: productImages?.mainImage,
      price: productPrices?.sellingPrice,
      originalPrice: productPrices?.unitPrice,
      discount: productInfo.prices.b2cPrice.discount,
      quantity,
      selectedSize,
      selectedColor,
    };

    if (token) {
      updateCart({
        data: {
          productId: productInfo._id,
          quantity,
          selectedSize,
          selectedColor,
        },
        token,
      });
    }

    setCartItems([...cartItems, itemInfo]);
    setQuantity(1);
    setCtaText(SETTINGS.ADDED_TO_CART);
    toast.success("Item added to cart");

    setTimeout(() => {
      setCtaText(SETTINGS.ADD_TO_CART);
    }, 2000);
  };

  if (!productInfo) return null;

  return (
    <Modal
      show={show}
      onHide={onHide}
      centered
      size="lg"
      className="quick-look-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title>Quick Look</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="row gc-row-summary">
          <div className="col-12 col-md-5 gallery-col">
            <img
              src={productImages?.mainImage?.url || productInfo?.mainImage}
              alt={productInfo.name}
              className="img-fluid"
            />
            <div className="mt-3 text-center">
              <Link
                to={`/product-details/${productInfo._id}`}
                className="btn btn-outline-secondary"
                onClick={onHide}
              >
                View Full Details
              </Link>
            </div>
          </div>
          <div className="col-12 col-md-7 summary-col px-md-4 mt-4 mt-md-0">
            <div className="gc-product-summary">
              <div className="gc-product-summary-inner">
                <h1 className="gc-summary-item gc-product-title text-capitalize">
                  {productInfo.name}
                </h1>
                <div className="gc-summary-item gc-price price">
                  <span className="gc-primary-color del">
                    <span className="woocommerce-Price-currencySymbol">
                      {parse(SETTINGS.CURRENCY)}
                    </span>
                    {productPrices?.unitPrice}
                  </span>{" "}
                  –
                  <span className="gc-primary-color ins">
                    <span className="woocommerce-Price-currencySymbol">
                      {parse(SETTINGS.CURRENCY)}
                    </span>
                    {productPrices?.sellingPrice}
                  </span>
                </div>

                <div className="gc-summary-item product-details__short-description text-capitalize">
                  {productInfo?.description}
                </div>

                <div className="gc-variations variations">
                  <div className="gc-select-variations-terms-title">
                    Selected Features
                  </div>
                  <div className="gc-selected-variations-terms">
                    <span className="selected-features">
                      color: {selectedColor?.name}
                    </span>
                    &nbsp;&nbsp;&nbsp;
                    <span className="selected-features">
                      size: {selectedSize}
                    </span>
                  </div>

                  {productInfo.attributes?.color?.length > 0 && (
                    <div className="gc-variations-items variations-items">
                      <div className="gc-flex gc-align-center">
                        <span className="gc-small-title">color</span>
                        <div className="gc-flex value">
                          <ProductColorListMolecule
                            colors={productInfo.attributes.color}
                            selectedColor={selectedColor}
                            onSelectColor={handleSelectColor}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {productInfo.stock?.length > 0 && (
                    <div className="gc-variations-items variations-items">
                      <div className="gc-flex gc-align-center">
                        <span className="gc-small-title">size</span>
                        <div className="gc-flex value">
                          <ProductSizeListMolecule
                            size={availableSizes || []}
                            selectedSize={selectedSize}
                            onSelect={handleSelectSize}
                            availableSizes={availableSizes}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="gc-variations-items variations-items">
                    <div className="gc-flex gc-align-center">
                      <span className="gc-small-title">quantity</span>
                      <div className="gc-flex value">
                        <div className="gc-quantity-wrapper">
                          <div className="quantity">
                            <div
                              className="quantity-button minus"
                              onClick={() => handleQuantityChange("decrease")}
                            >
                              -
                            </div>
                            <input
                              type="text"
                              className="input-text qty text"
                              value={quantity}
                              readOnly
                            />
                            <div
                              className="quantity-button plus"
                              onClick={() => handleQuantityChange("increase")}
                            >
                              +
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="gc-cart-form-buttons">
                    <div className="gc-flex">
                      <button
                        className="gc-btn gc-btn-primary gc-btn-large single_add_to_cart_button"
                        onClick={handleAddToCart}
                        style={{
                          backgroundColor: "#6c5ebc",
                          color: "white",
                          border: "none",
                        }}
                      >
                        {isProductInCart ? "Remove from Cart" : ctaText}
                      </button>
                    </div>
                  </div>

                  <div className="gc-product-popup-details">
                    <div className="gc-estimated-delivery gc-align-center">
                      <IconCalendar />
                      <span>Estimated Delivery&nbsp;</span>
                      {getEstimatedDeliveryDate()}{" "}
                    </div>
                    <div className="gc-product-view inited gc-align-center">
                      <IconSmiley />
                      <span>
                        <span className="gc-view-count">
                          {generateRandomNumber()}
                        </span>{" "}
                        people are viewing this right now
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default QuickLookMolecule;
