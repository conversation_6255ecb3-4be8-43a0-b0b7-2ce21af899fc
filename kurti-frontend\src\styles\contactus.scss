.contact-page {
    padding-top: 100px;
    .address {
        @media(min-width:1200px) {
            max-width: 280px;
        }
        h3 {
            color: var(--gc-dark);
            font-size: 25px;
            line-height: 1.3;
            margin-bottom: 24px;
        }
        p {
            line-height: 1.6;
            color: var(--e-global-color-text);
            margin-bottom: 6px;
            strong {
                font-weight: 500;
                color: var(--gc-dark);
            }
            a {
                color: var(--gc-dark);
            }
        }
    }
    .contact-form {
        padding-top: 40px;
        h3 {
            color: var(--gc-dark);
            font-size: 25px;
            line-height: 1.3;
            margin-bottom: 24px;
        }
        input:not([type="checkbox"]):not([type="radio"]):not([type="button"]),
        select,
        textarea {
            padding: 12px 10px;
            min-height: 45px;
            border: 2px solid var(--gc-border);
            max-width: 100%;
            width: 100%;
            margin-bottom: 10px;
            border-radius: 0;
            &:focus{
                outline: none;
                box-shadow: none;
            }
        }
    }
}